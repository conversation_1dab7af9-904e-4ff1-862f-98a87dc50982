# Sec Flow - 安全流程编排平台

基于 LogicFlow + React + Ant Design + Vite + Golang 构建的企业级流程编排平台，支持可视化流程设计、多种触发方式、实时执行和调试功能。

## ✨ 核心特性

- 🎨 **可视化流程设计** - 基于LogicFlow的拖拽式流程编辑器
- 🚀 **多种触发方式** - 支持HTTP触发、定时触发等多种启动方式
- 🔧 **预置节点库** - 丰富的预置节点，支持用户信息补充、API调用、条件判断等
- 📊 **实时执行监控** - 流程执行状态实时监控和日志查看
- 🛠️ **调试工具** - 内置print节点和预览功能，方便调试流程
- 📱 **响应式设计** - 适配不同屏幕尺寸的现代化界面

## 🏗️ 技术架构

### 前端技术栈
- **React 19** + TypeScript - 现代化前端框架
- **Vite 7** - 快速构建工具
- **Ant Design 5** - 企业级UI组件库
- **LogicFlow 2.0** - 流程图编辑引擎
- **Node.js 20** - 运行环境

### 后端技术栈
- **Golang 1.21+** - 高性能后端语言
- **Gin** - 轻量级Web框架
- **GORM** - ORM数据库操作
- **MySQL** - 关系型数据库
- **Cron** - 定时任务调度

## 📁 项目结构

```
sec-flow/
├── web/                           # 前端项目
│   ├── src/
│   │   ├── pages/                 # 页面组件
│   │   │   ├── FlowList.tsx       # 流程列表页
│   │   │   └── FlowEditor.tsx     # 流程编辑器页
│   │   ├── components/            # 通用组件
│   │   ├── App.tsx                # 主应用
│   │   └── main.tsx               # 应用入口
│   ├── package.json               # 前端依赖
│   └── vite.config.ts             # Vite配置
├── server/                        # 后端项目
│   ├── main.go                    # 服务入口
│   ├── internal/
│   │   ├── constants/             # 常量定义
│   │   ├── model/                 # 数据模型
│   │   ├── service/               # 业务服务
│   │   ├── handler/               # HTTP处理器
│   │   ├── database/              # 数据库配置
│   │   ├── scheduler/             # 定时调度器
│   │   └── node/                  # 预置节点
│   │       ├── base/              # 节点基础接口
│   │       ├── cron_start/        # 定时开始节点
│   │       ├── http_start/        # HTTP开始节点
│   │       ├── user_info/         # 用户信息节点
│   │       ├── api_call/          # API调用节点
│   │       ├── condition/         # 条件判断节点
│   │       ├── data_transform/    # 数据转换节点
│   │       └── print/             # 日志打印节点
│   ├── go.mod                     # Go模块
│   └── Makefile                   # 构建脚本
└── README.md                      # 项目文档
```

## 🎯 功能特性

### 🎨 可视化流程设计
- **拖拽式编辑器** - 基于LogicFlow的直观流程设计界面
- **预置节点库** - 丰富的预置节点，开箱即用
- **实时预览** - 流程数据流转预览，支持变动字段高亮
- **参数配置** - 节点参数可视化配置，支持表单验证

### 🚀 多种触发方式
- **HTTP触发** - 通过REST API触发流程执行
- **定时触发** - 基于Cron表达式的定时执行
- **手动执行** - 支持参数输入的手动测试执行

### 🔧 预置节点类型
- **触发节点**
  - `cron_start` - 定时开始节点
  - `http_start` - HTTP开始节点
- **数据处理节点**
  - `user_info` - 用户信息补充
  - `data_transform` - 数据转换
  - `api_call` - API调用
- **控制节点**
  - `condition` - 条件判断
- **调试节点**
  - `print` - 日志打印

### 📊 执行监控
- **实时状态** - 流程执行状态实时监控
- **执行日志** - 详细的执行日志和错误信息
- **数据追踪** - 流程数据在各节点间的传递追踪

### 🛠️ 开发调试
- **参数输入** - 执行时支持动态参数输入
- **数据预览** - 流程数据变化预览
- **日志打印** - 内置print节点用于调试

## 🚀 快速开始

### 环境要求

- **前端**: Node.js 20+ (推荐使用 nvm 管理版本)
- **后端**: Go 1.21+
- **数据库**: MySQL 8.0+
- **工具**: npm, Git

### 数据库配置

```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE sec_flow CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 启动后端服务

```bash
# 进入后端项目目录
cd server

# 安装依赖
go mod download

# 启动服务器（会自动创建数据表）
go run main.go
```

后端服务启动后会：
- 自动连接数据库并创建表结构
- 初始化示例数据
- 启动HTTP触发器服务
- 启动定时调度器
- 监听端口 8080

### 启动前端服务

```bash
# 切换到Node.js 20
nvm use 20

# 进入前端项目目录
cd web

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端服务启动后访问 http://localhost:5173

### 服务验证

- **前端**: http://localhost:5173
- **后端健康检查**: http://localhost:8080/health
- **API文档**: http://localhost:8080/api/v1

## 📖 使用指南

### 流程设计

1. **创建流程**
   - 点击"新建流程"创建空白流程
   - 填写流程名称、执行时机和描述

2. **添加节点**
   - 点击"预置节点"选择需要的节点类型
   - 拖拽节点到画布上合适位置
   - 配置节点参数

3. **连接节点**
   - 拖拽节点连接点创建连线
   - 建立流程执行顺序

4. **保存发布**
   - 保存流程为草稿状态
   - 发布流程使其可被触发执行

### 流程执行

#### HTTP触发执行
```bash
# 触发HTTP开始节点的流程
curl -X POST http://localhost:8080/api/execute/flow/{key} \
  -H "Content-Type: application/json" \
  -d '{"user_id": "123", "name": "张三"}'
```

#### 手动执行
- 在流程编辑器中点击"执行"按钮
- 根据起始节点类型输入相应参数：
  - HTTP Start: 输入JSON格式的请求数据
  - Cron Start: 选择执行时间点

#### 定时执行
- 系统每分钟自动检查定时流程
- 匹配Cron表达式的流程会自动执行

### 调试功能

1. **预览功能**
   - 点击"预览"查看流程数据流转
   - 查看各节点的数据变化

2. **Print节点**
   - 在流程中添加print节点
   - 执行时在后端日志中查看数据

3. **执行日志**
   - 查看后端控制台的详细执行日志
   - 监控流程执行状态和错误信息

## 📋 API 接口

### 核心接口

#### 流程管理
- `GET /api/v1/flows` - 获取流程列表
- `POST /api/v1/flows` - 创建流程
- `GET /api/v1/flows/:id` - 获取流程详情
- `PUT /api/v1/flows/:id` - 更新流程
- `DELETE /api/v1/flows/:id` - 删除流程
- `POST /api/v1/flows/:id/execute` - 手动执行流程

#### 预置节点
- `GET /api/v1/preset-nodes` - 获取预置节点列表
- `GET /api/v1/preset-nodes/:id` - 获取节点详情
- `POST /api/v1/preset-nodes/:id/example` - 获取节点示例数据

#### HTTP触发器
- `GET /api/v1/http-triggers` - 获取HTTP触发器列表
- `POST /api/v1/http-triggers/refresh` - 刷新触发器映射
- `POST /api/execute/flow/:key` - 触发HTTP流程执行

#### 系统接口
- `GET /health` - 健康检查

### 流程状态

流程支持以下状态：
- `draft` - 草稿（默认状态）
- `published` - 已发布（可被触发执行）
- `archived` - 已归档
- `deleted` - 已删除（软删除）

### 响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 响应数据
  }
}
```

### 错误处理

```json
{
  "code": 400,
  "message": "参数错误",
  "data": null
}
```

## 🔧 开发指南

### 添加新的预置节点

1. **创建节点目录**
```bash
mkdir server/internal/node/your_node
```

2. **实现节点配置** (`config.go`)
```go
func GetNodeConfig() *base.NodeConfig {
    return &base.NodeConfig{
        ID:          "your_node",
        Name:        "节点名称",
        Category:    "节点分类",
        Description: "节点描述",
        FormFields:  []base.FormField{
            // 配置表单字段
        },
    }
}
```

3. **实现节点执行器** (`executor.go`)
```go
func (n *YourNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
    // 实现节点逻辑
    return &base.ExecutionOutput{
        Success:  true,
        Continue: true,
        FlowData: updatedData,
        Message:  "执行成功",
    }, nil
}
```

4. **注册节点** (在 `internal/node/init.go` 中)
```go
yourNode := your_node.NewYourNode()
registry.Register(yourNode)
```

### 数据结构设计

#### FlowData 结构
```json
{
  "user_id": "123",           // 业务数据
  "name": "张三",
  "executeInfo": {            // 执行信息（独立存储）
    "httpTrigger": {...},
    "cronTrigger": {...}
  }
}
```

#### 节点配置存储
- 前端：存储在 `properties.formData` 中
- 后端：从 `formData` 或 `config` 中读取（向后兼容）

### 调试技巧

1. **使用Print节点**
   - 在关键位置添加print节点
   - 查看后端日志输出

2. **预览功能**
   - 使用预览功能查看数据流转
   - 观察变动字段高亮

3. **日志监控**
   - 后端详细日志记录执行过程
   - 前端控制台输出调试信息

## 🔗 相关链接

- [LogicFlow 官方文档](https://site.logic-flow.cn/)
- [Ant Design 官方文档](https://ant.design/)
- [Gin 框架文档](https://gin-gonic.com/)
- [GORM 文档](https://gorm.io/)

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

### 贡献指南
1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

## 📞 支持

如有问题，请通过以下方式联系：
- 提交 GitHub Issue
- 查看项目文档
- 参考示例代码
