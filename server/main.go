package main

import (
	"log"
	"os"
	"os/signal"
	"sec-flow-server/internal"
	"sec-flow-server/internal/config"
	"sec-flow-server/internal/database"
	"sec-flow-server/internal/flow"
	"sec-flow-server/internal/middleware"
	"sec-flow-server/internal/node"
	"sec-flow-server/internal/scheduler"
	"sec-flow-server/internal/service"
	thirdParty "sec-flow-server/internal/service/third_party"
	"syscall"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("❌ Failed to load config:", err)
	}

	// 设置Gin模式
	if cfg.App.Debug {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 初始化数据库
	err = database.InitDatabase()
	if err != nil {
		log.Fatal("❌ Failed to initialize database:", err)
	}

	// 初始化RBAC系统
	rbacInitService := service.NewRBACInitService()
	err = rbacInitService.CheckAndInitRBAC()
	if err != nil {
		log.Fatal("❌ Failed to initialize RBAC:", err)
	}

	err = thirdParty.InitThirdParty()
	if err != nil {
		log.Fatal("❌ Failed to initialize thirdParty:", err)
	}

	// 初始化节点
	node.InitNodes()

	// 初始化服务
	flowService := service.NewFlowService()
	flowExecutor := flow.NewFlowExecutor(flowService)
	httpTriggerService := flow.NewHttpTriggerService(flowService, flowExecutor)
	lockService := service.NewDistributedLockService()

	// 初始化并启动调度器
	flowScheduler := scheduler.NewFlowScheduler(flowService, flowExecutor, httpTriggerService, lockService)
	flowScheduler.Start()

	// 初始化并启动清理调度器
	cleanupScheduler := scheduler.NewCleanupScheduler()
	cleanupScheduler.Start()

	// 创建Gin引擎
	r := gin.Default()

	// 配置CORS中间件
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowOrigins = cfg.CORS.AllowOrigins
	corsConfig.AllowMethods = cfg.CORS.AllowMethods
	corsConfig.AllowHeaders = cfg.CORS.AllowHeaders
	corsConfig.AllowCredentials = cfg.CORS.AllowCredentials
	r.Use(cors.New(corsConfig))

	// 添加日志中间件
	r.Use(middleware.Logger())

	// 添加错误处理中间件
	r.Use(middleware.ErrorHandler())

	// 创建认证和权限中间件
	authMiddleware := middleware.NewAuthMiddleware(cfg.JWT.Secret)
	permissionMiddleware := middleware.NewPermissionMiddleware()

	// 设置路由
	internal.SetupRoutes(r, authMiddleware, permissionMiddleware, flowScheduler, cleanupScheduler, httpTriggerService, lockService)

	// 启动服务器
	serverAddr := cfg.GetServerAddr()
	log.Printf("🚀 %s starting on %s", cfg.App.Name, serverAddr)
	log.Printf("📊 Health check: http://localhost:%d/health", cfg.Server.Port)
	log.Printf("📖 API docs: http://localhost:%d/api/v1", cfg.Server.Port)
	log.Printf("🌍 Environment: %s", cfg.App.Env)

	// 设置信号处理，用于优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// 在单独的 goroutine 中启动服务器
	go func() {
		if err := r.Run(serverAddr); err != nil {
			log.Fatal("❌ Failed to start server:", err)
		}
	}()

	// 等待关闭信号
	<-quit
	log.Println("🛑 正在关闭服务器...")

	// 停止调度器
	flowScheduler.Stop()
	cleanupScheduler.Stop()

	log.Println("✅ 服务器已关闭")
}
