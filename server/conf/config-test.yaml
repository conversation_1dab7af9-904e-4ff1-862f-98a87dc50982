# 测试环境配置
app:
  name: "Sec Flow Server"
  version: "1.0.0"
  env: "test"
  debug: false

server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 60s
  write_timeout: 60s
  idle_timeout: 60s

database:
  driver: "mysql"
  host: "test-mysql.internal"
  port: 3306
  username: "sec_flow_user"
  password: "test_password_456"
  database: "sec_flow_test"
  charset: "utf8mb4"
  parse_time: true
  loc: "Local"
  max_idle_conns: 15
  max_open_conns: 150
  conn_max_lifetime: 3600s



log:
  level: "info"
  format: "json"
  output: "file"
  file_path: "/var/log/sec-flow/app.log"
  max_size: 100
  max_backups: 7
  max_age: 30
  compress: true

scheduler:
  heartbeat_interval: 30s
  heartbeat_timeout: 90s
  active_instance_timeout: 60s
  preprocess_lock_timeout: 90s
  task_assignment_timeout: 180s

cors:
  allow_origins:
    - "https://test-frontend.example.com"
  allow_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allow_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
  allow_credentials: true
