# 开发环境配置
app:
  name: "Sec Flow Server"
  version: "1.0.0"
  env: "dev"
  debug: true

server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 60s
  write_timeout: 60s
  idle_timeout: 60s

database:
  driver: "mysql"
  host: "dev-mysql.internal"
  port: 3306
  username: "sec_flow_user"
  password: "dev_password_123"
  database: "sec_flow_dev"
  charset: "utf8mb4"
  parse_time: true
  loc: "Local"
  max_idle_conns: 20
  max_open_conns: 200
  conn_max_lifetime: 3600s
  log_level: "error"
  auto_migrate: true  # 开发环境启用自动迁移



log:
  level: "info"
  format: "json"
  output: "file"
  file_path: "/var/log/sec-flow/app.log"
  max_size: 100
  max_backups: 7
  max_age: 30
  compress: true

scheduler:
  heartbeat_interval: 30s
  heartbeat_timeout: 90s
  active_instance_timeout: 60s
  preprocess_lock_timeout: 90s
  task_assignment_timeout: 180s

cors:
  allow_origins:
    - "https://dev-frontend.example.com"
    - "http://localhost:3000"
  allow_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allow_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
  allow_credentials: true

jwt:
  secret: "ENC(aTMJlNi3ZXuy4A9D1cbo9Eh+kTEDuQRP1ZFsq81IO5F2bZEPEUVKRgCRuVJCOyrEhWFEZ6ZxCOTNBeUl)"

third:
  lark:
    app_id: "cli_9f8fe851c0b4d00e"
    app_secret: "ENC(zYOFOvKtSLeIjp/BfrjB14C9jc+6BUDq3/OQHDMgQZA29eRaIwPtL/qSOcfEe51MiodIcxts+sPSXr1y)"
  mongo:
    address: "dds-2zeb56a5379411a42.mongodb.rds.aliyuncs.com:3717,dds-2zeb56a5379411a41.mongodb.rds.aliyuncs.com:3717"
    username: "root"
    password: "ENC(L/edakrcO0jYtK38et9OezjJm7MCM4Ylg+cQ8vs6b7iSGd5huGYyJkvG3UMUYWSZ)"
  aliyun:
    access_key: "ENC(F+K11cvVceUU6ktnhmoq8XP445PWnQEESlJMGjUTvjX5kL2s4BBQTKGzvnVCfX7JaD9jhubBPiPyqA==)"
    access_key_id: "ENC(wbJpcnLV7TlkXllr9v+iGXIhVj2utt0qAVtSTyakNSPxWcVPzplA0eTpgEaRFVdhqtwp0A==)"


databases:
  - host: "polar-public.mysql.polardb.rds.aliyuncs.com"
    username: "u_security_rd"
    password: "ENC(3Hez/oakh5905V99n/k9rh5fzwd/ef+PEnxSkQPrYPfhd50XprdBw7uG6KqucKPQ)"
    database: "db_groupbuy"
