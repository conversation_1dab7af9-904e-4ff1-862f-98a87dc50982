# 本地开发环境配置
app:
  name: "Sec Flow Server"
  version: "1.0.0"
  env: "local"
  debug: true

server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 60s
  write_timeout: 60s
  idle_timeout: 60s

database:
  driver: "mysql"
  host: "localhost"
  port: 3306
  username: "root"
  password: "ENC(Iy8UnePg/L7o3P+xUO/4X09zFUXSkNb/dfQlavNJ+UMD2yak)"
  database: "sec_flow_prod"
  charset: "utf8mb4"
  parse_time: true
  loc: "Local"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600s
  log_level: "error"  # 专门的数据库日志级别：silent, error, warn, info
  auto_migrate: false  # 开发环境启用自动迁移



log:
  level: "debug"  # 应用日志保持debug级别
  format: "text"
  output: "stdout"
  file_path: "logs/app.log"
  max_size: 100
  max_backups: 7
  max_age: 30
  compress: true

scheduler:
  heartbeat_interval: 10s
  heartbeat_timeout: 15s
  active_instance_timeout: 30s
  preprocess_lock_timeout: 45s
  task_assignment_timeout: 120s

cors:
  allow_origins:
    - "http://localhost:3000"
    - "http://127.0.0.1:3000"
    - "http://localhost:5173"
    - "http://localhost:5174"
  allow_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allow_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
  allow_credentials: true

jwt:
  secret: "ENC(tL+AfxPmYq4nZeBup6C+f1dfqPmYf2+Hi9aBPF16729vso3M56DEM6kxN+zQ1E7GKR26NAlm4OLz1UxI)"

other:
  aes_key: "ENC(MceWHeTm33J9BYXfBc7VQm7ZtmxOZSVsEq9vbgfjkMrylSxb7WlFqk51+HGgo3hVBQiBttUkKzWkOZHa)"

third:
  lark:
    app_id: "cli_9f8fe851c0b4d00e"
    app_secret: "ENC(zYOFOvKtSLeIjp/BfrjB14C9jc+6BUDq3/OQHDMgQZA29eRaIwPtL/qSOcfEe51MiodIcxts+sPSXr1y)"
  mongo:
    address: "dds-2zeb56a5379411a42.mongodb.rds.aliyuncs.com:3717,dds-2zeb56a5379411a41.mongodb.rds.aliyuncs.com:3717"
    username: "root"
    password: "ENC(L/edakrcO0jYtK38et9OezjJm7MCM4Ylg+cQ8vs6b7iSGd5huGYyJkvG3UMUYWSZ)"
  aliyun:
    access_key: "ENC(F+K11cvVceUU6ktnhmoq8XP445PWnQEESlJMGjUTvjX5kL2s4BBQTKGzvnVCfX7JaD9jhubBPiPyqA==)"
    access_key_id: "ENC(wbJpcnLV7TlkXllr9v+iGXIhVj2utt0qAVtSTyakNSPxWcVPzplA0eTpgEaRFVdhqtwp0A==)"
  yongan:
    snkey: "ENC(5+vgCLybPpQkpnrAteV0fwpklk1p9d8qSMPcALcWze6eupiqovWhBv/wboU7+a0g+OPEaJvSddU+DpCz)"
    snuser: "LZe5mDdbvxoQfqlk"


databases:
  - host: "polar-public.mysql.polardb.rds.aliyuncs.com"
    username: "u_security_rd"
    password: "ENC(3Hez/oakh5905V99n/k9rh5fzwd/ef+PEnxSkQPrYPfhd50XprdBw7uG6KqucKPQ)"
    database: "db_groupbuy"
  - host: "rm-2zeg68m78e01k7v50.mysql.rds.aliyuncs.com"
    username: "u_security_rw"
    password: "ENC(wUNw5Egc+Trp04ApHt39G4bv4FKqLay3bOZ7K7GYlemn8L8Ul+ygYpi7lb0xM0H1)"
    database: "db_security"
