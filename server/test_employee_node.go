package main

import (
	"encoding/json"
	"fmt"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/node/business/employee_resigned_scan"
)

func main() {
	// 创建节点
	node := employee_resigned_scan.NewEmployeeResignedScanNode()

	// 测试节点配置
	config := node.GetConfig()
	fmt.Printf("节点ID: %s\n", config.ID)
	fmt.Printf("节点名称: %s\n", config.Name)
	fmt.Printf("节点描述: %s\n", config.Description)
	fmt.Printf("节点分类: %s\n", config.Category)

	// 打印表单字段
	fmt.Println("\n表单字段:")
	for _, field := range config.FormFields {
		fmt.Printf("- %s (%s): %s\n", field.Label, field.Key, field.Type)
		if len(field.Options) > 0 {
			fmt.Printf("  选项: ")
			for _, option := range field.Options {
				fmt.Printf("%s=%s ", option.Label, option.Value)
			}
			fmt.Println()
		}
	}

	// 测试Example方法
	fmt.Println("\n测试Example方法:")
	exampleInput := &base.ExampleInput{
		Config: map[string]interface{}{
			"executeDayField": "executeInfo.thisDay",
			"flushStatus":     "false",
			"outputField":     "resignedEmployees",
		},
		FlowData: map[string]interface{}{
			"executeInfo": map[string]interface{}{
				"thisDay": "2024-01-15",
			},
		},
	}

	exampleOutput := node.Example(exampleInput)
	fmt.Printf("示例描述: %s\n", exampleOutput.Description)
	fmt.Printf("数据变化数量: %d\n", len(exampleOutput.Changes))

	// 打印示例输出数据
	if resignedEmployees, ok := exampleOutput.FlowData["resignedEmployees"].([]map[string]interface{}); ok {
		fmt.Printf("示例离职员工数量: %d\n", len(resignedEmployees))
		if len(resignedEmployees) > 0 {
			// 打印第一个员工的信息
			firstEmployee := resignedEmployees[0]
			employeeJSON, _ := json.MarshalIndent(firstEmployee, "", "  ")
			fmt.Printf("第一个员工信息:\n%s\n", string(employeeJSON))
		}
	}

	// 测试参数验证
	fmt.Println("\n测试参数验证:")
	
	// 测试有效参数
	validParams := map[string]interface{}{
		"executeDayField": "executeInfo.thisDay",
		"flushStatus":     "true",
		"outputField":     "result",
	}
	if err := node.Validate(validParams); err != nil {
		fmt.Printf("有效参数验证失败: %v\n", err)
	} else {
		fmt.Println("有效参数验证通过")
	}

	// 测试无效参数
	invalidParams := map[string]interface{}{
		"executeDayField": "",
		"flushStatus":     "invalid",
		"outputField":     "result",
	}
	if err := node.Validate(invalidParams); err != nil {
		fmt.Printf("无效参数验证失败（预期）: %v\n", err)
	} else {
		fmt.Println("无效参数验证意外通过")
	}

	fmt.Println("\n节点测试完成！")
}
