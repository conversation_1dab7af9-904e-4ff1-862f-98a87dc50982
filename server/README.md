# Sec Flow Server

基于 Gin 框架的 Sec Flow 后端服务，为 LogicFlow 流程图编辑器提供 API 支持。

## 🚀 功能特性

### 核心功能
- **流程图管理**: 创建、编辑、删除、查询流程图
- **模板系统**: 节点模板的管理和使用
- **流程执行**: 流程图的执行引擎（模拟）
- **用户管理**: 用户信息管理

### 技术特性
- **RESTful API**: 标准的 REST 接口设计
- **CORS 支持**: 跨域请求支持
- **错误处理**: 统一的错误处理机制
- **日志记录**: 请求日志和错误日志
- **分页查询**: 支持分页的数据查询

## 📁 项目结构

```
server/
├── main.go                 # 应用入口
├── Makefile               # 构建脚本
├── go.mod                 # Go模块文件
├── go.sum                 # 依赖校验文件
├── conf/                  # 配置文件目录
│   ├── config-local.yaml  # 本地环境配置
│   ├── config-dev.yaml    # 开发环境配置
│   ├── config-test.yaml   # 测试环境配置
│   ├── config-pre.yaml    # 预发布环境配置
│   └── config-prod.yaml   # 生产环境配置
├── internal/              # 内部代码
│   ├── config/            # 配置管理
│   │   └── config.go      # 配置加载和管理
│   ├── handler/           # HTTP处理器
│   │   ├── flow.go
│   │   ├── template.go
│   │   ├── preset_node.go
│   │   ├── distributed_lock.go
│   │   └── http_trigger.go
│   ├── service/           # 业务逻辑层
│   │   ├── flow.go
│   │   ├── template.go
│   │   ├── flow_executor.go
│   │   ├── http_trigger.go
│   │   └── distributed_lock.go
│   ├── model/             # 数据模型
│   │   ├── flow.go
│   │   ├── template.go
│   │   ├── user.go
│   │   └── distributed_lock.go
│   ├── database/          # 数据库管理
│   │   └── database.go
│   ├── scheduler/         # 分布式调度器
│   │   └── scheduler.go
│   ├── node/              # 预置节点
│   │   ├── base/          # 节点基础接口
│   │   ├── registry.go    # 节点注册中心
│   │   ├── cron_start/    # 定时开始节点
│   │   ├── http_start/    # HTTP开始节点
│   │   ├── user_info/     # 用户信息节点
│   │   ├── api_call/      # API调用节点
│   │   ├── condition/     # 条件判断节点
│   │   ├── data_transform/ # 数据转换节点
│   │   └── print/         # 日志打印节点
│   ├── constants/         # 常量定义
│   │   └── constants.go
│   └── middleware/        # 中间件
│       ├── logger.go
│       └── error.go
└── pkg/                   # 公共包
    └── response/          # 响应工具
        └── response.go
```

## 🛠️ 技术栈

- **Go 1.21+**: 编程语言
- **Gin**: Web框架
- **GORM**: ORM数据库操作
- **MySQL**: 关系型数据库
- **gin-contrib/cors**: CORS中间件
- **robfig/cron**: 定时任务调度
- **google/uuid**: UUID生成
- **gopkg.in/yaml.v3**: YAML配置解析

## 📋 API 接口

### 健康检查
- `GET /health` - 服务健康检查

### 流程图管理
- `GET /api/v1/flows` - 获取流程图列表
- `POST /api/v1/flows` - 创建流程图
- `GET /api/v1/flows/:id` - 获取单个流程图
- `PUT /api/v1/flows/:id` - 更新流程图
- `DELETE /api/v1/flows/:id` - 删除流程图
- `POST /api/v1/flows/:id/execute` - 执行流程图

### 模板管理
- `GET /api/v1/templates` - 获取模板列表
- `POST /api/v1/templates` - 创建模板
- `GET /api/v1/templates/:id` - 获取单个模板
- `PUT /api/v1/templates/:id` - 更新模板
- `DELETE /api/v1/templates/:id` - 删除模板

### 用户管理
- `GET /api/v1/users/profile` - 获取用户信息
- `PUT /api/v1/users/profile` - 更新用户信息

## 🚀 快速开始

### 环境要求
- Go 1.21 或更高版本
- Git

### 安装依赖
```bash
# 进入server目录
cd server

# 安装依赖
make deps
# 或者
go mod download
```

### 运行服务
```bash
# 开发模式运行
make run
# 或者
go run main.go

# 生产模式运行
make build
./build/sec-flow-server
```

### 开发模式（热重载）
```bash
# 安装并运行热重载
make dev
```

## 🔧 配置

服务支持通过环境变量进行配置：

```bash
# 服务器配置
SERVER_PORT=8080
GIN_MODE=release

# 数据库配置（预留）
DB_DRIVER=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=
DB_DATABASE=sec_flow

# Redis配置（预留）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0

# JWT配置（预留）
JWT_SECRET=sec-flow-secret-key
JWT_EXPIRE_TIME=86400
```

## 📊 API 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 响应数据
  }
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "page": 1,
    "pageSize": 10,
    "total": 100,
    "data": [
      // 数据列表
    ]
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误信息"
}
```

## 🧪 测试

```bash
# 运行测试
make test

# 运行测试并生成覆盖率报告
make test-coverage
```

## 📚 开发指南

### 添加新的API接口

1. 在 `internal/model/` 中定义数据模型
2. 在 `internal/service/` 中实现业务逻辑
3. 在 `internal/handler/` 中实现HTTP处理器
4. 在 `main.go` 中注册路由

### 中间件开发

在 `internal/middleware/` 目录下创建新的中间件文件，然后在 `main.go` 中注册使用。

### 配置管理

配置文件位于 `conf/` 目录下，支持多环境配置：
- 通过 `APP_ENV` 环境变量指定环境（local/dev/test/pre/prod）
- 支持环境变量覆盖配置文件中的值
- 默认使用 `local` 环境配置

## 🐳 Docker 支持

```bash
# 构建Docker镜像
make docker-build

# 运行Docker容器
make docker-run
```

## 📝 待办事项

- [x] 数据库集成（MySQL）
- [x] 配置文件支持（多环境YAML配置）
- [x] 分布式调度系统
- [x] 预置节点系统
- [ ] JWT 认证系统
- [ ] API 文档生成（Swagger）
- [ ] 单元测试完善
- [ ] 日志系统优化
- [ ] Docker Compose 配置
- [ ] CI/CD 流水线

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
