package constants

// FlowStatus 流程状态常量
type FlowStatus string

const (
	// FlowStatusDraft 草稿状态
	FlowStatusDraft FlowStatus = "draft"
	
	// FlowStatusPublished 已发布状态
	FlowStatusPublished FlowStatus = "published"
	
	// FlowStatusArchived 已归档状态
	FlowStatusArchived FlowStatus = "archived"
	
	// FlowStatusDeleted 已删除状态（软删除）
	FlowStatusDeleted FlowStatus = "deleted"
)

// String 返回状态的字符串表示
func (s FlowStatus) String() string {
	return string(s)
}

// IsValid 检查状态是否有效
func (s FlowStatus) IsValid() bool {
	switch s {
	case FlowStatusDraft, FlowStatusPublished, FlowStatusArchived, FlowStatusDeleted:
		return true
	default:
		return false
	}
}

// GetAllFlowStatuses 获取所有有效的流程状态
func GetAllFlowStatuses() []FlowStatus {
	return []FlowStatus{
		FlowStatusDraft,
		FlowStatusPublished,
		FlowStatusArchived,
		FlowStatusDeleted,
	}
}

// GetDisplayName 获取状态的显示名称（中文）
func (s FlowStatus) GetDisplayName() string {
	switch s {
	case FlowStatusDraft:
		return "草稿"
	case FlowStatusPublished:
		return "已发布"
	case FlowStatusArchived:
		return "已归档"
	case FlowStatusDeleted:
		return "已删除"
	default:
		return "未知状态"
	}
}

// TriggerType 触发类型常量
type TriggerType string

const (
	// TriggerTypeManual 手动触发
	TriggerTypeManual TriggerType = "manual"

	// TriggerTypeCron 定时触发
	TriggerTypeCron TriggerType = "cron"

	// TriggerTypeHTTP HTTP触发
	TriggerTypeHTTP TriggerType = "http"
)

// String 返回触发类型的字符串表示
func (t TriggerType) String() string {
	return string(t)
}

// GetDisplayName 获取触发类型的显示名称（中文）
func (t TriggerType) GetDisplayName() string {
	switch t {
	case TriggerTypeManual:
		return "手动触发"
	case TriggerTypeCron:
		return "定时触发"
	case TriggerTypeHTTP:
		return "HTTP触发"
	default:
		return "未知类型"
	}
}

// ExecutionStatus 执行状态常量
type ExecutionStatus string

const (
	// ExecutionStatusRunning 执行中
	ExecutionStatusRunning ExecutionStatus = "running"

	// ExecutionStatusCompleted 执行完成
	ExecutionStatusCompleted ExecutionStatus = "completed"

	// ExecutionStatusFailed 执行失败
	ExecutionStatusFailed ExecutionStatus = "failed"

	// ExecutionStatusCancelled 执行取消
	ExecutionStatusCancelled ExecutionStatus = "cancelled"
)

// String 返回执行状态的字符串表示
func (s ExecutionStatus) String() string {
	return string(s)
}

// GetDisplayName 获取执行状态的显示名称（中文）
func (s ExecutionStatus) GetDisplayName() string {
	switch s {
	case ExecutionStatusRunning:
		return "执行中"
	case ExecutionStatusCompleted:
		return "执行完成"
	case ExecutionStatusFailed:
		return "执行失败"
	case ExecutionStatusCancelled:
		return "执行取消"
	default:
		return "未知状态"
	}
}
