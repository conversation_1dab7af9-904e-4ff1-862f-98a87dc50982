package constants

import "sec-flow-server/internal/node/base"

// 操作符常量定义
const (
	// 基础比较操作符
	OpEquals    = "equals"
	OpNotEquals = "not_equals"
	OpGreater   = "greater_than"
	OpLess      = "less_than"
	OpGreaterEq = "greater_equal"
	OpLessEq    = "less_equal"

	// 包含操作符
	OpContains    = "contains"
	OpNotContains = "not_contains"

	// 空值操作符
	OpIsEmpty    = "is_empty"
	OpIsNotEmpty = "is_not_empty"

	// 存在性操作符
	OpExists    = "exists"
	OpNotExists = "not_exists"

	// 长度比较操作符
	OpLenEquals    = "len_equals"
	OpLenNotEquals = "len_not_equals"
	OpLenGreater   = "len_greater_than"
	OpLenLess      = "len_less_than"
	OpLenGreaterEq = "len_greater_equal"
	OpLenLessEq    = "len_less_equal"

	// 版本号比较操作符
	OpVerEquals      = "ver_equals"
	OpVerNotEquals   = "ver_not_equals"
	OpVerGreater     = "ver_greater_than"
	OpVerLess        = "ver_less_than"
	OpVerGreaterEq   = "ver_greater_equal"
	OpVerLessEq      = "ver_less_equal"
	OpVerContains    = "ver_contains"     // 小版本包含于大版本
	OpVerContainedIn = "ver_contained_in" // 大版本包含小版本

	// IP地址判断操作符
	OpIsPrivateIP = "is_private_ip" // IP为内网
	OpIsPublicIP  = "is_public_ip"  // IP为外网
)

// GetAllOperators 获取所有操作符选项
func GetAllOperators() []base.FormFieldOption {
	return []base.FormFieldOption{
		// 基础比较
		{Label: "等于", Value: OpEquals},
		{Label: "不等于", Value: OpNotEquals},
		{Label: "大于", Value: OpGreater},
		{Label: "小于", Value: OpLess},
		{Label: "大于等于", Value: OpGreaterEq},
		{Label: "小于等于", Value: OpLessEq},

		// 包含
		{Label: "包含", Value: OpContains},
		{Label: "不包含", Value: OpNotContains},

		// 空值
		{Label: "为空", Value: OpIsEmpty},
		{Label: "不为空", Value: OpIsNotEmpty},

		// 存在性
		{Label: "字段存在", Value: OpExists},
		{Label: "字段不存在", Value: OpNotExists},

		// 长度比较
		{Label: "长度等于", Value: OpLenEquals},
		{Label: "长度不等于", Value: OpLenNotEquals},
		{Label: "长度大于", Value: OpLenGreater},
		{Label: "长度小于", Value: OpLenLess},
		{Label: "长度大于等于", Value: OpLenGreaterEq},
		{Label: "长度小于等于", Value: OpLenLessEq},

		// 版本号比较
		{Label: "版本等于", Value: OpVerEquals},
		{Label: "版本不等于", Value: OpVerNotEquals},
		{Label: "版本大于", Value: OpVerGreater},
		{Label: "版本小于", Value: OpVerLess},
		{Label: "版本大于等于", Value: OpVerGreaterEq},
		{Label: "版本小于等于", Value: OpVerLessEq},
		{Label: "版本包含于", Value: OpVerContains},
		{Label: "版本被包含", Value: OpVerContainedIn},

		// IP地址判断
		{Label: "IP为内网", Value: OpIsPrivateIP},
		{Label: "IP为外网", Value: OpIsPublicIP},
	}
}

// GetBasicOperators 获取基础操作符（用于简单场景）
func GetBasicOperators() []base.FormFieldOption {
	return []base.FormFieldOption{
		{Label: "等于", Value: OpEquals},
		{Label: "不等于", Value: OpNotEquals},
		{Label: "大于", Value: OpGreater},
		{Label: "小于", Value: OpLess},
		{Label: "大于等于", Value: OpGreaterEq},
		{Label: "小于等于", Value: OpLessEq},
		{Label: "包含", Value: OpContains},
		{Label: "不包含", Value: OpNotContains},
		{Label: "为空", Value: OpIsEmpty},
		{Label: "不为空", Value: OpIsNotEmpty},
	}
}

// IsValidOperator 检查操作符是否有效
func IsValidOperator(operator string) bool {
	validOps := map[string]bool{
		OpEquals:         true,
		OpNotEquals:      true,
		OpGreater:        true,
		OpLess:           true,
		OpGreaterEq:      true,
		OpLessEq:         true,
		OpContains:       true,
		OpNotContains:    true,
		OpIsEmpty:        true,
		OpIsNotEmpty:     true,
		OpExists:         true,
		OpNotExists:      true,
		OpLenEquals:      true,
		OpLenNotEquals:   true,
		OpLenGreater:     true,
		OpLenLess:        true,
		OpLenGreaterEq:   true,
		OpLenLessEq:      true,
		OpVerEquals:      true,
		OpVerNotEquals:   true,
		OpVerGreater:     true,
		OpVerLess:        true,
		OpVerGreaterEq:   true,
		OpVerLessEq:      true,
		OpVerContains:    true,
		OpVerContainedIn: true,
		OpIsPrivateIP:    true,
		OpIsPublicIP:     true,
	}
	return validOps[operator]
}
