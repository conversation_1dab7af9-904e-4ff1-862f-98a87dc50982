package constants

// 资源类型
const (
	// 流程管理
	ResourceFlow = "flows"
	// 模板管理
	ResourceTemplate = "templates"
	// 用户管理
	ResourceUser = "users"
	// 角色管理
	ResourceRole = "roles"
	// 权限管理
	ResourcePermission = "permissions"
	// 执行记录管理
	ResourceExecution = "executions"
	// 事件分类管理
	ResourceEventCategory = "event_categories"
	// 事件运营管理
	ResourceEventOperation = "event_operations"
	// 系统管理
	ResourceSystem = "system"
)

// 操作类型
const (
	ActionRead    = "read"    // 查看
	ActionWrite   = "write"   // 创建/编辑
	ActionDelete  = "delete"  // 删除
	ActionExecute = "execute" // 执行
	ActionManage  = "manage"  // 管理（包含所有权限）
)

// 默认角色
const (
	RoleAdmin    = "admin"    // 系统管理员
	RoleUser     = "user"     // 普通用户
	RoleViewer   = "viewer"   // 只读用户
	RoleExecutor = "executor" // 执行用户
)

// 权限组合
var (
	// 管理员权限 - 拥有所有权限
	AdminPermissions = []string{
		ResourceFlow + ":" + ActionManage,
		ResourceTemplate + ":" + ActionManage,
		ResourceUser + ":" + ActionManage,
		ResourceRole + ":" + ActionManage,
		ResourcePermission + ":" + ActionManage,
		ResourceExecution + ":" + ActionManage,
		ResourceEventCategory + ":" + ActionManage,
		ResourceEventOperation + ":" + ActionManage,
		ResourceSystem + ":" + ActionManage,
	}

	// 普通用户权限 - 可以管理流程和模板，查看执行记录，管理事件
	UserPermissions = []string{
		ResourceFlow + ":" + ActionRead,
		ResourceFlow + ":" + ActionWrite,
		ResourceFlow + ":" + ActionDelete,
		ResourceFlow + ":" + ActionExecute,
		ResourceTemplate + ":" + ActionRead,
		ResourceTemplate + ":" + ActionWrite,
		ResourceTemplate + ":" + ActionDelete,
		ResourceExecution + ":" + ActionRead,
		ResourceEventCategory + ":" + ActionRead,
		ResourceEventCategory + ":" + ActionWrite,
		ResourceEventCategory + ":" + ActionDelete,
		ResourceEventOperation + ":" + ActionRead,
		ResourceEventOperation + ":" + ActionWrite,
		ResourceEventOperation + ":" + ActionDelete,
		ResourceUser + ":" + ActionRead, // 可以查看用户信息
	}

	// 只读用户权限 - 只能查看
	ViewerPermissions = []string{
		ResourceFlow + ":" + ActionRead,
		ResourceTemplate + ":" + ActionRead,
		ResourceExecution + ":" + ActionRead,
		ResourceEventCategory + ":" + ActionRead,
		ResourceEventOperation + ":" + ActionRead,
		ResourceUser + ":" + ActionRead,
	}

	// 执行用户权限 - 可以查看和执行流程
	ExecutorPermissions = []string{
		ResourceFlow + ":" + ActionRead,
		ResourceFlow + ":" + ActionExecute,
		ResourceTemplate + ":" + ActionRead,
		ResourceExecution + ":" + ActionRead,
		ResourceEventCategory + ":" + ActionRead,
		ResourceEventOperation + ":" + ActionRead,
		ResourceUser + ":" + ActionRead,
	}
)

// GetPermissionDisplayName 获取权限显示名称
func GetPermissionDisplayName(resource, action string) string {
	resourceNames := map[string]string{
		ResourceFlow:       "流程",
		ResourceTemplate:   "模板",
		ResourceUser:       "用户",
		ResourceRole:       "角色",
		ResourcePermission: "权限",
		ResourceExecution:  "执行记录",
		ResourceSystem:     "系统",
	}

	actionNames := map[string]string{
		ActionRead:    "查看",
		ActionWrite:   "编辑",
		ActionDelete:  "删除",
		ActionExecute: "执行",
		ActionManage:  "管理",
	}

	resourceName := resourceNames[resource]
	if resourceName == "" {
		resourceName = resource
	}

	actionName := actionNames[action]
	if actionName == "" {
		actionName = action
	}

	return resourceName + ":" + actionName
}
