package utils

import (
	"database/sql"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

// MySQLConfig MySQL连接配置
type MySQLConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	Database string
	Charset  string
	// 连接池配置
	MaxOpenConns    int
	MaxIdleConns    int
	ConnMaxLifetime time.Duration
	ConnMaxIdleTime time.Duration
}

// DefaultConfig 返回默认配置
func DefaultConfig() *MySQLConfig {
	return &MySQLConfig{
		Host:            "localhost",
		Port:            3306,
		User:            "root",
		Password:        "",
		Database:        "",
		Charset:         "utf8mb4",
		MaxOpenConns:    10,
		MaxIdleConns:    5,
		ConnMaxLifetime: 30 * time.Minute,
		ConnMaxIdleTime: 10 * time.Minute,
	}
}

func SimpleConfig(host, user, password, database string) *MySQLConfig {
	c := DefaultConfig()
	c.Host = host
	c.User = user
	c.Password = password
	c.Database = database
	return c
}

// MySQLClient MySQL客户端
type MySQLClient struct {
	db *sql.DB
}

// NewMySQLClient 创建新的MySQL客户端
func NewMySQLClient(config *MySQLConfig) (*MySQLClient, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=true&loc=Local",
		config.User, config.Password, config.Host, config.Port, config.Database, config.Charset)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %v", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(config.MaxOpenConns)
	db.SetMaxIdleConns(config.MaxIdleConns)
	db.SetConnMaxLifetime(config.ConnMaxLifetime)
	db.SetConnMaxIdleTime(config.ConnMaxIdleTime)

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %v", err)
	}

	return &MySQLClient{db: db}, nil
}

// Close 关闭数据库连接
func (c *MySQLClient) Close() error {
	if c.db != nil {
		return c.db.Close()
	}
	return nil
}

// Query 执行查询SQL，返回多行结果
func (c *MySQLClient) Query(sql string, args ...interface{}) ([]map[string]interface{}, error) {
	rows, err := c.db.Query(sql, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}
	defer rows.Close()

	return c.scanRows(rows)
}

// QueryRow 执行查询SQL，返回单行结果
func (c *MySQLClient) QueryRow(sql string, args ...interface{}) (map[string]interface{}, error) {
	row := c.db.QueryRow(sql, args...)
	return c.scanRow(row)
}

// Execute 执行增删改SQL，返回影响的行数
func (c *MySQLClient) Execute(sql string, args ...interface{}) (int64, error) {
	result, err := c.db.Exec(sql, args...)
	if err != nil {
		return 0, fmt.Errorf("failed to execute statement: %v", err)
	}

	affected, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("failed to get affected rows: %v", err)
	}

	return affected, nil
}

// ExecuteWithLastInsertID 执行插入SQL，返回最后插入的ID
func (c *MySQLClient) ExecuteWithLastInsertID(sql string, args ...interface{}) (int64, error) {
	result, err := c.db.Exec(sql, args...)
	if err != nil {
		return 0, fmt.Errorf("failed to execute statement: %v", err)
	}

	lastID, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("failed to get last insert id: %v", err)
	}

	return lastID, nil
}

// Begin 开始事务
func (c *MySQLClient) Begin() (*sql.Tx, error) {
	return c.db.Begin()
}

// scanRows 扫描多行结果
func (c *MySQLClient) scanRows(rows *sql.Rows) ([]map[string]interface{}, error) {
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get columns: %v", err)
	}

	var results []map[string]interface{}
	for rows.Next() {
		row := make([]interface{}, len(columns))
		rowPtrs := make([]interface{}, len(columns))
		for i := range row {
			rowPtrs[i] = &row[i]
		}

		if err := rows.Scan(rowPtrs...); err != nil {
			return nil, fmt.Errorf("failed to scan row: %v", err)
		}

		rowMap := make(map[string]interface{})
		for i, col := range columns {
			val := row[i]
			if val == nil {
				rowMap[col] = nil
			} else {
				// 处理时间类型
				switch v := val.(type) {
				case time.Time:
					rowMap[col] = v.Format("2006-01-02 15:04:05")
				default:
					rowMap[col] = v
				}
			}
		}
		results = append(results, rowMap)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating rows: %v", err)
	}

	return results, nil
}

// scanRow 扫描单行结果
func (c *MySQLClient) scanRow(row *sql.Row) (map[string]interface{}, error) {
	// sql.Row 没有 Columns() 方法，我们需要先执行查询获取列信息
	// 这里我们使用一个简化的方法，假设我们知道列名
	// 在实际使用中，建议使用 Query() 方法而不是 QueryRow()

	// 创建一个通用的扫描方法
	var result map[string]interface{}

	// 由于 sql.Row 的限制，这里提供一个简化的实现
	// 实际使用时建议使用 Query() 方法获取多行结果，然后取第一行
	return result, fmt.Errorf("QueryRow is not fully implemented, please use Query() instead")
}

// GetTableInfo 获取表结构信息
func (c *MySQLClient) GetTableInfo(tableName string) ([]map[string]interface{}, error) {
	sql := `SELECT 
		COLUMN_NAME as column_name,
		DATA_TYPE as data_type,
		IS_NULLABLE as is_nullable,
		COLUMN_DEFAULT as column_default,
		COLUMN_COMMENT as column_comment,
		COLUMN_KEY as column_key,
		EXTRA as extra
	FROM INFORMATION_SCHEMA.COLUMNS 
	WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ? 
	ORDER BY ORDINAL_POSITION`

	return c.Query(sql, tableName)
}

// GetTables 获取当前数据库的所有表
func (c *MySQLClient) GetTables() ([]map[string]interface{}, error) {
	sql := `SELECT 
		TABLE_NAME as table_name,
		TABLE_COMMENT as table_comment,
		TABLE_ROWS as table_rows,
		DATA_LENGTH as data_length,
		INDEX_LENGTH as index_length
	FROM INFORMATION_SCHEMA.TABLES 
	WHERE TABLE_SCHEMA = DATABASE()`

	return c.Query(sql)
}

// GetTableIndexes 获取表的索引信息
func (c *MySQLClient) GetTableIndexes(tableName string) ([]map[string]interface{}, error) {
	sql := `SELECT 
		INDEX_NAME as index_name,
		COLUMN_NAME as column_name,
		NON_UNIQUE as non_unique,
		SEQ_IN_INDEX as seq_in_index,
		INDEX_TYPE as index_type
	FROM INFORMATION_SCHEMA.STATISTICS 
	WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ? 
	ORDER BY INDEX_NAME, SEQ_IN_INDEX`

	return c.Query(sql, tableName)
}

// ExecuteWithTransaction 在事务中执行多个SQL
func (c *MySQLClient) ExecuteWithTransaction(queries []string, args [][]interface{}) error {
	tx, err := c.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		}
	}()

	for i, query := range queries {
		var queryArgs []interface{}
		if i < len(args) {
			queryArgs = args[i]
		}

		_, err := tx.Exec(query, queryArgs...)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to execute query %d: %v", i+1, err)
		}
	}

	return tx.Commit()
}

// Ping 测试数据库连接
func (c *MySQLClient) Ping() error {
	return c.db.Ping()
}

// GetStats 获取数据库连接统计信息
func (c *MySQLClient) GetStats() sql.DBStats {
	return c.db.Stats()
}

// 便捷函数：快速连接并执行查询
func QueryMySQL(dsn string, sqlQuery string, args ...interface{}) ([]map[string]interface{}, error) {
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %v", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %v", err)
	}

	rows, err := db.Query(sqlQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}
	defer rows.Close()

	// 复用扫描逻辑
	client := &MySQLClient{db: db}
	return client.scanRows(rows)
}

// 便捷函数：快速连接并执行单行查询
func QueryMySQLRow(dsn string, sqlQuery string, args ...interface{}) (map[string]interface{}, error) {
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %v", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %v", err)
	}

	row := db.QueryRow(sqlQuery, args...)

	// 复用扫描逻辑
	client := &MySQLClient{db: db}
	return client.scanRow(row)
}
