package utils

import (
	"encoding/base64"
	"fmt"
	"testing"
)

// 32-byte key for AES-256 tests only
const testAESKey32 = "12345678901234567890123456789012"

func TestAESDecrypt_RoundTrip(t *testing.T) {
	plaintext := "13899229922"

	// Encrypt first to produce a valid ciphertext (with random IV)
	ct, err := AESEncryptBase64(plaintext, testAESKey32)
	if err != nil {
		t.Fatalf("AESEncryptBase64 error: %v", err)
	}
	if ct == "" || ct == plaintext {
		t.Fatalf("ciphertext invalid: %q", ct)
	}

	// Decrypt and compare
	got, err := AESDecryptBase64(ct, testAESKey32)
	if err != nil {
		t.Fatalf("AESDecryptBase64 error: %v", err)
	}
	if got != plaintext {
		t.Fatalf("roundtrip mismatch: got %q want %q", got, plaintext)
	}
}

func TestAESDecrypt_WrongKey(t *testing.T) {
	plaintext := "13899229922"
	ct, err := AESEncryptBase64(plaintext, testAESKey32)
	if err != nil {
		t.Fatalf("AESEncryptBase64 error: %v", err)
	}

	wrongKey := "abcdefghijklmnopqrstuvwx12345678" // still 32 bytes but different
	if _, err := AESDecryptBase64(ct, wrongKey); err == nil {
		t.Fatalf("expected error when decrypting with wrong key, got nil")
	}
}

func TestAESDecrypt_InvalidInputs(t *testing.T) {
	// invalid base64
	if _, err := AESDecryptBase64("not-base64", testAESKey32); err == nil {
		t.Fatalf("expected error for invalid base64 input")
	}

	// valid base64 but too short to contain IV
	short := base64.StdEncoding.EncodeToString([]byte("short"))
	if _, err := AESDecryptBase64(short, testAESKey32); err == nil {
		t.Fatalf("expected error for invalid ciphertext length")
	}
}

func TestAESDecrypt(t *testing.T) {
	// invalid base64
	got, err := AESDecryptBase64("0UnconI4/d9UvK6jgpS2uWLSvUHMCV5K5+CyQmit3/0=", "1f66142bd2964aeeb742599b90747ee4")
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(got)

}
