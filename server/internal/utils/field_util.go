package utils

import (
	"encoding/json"
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"
)

// GetFieldValue 从对象中获取字段值，支持JSON路径表达式
// 参数:
//   - obj: 源对象 (通常是flowData或prevOutput)
//   - fieldPath: 字段路径，支持JSON路径表达式 (如: data.user.name, arr[0].id, arr[].field)
//
// 返回: 字段值和是否找到
// 注意: arr[].field 返回数组 [value1, value2, ...]，arr[0].field 返回单个值
func GetFieldValue(obj interface{}, fieldPath string) (interface{}, bool) {
	if fieldPath == "" {
		return obj, true
	}

	// 检查是否包含数组遍历语法 []
	if strings.Contains(fieldPath, "[]") {
		return handleArrayTraversalAccess(obj, fieldPath)
	}

	// 分割路径
	parts := strings.Split(fieldPath, ".")
	current := obj

	for _, part := range parts {
		if part == "" {
			continue
		}

		// 处理数组索引 (如: arr[0])
		if strings.Contains(part, "[") && strings.Contains(part, "]") {
			current = handleArrayIndexAccess(current, part)
			if current == nil {
				return nil, false
			}
			continue
		}

		// 处理普通字段访问
		current = getObjectField(current, part)
		if current == nil {
			return nil, false
		}
	}

	return current, true
}

// SetFieldValue 设置对象中的字段值，支持JSON路径表达式
// 参数:
//   - obj: 目标对象 (通常是flowData的map)
//   - fieldPath: 字段路径，支持JSON路径表达式 (如: data.user.name, arr[0].id)
//   - value: 要设置的值
//
// 返回: 是否设置成功
func SetFieldValue(obj map[string]interface{}, fieldPath string, value interface{}) bool {
	if fieldPath == "" {
		return false
	}

	// 分割路径
	parts := strings.Split(fieldPath, ".")
	current := obj

	// 遍历到倒数第二层
	for _, part := range parts[:len(parts)-1] {
		if part == "" {
			continue
		}

		// 处理数组索引 (如: arr[0])
		if strings.Contains(part, "[") && strings.Contains(part, "]") {
			// 对于设值操作，数组索引访问比较复杂，暂时不支持
			return false
		}

		// 确保路径存在
		if _, exists := current[part]; !exists {
			current[part] = make(map[string]interface{})
		}

		// 转换为map类型
		if nextMap, ok := current[part].(map[string]interface{}); ok {
			current = nextMap
		} else {
			// 如果不是map类型，无法继续设置嵌套字段
			return false
		}
	}

	// 设置最后一层的值
	lastPart := parts[len(parts)-1]
	if strings.Contains(lastPart, "[") && strings.Contains(lastPart, "]") {
		// 暂时不支持数组索引设值
		return false
	}

	current[lastPart] = value
	return true
}

// SetFieldValueForArray 为数组遍历设置字段值 (如: datas[].ageStr)
// 参数:
//   - obj: 目标对象
//   - fieldPath: 字段路径 (如: datas[].ageStr)
//   - valueFunc: 值生成函数，接收数组元素和索引，返回要设置的值
//
// 返回: 是否设置成功
func SetFieldValueForArray(obj map[string]interface{}, fieldPath string, valueFunc func(element interface{}, index int) interface{}) bool {
	if !strings.Contains(fieldPath, "[]") {
		return false
	}

	// 解析路径：datas[].ageStr → arrayPath="datas", fieldName="ageStr"
	parts := strings.Split(fieldPath, "[]")
	if len(parts) != 2 {
		return false
	}

	arrayPath := strings.TrimSuffix(parts[0], ".")
	fieldName := strings.TrimPrefix(parts[1], ".")

	// 获取数组对象
	arrayObj, found := GetFieldValue(obj, arrayPath)
	if !found {
		return false
	}

	// 确保是数组类型
	arrayValue := reflect.ValueOf(arrayObj)
	if arrayValue.Kind() != reflect.Slice && arrayValue.Kind() != reflect.Array {
		return false
	}

	// 遍历数组，为每个元素设置字段值
	for i := 0; i < arrayValue.Len(); i++ {
		element := arrayValue.Index(i).Interface()

		// 确保元素是map类型
		if elementMap, ok := element.(map[string]interface{}); ok {
			// 调用值生成函数获取要设置的值
			value := valueFunc(element, i)

			if fieldName == "" {
				// 如果没有字段名，说明要替换整个元素（这种情况比较少见）
				// 暂时不支持
				continue
			} else {
				// 设置字段值
				if !SetFieldValue(elementMap, fieldName, value) {
					// 如果设置失败，直接设置到map中
					elementMap[fieldName] = value
				}
			}
		}
	}

	return true
}

// GetFieldValueFromSources 从多个数据源中获取字段值
// 按优先级顺序查找: prevOutput -> flowData
func GetFieldValueFromSources(prevOutput, flowData map[string]interface{}, fieldPath string) (interface{}, bool) {
	// 先从流程数据中查找
	if flowData != nil {
		if value, found := GetFieldValue(flowData, fieldPath); found {
			return value, true
		}
	}

	// 如果没找到，从上一个节点的输出中查找
	if prevOutput != nil {
		if value, found := GetFieldValue(prevOutput, fieldPath); found {
			return value, true
		}
	}

	return nil, false
}

// handleArrayTraversalAccess 处理数组遍历访问 (如: arr[].field)
// 返回数组中每个元素指定字段的值组成的数组
func handleArrayTraversalAccess(obj interface{}, fieldPath string) (interface{}, bool) {
	// 解析路径：datas[].age → arrayPath="datas", remainingPath="age"
	parts := strings.Split(fieldPath, "[]")
	if len(parts) != 2 {
		return nil, false
	}

	arrayPath := strings.TrimSuffix(parts[0], ".")
	remainingPath := strings.TrimPrefix(parts[1], ".")

	// 获取数组对象
	arrayObj, found := GetFieldValue(obj, arrayPath)
	if !found {
		return nil, false
	}

	// 确保是数组类型
	arrayValue := reflect.ValueOf(arrayObj)
	if arrayValue.Kind() != reflect.Slice && arrayValue.Kind() != reflect.Array {
		return nil, false
	}

	// 遍历数组，提取每个元素的字段值
	var results []interface{}
	for i := 0; i < arrayValue.Len(); i++ {
		element := arrayValue.Index(i).Interface()

		if remainingPath == "" {
			// 如果没有剩余路径，直接返回元素本身
			results = append(results, element)
		} else {
			// 从元素中获取指定字段的值
			if fieldValue, found := GetFieldValue(element, remainingPath); found {
				results = append(results, fieldValue)
			} else {
				// 如果字段不存在，添加nil
				results = append(results, nil)
			}
		}
	}

	return results, true
}

// handleArrayIndexAccess 处理数组索引访问 (如: arr[0])
func handleArrayIndexAccess(obj interface{}, part string) interface{} {
	// 解析字段名和索引
	re := regexp.MustCompile(`^([^[]+)\[(\d+)\]$`)
	matches := re.FindStringSubmatch(part)
	if len(matches) != 3 {
		return nil
	}

	fieldName := matches[1]
	indexStr := matches[2]
	index, err := strconv.Atoi(indexStr)
	if err != nil {
		return nil
	}

	// 获取数组字段
	arrayField := getObjectField(obj, fieldName)
	if arrayField == nil {
		return nil
	}

	// 转换为数组并访问索引
	arrayValue := reflect.ValueOf(arrayField)
	if arrayValue.Kind() != reflect.Slice && arrayValue.Kind() != reflect.Array {
		return nil
	}

	if index < 0 || index >= arrayValue.Len() {
		return nil
	}

	return arrayValue.Index(index).Interface()
}

// ValidateFieldPath 验证字段路径格式是否正确
func ValidateFieldPath(fieldPath string) error {
	if fieldPath == "" {
		return fmt.Errorf("field path cannot be empty")
	}

	// 检查是否包含非法字符
	if strings.Contains(fieldPath, "..") {
		return fmt.Errorf("field path cannot contain '..'")
	}

	// 验证数组索引格式
	re := regexp.MustCompile(`\[(\d+)\]`)
	matches := re.FindAllString(fieldPath, -1)
	for _, match := range matches {
		indexStr := strings.Trim(match, "[]")
		if _, err := strconv.Atoi(indexStr); err != nil {
			return fmt.Errorf("invalid array index: %s", match)
		}
	}

	return nil
}

// GetFieldPathDescription 获取字段路径的描述信息
func GetFieldPathDescription(fieldPath string) string {
	if fieldPath == "" {
		return "根对象"
	}

	parts := strings.Split(fieldPath, ".")
	descriptions := make([]string, 0, len(parts))

	for _, part := range parts {
		if part == "" {
			continue
		}

		if strings.Contains(part, "[") && strings.Contains(part, "]") {
			// 数组索引访问
			re := regexp.MustCompile(`^([^[]+)\[(\d+)\]$`)
			matches := re.FindStringSubmatch(part)
			if len(matches) == 3 {
				fieldName := matches[1]
				index := matches[2]
				descriptions = append(descriptions, fmt.Sprintf("%s的第%s个元素", fieldName, index))
			} else {
				descriptions = append(descriptions, part)
			}
		} else {
			descriptions = append(descriptions, part+"字段")
		}
	}

	return strings.Join(descriptions, "的")
}

// ReplaceVariablesInText 替换文本中的${变量}为flowData中的实际值
// 例如："${name}您好，检测到您发送了${files}文件" => "张三您好，检测到您发送了3个文件"
func ReplaceVariablesInText(text string, flowData map[string]interface{}) string {
	if text == "" || flowData == nil {
		return text
	}

	// 匹配${变量名}，变量名支持字母、数字、下划线、点
	re := regexp.MustCompile(`\$\{([a-zA-Z0-9_\.\[\]]+)\}`)

	return re.ReplaceAllStringFunc(text, func(match string) string {
		// 提取变量名
		groups := re.FindStringSubmatch(match)
		if len(groups) != 2 {
			return match // 匹配异常，原样返回
		}
		fieldPath := groups[1]
		if value, found := GetFieldValue(flowData, fieldPath); found && value != nil {
			return fmt.Sprintf("%v", value)
		}
		return "" // 未找到变量，替换为空
	})
}

// ReplaceVariablesInObject 在一个任意JSON兼容对象上执行占位符替换
// - 若是字符串：对其中的 ${var} 做替换（与 ReplaceVariablesInText 相同）
// - 若是对象(map)或数组([]interface{})：递归处理其子元素
// - 其他类型：原样返回
func ReplaceVariablesInObject(value interface{}, flowData map[string]interface{}) interface{} {
	switch v := value.(type) {
	case string:
		return ReplaceVariablesInText(v, flowData)
	case map[string]interface{}:
		out := make(map[string]interface{}, len(v))
		for k, val := range v {
			out[k] = ReplaceVariablesInObject(val, flowData)
		}
		return out
	case []interface{}:
		out := make([]interface{}, 0, len(v))
		for _, item := range v {
			out = append(out, ReplaceVariablesInObject(item, flowData))
		}
		return out
	default:
		return value
	}
}

// ReplaceUnquotedVariablesInJSONTemplate 在 JSON 模板字符串中，将未被双引号包裹的占位符 ${var}
// 替换为其对应值的 JSON 序列化文本（对象/数组/数字/字符串均可）。
// 该函数不会替换位于 JSON 字符串字面量内部的占位符，避免破坏字符串结构。
func ReplaceUnquotedVariablesInJSONTemplate(jsonTemplate string, flowData map[string]interface{}) string {
	if jsonTemplate == "" || flowData == nil {
		return jsonTemplate
	}

	re := regexp.MustCompile(`\$\{([a-zA-Z0-9_\.\[\]]+)\}`)
	// 先找出所有占位符位置
	indices := re.FindAllStringSubmatchIndex(jsonTemplate, -1)
	if len(indices) == 0 {
		return jsonTemplate
	}

	// 扫描判断每个位置是否在字符串字面量中
	var builder strings.Builder
	inString := false
	escaped := false
	cursor := 0
	iMatch := 0

	for pos := 0; pos < len(jsonTemplate); {
		// 命中占位符的起始位置
		if iMatch < len(indices) && pos == indices[iMatch][0] {
			if !inString {
				// 写入占位符之前的内容
				builder.WriteString(jsonTemplate[cursor:pos])
				// 取出变量名
				varStart := indices[iMatch][2]
				varEnd := indices[iMatch][3]
				varName := jsonTemplate[varStart:varEnd]
				if val, found := GetFieldValue(flowData, varName); found {
					if b, err := json.Marshal(val); err == nil {
						builder.Write(b)
					} else {
						builder.WriteString("null")
					}
				} else {
					builder.WriteString("null")
				}
				// 跳过占位符
				pos = indices[iMatch][1]
				cursor = pos
				iMatch++
				continue
			} else {
				// 该占位符位于字符串字面量内，不参与“未加引号”替换。
				// 为防止阻塞后续占位符处理，推进到下一个匹配。
				iMatch++
				// 不修改 pos/cursor，继续逐字符扫描
			}
		}

		ch := jsonTemplate[pos]
		if ch == '\\' && !escaped { // 反斜杠处理转义
			escaped = true
			pos++
			continue
		}
		if ch == '"' && !escaped {
			inString = !inString
		}
		escaped = false
		pos++
	}

	if cursor < len(jsonTemplate) {
		builder.WriteString(jsonTemplate[cursor:])
	}
	return builder.String()
}

// DeleteFieldValue 删除字段值，支持JSON路径
func DeleteFieldValue(obj map[string]interface{}, fieldPath string) bool {
	if obj == nil || fieldPath == "" {
		return false
	}

	// 解析字段路径
	parts := strings.Split(fieldPath, ".")
	if len(parts) == 1 {
		// 直接删除顶级字段
		_, exists := obj[fieldPath]
		if exists {
			delete(obj, fieldPath)
			return true
		}
		return false
	}

	// 递归删除嵌套字段
	current := obj
	for _, part := range parts[:len(parts)-1] {
		if current == nil {
			return false
		}

		if val, exists := current[part]; exists {
			if nextMap, ok := val.(map[string]interface{}); ok {
				current = nextMap
			} else {
				// 中间路径不是map，无法继续
				return false
			}
		} else {
			// 中间路径不存在
			return false
		}
	}

	// 删除最后一个字段
	lastPart := parts[len(parts)-1]
	if current != nil {
		_, exists := current[lastPart]
		if exists {
			delete(current, lastPart)
			return true
		}
	}

	return false
}
