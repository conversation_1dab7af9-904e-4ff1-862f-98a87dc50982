package utils

import (
	"fmt"
	"net"
	"reflect"
	"regexp"
	"sec-flow-server/internal/constants"
	"strconv"
	"strings"
)

// EvaluateCondition 通用条件判断函数
// 参数:
//   - obj: 原始对象 (对数组过滤节点是数组元素，对条件判断节点是flowData)
//   - fieldPath: 字段路径，支持JSON路径表达式 (如: data.a, data.b, arr[].age)
//   - operator: 操作符
//   - compareValue: 比较值
//
// 符
//   - compare
//
// 返回: 判断结果和错误信息
func EvaluateCondition(obj interface{}, fieldPath, operator, compareValue string) (bool, error) {
	// 对于存在性操作符，需要特殊处理
	if operator == constants.OpExists || operator == constants.OpNotExists {
		result, err := getFieldValueWithExistence(obj, fieldPath)
		if err != nil {
			// 字段路径错误
			return false, fmt.Errorf("failed to get field value: %v", err)
		}

		if operator == constants.OpExists {
			return result.Exists, nil
		} else { // OpNotExists
			return !result.Exists, nil
		}
	}

	// 获取字段值
	fieldValue, err := getFieldValue(obj, fieldPath)
	if err != nil {
		return false, fmt.Errorf("failed to get field value: %v", err)
	}

	// 如果字段路径以[]结尾，表示对数组的每个元素进行比较
	if strings.HasSuffix(fieldPath, "[]") {
		return evaluateArrayCondition(fieldValue, operator, compareValue)
	}

	// 单个值比较
	return evaluateSingleCondition(fieldValue, operator, compareValue)
}

// FieldExistenceResult 字段存在性检查结果
type FieldExistenceResult struct {
	Value  interface{}
	Exists bool
}

// fieldNotExistMarker 字段不存在的标记
type fieldNotExistMarker struct{}

var fieldNotExist = &fieldNotExistMarker{}

// getFieldValueWithExistence 根据字段路径获取值，保留字段不存在的信息
func getFieldValueWithExistence(obj interface{}, fieldPath string) (*FieldExistenceResult, error) {
	if fieldPath == "" {
		return &FieldExistenceResult{Value: obj, Exists: true}, nil
	}

	// 处理数组访问标记 []
	isArrayAccess := strings.HasSuffix(fieldPath, "[]")
	if isArrayAccess {
		fieldPath = strings.TrimSuffix(fieldPath, "[]")
	}

	// 分割路径
	parts := strings.Split(fieldPath, ".")
	current := obj
	exists := true

	for _, part := range parts {
		if part == "" {
			continue
		}

		// 处理数组索引 (如: arr[0])
		if strings.Contains(part, "[") && strings.Contains(part, "]") {
			current = handleArrayIndex(current, part)
			if current == nil {
				exists = false
				break
			}
			continue
		}

		// 处理普通字段访问
		result := getObjectFieldWithExistence(current, part)
		if result == fieldNotExist {
			exists = false
			break
		}
		current = result
	}

	return &FieldExistenceResult{Value: current, Exists: exists}, nil
}

// getObjectFieldWithExistence 从对象中获取字段值，区分字段不存在和字段值为nil
func getObjectFieldWithExistence(obj interface{}, fieldName string) interface{} {
	if obj == nil {
		return nil
	}

	// 处理map类型
	if objMap, ok := obj.(map[string]interface{}); ok {
		val, exists := objMap[fieldName]
		if !exists {
			return fieldNotExist // 字段不存在
		}
		return val // 字段存在，可能为nil
	}

	// 处理结构体类型 (使用反射)
	objValue := reflect.ValueOf(obj)
	if objValue.Kind() == reflect.Ptr {
		objValue = objValue.Elem()
	}

	if objValue.Kind() != reflect.Struct {
		return fieldNotExist
	}

	fieldValue := objValue.FieldByName(fieldName)
	if !fieldValue.IsValid() {
		return fieldNotExist // 字段不存在
	}

	return fieldValue.Interface()
}

// getFieldValue 根据字段路径获取值，支持嵌套对象和数组访问
func getFieldValue(obj interface{}, fieldPath string) (interface{}, error) {
	if fieldPath == "" {
		return obj, nil
	}

	// 处理数组访问标记 []
	isArrayAccess := strings.HasSuffix(fieldPath, "[]")
	if isArrayAccess {
		fieldPath = strings.TrimSuffix(fieldPath, "[]")
	}

	// 分割路径
	parts := strings.Split(fieldPath, ".")
	current := obj

	for _, part := range parts {
		if part == "" {
			continue
		}

		// 处理数组索引 (如: arr[0])
		if strings.Contains(part, "[") && strings.Contains(part, "]") {
			current = handleArrayIndex(current, part)
			if current == nil {
				return nil, fmt.Errorf("array index access failed for part: %s", part)
			}
			continue
		}

		// 处理普通字段访问
		current = getObjectField(current, part)
		if current == nil {
			return nil, fmt.Errorf("field not found: %s", part)
		}
	}

	return current, nil
}

// handleArrayIndex 处理数组索引访问 (如: arr[0])
func handleArrayIndex(obj interface{}, part string) interface{} {
	// 解析字段名和索引
	re := regexp.MustCompile(`^([^[]+)\[(\d+)\]$`)
	matches := re.FindStringSubmatch(part)
	if len(matches) != 3 {
		return nil
	}

	fieldName := matches[1]
	indexStr := matches[2]
	index, err := strconv.Atoi(indexStr)
	if err != nil {
		return nil
	}

	// 获取数组字段
	arrayField := getObjectField(obj, fieldName)
	if arrayField == nil {
		return nil
	}

	// 转换为数组并访问索引
	arrayValue := reflect.ValueOf(arrayField)
	if arrayValue.Kind() != reflect.Slice && arrayValue.Kind() != reflect.Array {
		return nil
	}

	if index < 0 || index >= arrayValue.Len() {
		return nil
	}

	return arrayValue.Index(index).Interface()
}

// getObjectField 从对象中获取字段值
func getObjectField(obj interface{}, fieldName string) interface{} {
	if obj == nil {
		return nil
	}

	// 处理map类型
	if objMap, ok := obj.(map[string]interface{}); ok {
		return objMap[fieldName]
	}

	// 处理结构体类型 (使用反射)
	objValue := reflect.ValueOf(obj)
	if objValue.Kind() == reflect.Ptr {
		objValue = objValue.Elem()
	}

	if objValue.Kind() != reflect.Struct {
		return nil
	}

	fieldValue := objValue.FieldByName(fieldName)
	if !fieldValue.IsValid() {
		return nil
	}

	return fieldValue.Interface()
}

// evaluateArrayCondition 对数组的每个元素进行条件判断
func evaluateArrayCondition(fieldValue interface{}, operator, compareValue string) (bool, error) {
	arrayValue := reflect.ValueOf(fieldValue)
	if arrayValue.Kind() != reflect.Slice && arrayValue.Kind() != reflect.Array {
		return false, fmt.Errorf("field value is not an array")
	}

	// 对数组的每个元素进行判断
	for i := 0; i < arrayValue.Len(); i++ {
		element := arrayValue.Index(i).Interface()
		result, err := evaluateSingleCondition(element, operator, compareValue)
		if err != nil {
			continue // 跳过无法比较的元素
		}
		if result {
			return true, nil // 只要有一个元素满足条件就返回true
		}
	}

	return false, nil
}

// evaluateSingleCondition 单个值的条件判断
func evaluateSingleCondition(fieldValue interface{}, operator, compareValue string) (bool, error) {
	// 验证操作符
	if !constants.IsValidOperator(operator) {
		return false, fmt.Errorf("unsupported operator: %s", operator)
	}

	switch operator {
	// 基础比较操作符
	case constants.OpEquals:
		return compareEqual(fieldValue, compareValue), nil
	case constants.OpNotEquals:
		return !compareEqual(fieldValue, compareValue), nil
	case constants.OpGreater:
		return compareGreater(fieldValue, compareValue, false)
	case constants.OpLess:
		return compareLess(fieldValue, compareValue, false)
	case constants.OpGreaterEq:
		return compareGreater(fieldValue, compareValue, true)
	case constants.OpLessEq:
		return compareLess(fieldValue, compareValue, true)

	// 包含操作符
	case constants.OpContains:
		return compareContains(fieldValue, compareValue), nil
	case constants.OpNotContains:
		return !compareContains(fieldValue, compareValue), nil

	// 空值操作符
	case constants.OpIsEmpty:
		return isEmpty(fieldValue), nil
	case constants.OpIsNotEmpty:
		return !isEmpty(fieldValue), nil

	// 长度比较操作符
	case constants.OpLenEquals:
		return compareLengthEqual(fieldValue, compareValue)
	case constants.OpLenNotEquals:
		result, err := compareLengthEqual(fieldValue, compareValue)
		return !result, err
	case constants.OpLenGreater:
		return compareLengthGreater(fieldValue, compareValue, false)
	case constants.OpLenLess:
		return compareLengthLess(fieldValue, compareValue, false)
	case constants.OpLenGreaterEq:
		return compareLengthGreater(fieldValue, compareValue, true)
	case constants.OpLenLessEq:
		return compareLengthLess(fieldValue, compareValue, true)

	// 版本号比较操作符
	case constants.OpVerEquals:
		return compareVersionEqual(fieldValue, compareValue)
	case constants.OpVerNotEquals:
		result, err := compareVersionEqual(fieldValue, compareValue)
		return !result, err
	case constants.OpVerGreater:
		return compareVersionGreater(fieldValue, compareValue, false)
	case constants.OpVerLess:
		return compareVersionLess(fieldValue, compareValue, false)
	case constants.OpVerGreaterEq:
		return compareVersionGreater(fieldValue, compareValue, true)
	case constants.OpVerLessEq:
		return compareVersionLess(fieldValue, compareValue, true)
	case constants.OpVerContains:
		return compareVersionContains(fmt.Sprintf("%v", fieldValue), compareValue)
	case constants.OpVerContainedIn:
		return compareVersionContains(compareValue, fmt.Sprintf("%v", fieldValue))

	// IP地址判断操作符
	case constants.OpIsPrivateIP:
		return IsPrivateIP(fieldValue)
	case constants.OpIsPublicIP:
		return isPublicIP(fieldValue)

	default:
		return false, fmt.Errorf("unsupported operator: %s", operator)
	}
}

// compareEqual 相等比较
func compareEqual(fieldValue interface{}, compareValue string) bool {
	if fieldValue == nil {
		return compareValue == ""
	}

	fieldStr := fmt.Sprintf("%v", fieldValue)
	return fieldStr == compareValue
}

// compareGreater 大于比较
func compareGreater(fieldValue interface{}, compareValue string, orEqual bool) (bool, error) {
	fieldNum, err1 := convertToFloat(fieldValue)
	compareNum, err2 := strconv.ParseFloat(compareValue, 64)

	if err1 == nil && err2 == nil {
		// 数值比较
		if orEqual {
			return fieldNum >= compareNum, nil
		}
		return fieldNum > compareNum, nil
	}

	// 字符串比较
	fieldStr := fmt.Sprintf("%v", fieldValue)
	if orEqual {
		return fieldStr >= compareValue, nil
	}
	return fieldStr > compareValue, nil
}

// compareLess 小于比较
func compareLess(fieldValue interface{}, compareValue string, orEqual bool) (bool, error) {
	fieldNum, err1 := convertToFloat(fieldValue)
	compareNum, err2 := strconv.ParseFloat(compareValue, 64)

	if err1 == nil && err2 == nil {
		// 数值比较
		if orEqual {
			return fieldNum <= compareNum, nil
		}
		return fieldNum < compareNum, nil
	}

	// 字符串比较
	fieldStr := fmt.Sprintf("%v", fieldValue)
	if orEqual {
		return fieldStr <= compareValue, nil
	}
	return fieldStr < compareValue, nil
}

// compareContains 包含比较
func compareContains(fieldValue interface{}, compareValue string) bool {
	if fieldValue == nil {
		return false
	}

	fieldStr := fmt.Sprintf("%v", fieldValue)
	return strings.Contains(fieldStr, compareValue)
}

// isEmpty 判断是否为空
func isEmpty(fieldValue interface{}) bool {
	if fieldValue == nil {
		return true
	}

	switch v := fieldValue.(type) {
	case string:
		return v == ""
	case []interface{}:
		return len(v) == 0
	case map[string]interface{}:
		return len(v) == 0
	default:
		// 使用反射判断其他类型
		value := reflect.ValueOf(fieldValue)
		switch value.Kind() {
		case reflect.Slice, reflect.Array, reflect.Map, reflect.Chan:
			return value.Len() == 0
		case reflect.Ptr, reflect.Interface:
			return value.IsNil()
		default:
			return false
		}
	}
}

// convertToFloat 尝试将值转换为浮点数
func convertToFloat(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return strconv.ParseFloat(fmt.Sprintf("%v", value), 64)
	}
}

// getLength 获取值的长度
func getLength(value interface{}) (int, error) {
	if value == nil {
		return 0, nil
	}

	switch v := value.(type) {
	case string:
		return len(v), nil
	case []interface{}:
		return len(v), nil
	case map[string]interface{}:
		return len(v), nil
	default:
		// 使用反射处理其他类型
		reflectValue := reflect.ValueOf(value)
		switch reflectValue.Kind() {
		case reflect.Slice, reflect.Array, reflect.Map, reflect.Chan, reflect.String:
			return reflectValue.Len(), nil
		default:
			return 0, fmt.Errorf("cannot get length of type %T", value)
		}
	}
}

// compareLengthEqual 长度相等比较
func compareLengthEqual(fieldValue interface{}, compareValue string) (bool, error) {
	fieldLen, err := getLength(fieldValue)
	if err != nil {
		return false, err
	}

	compareLen, err := strconv.Atoi(compareValue)
	if err != nil {
		return false, fmt.Errorf("invalid length value: %s", compareValue)
	}

	return fieldLen == compareLen, nil
}

// compareLengthGreater 长度大于比较
func compareLengthGreater(fieldValue interface{}, compareValue string, orEqual bool) (bool, error) {
	fieldLen, err := getLength(fieldValue)
	if err != nil {
		return false, err
	}

	compareLen, err := strconv.Atoi(compareValue)
	if err != nil {
		return false, fmt.Errorf("invalid length value: %s", compareValue)
	}

	if orEqual {
		return fieldLen >= compareLen, nil
	}
	return fieldLen > compareLen, nil
}

// compareLengthLess 长度小于比较
func compareLengthLess(fieldValue interface{}, compareValue string, orEqual bool) (bool, error) {
	fieldLen, err := getLength(fieldValue)
	if err != nil {
		return false, err
	}

	compareLen, err := strconv.Atoi(compareValue)
	if err != nil {
		return false, fmt.Errorf("invalid length value: %s", compareValue)
	}

	if orEqual {
		return fieldLen <= compareLen, nil
	}
	return fieldLen < compareLen, nil
}

// parseVersion 解析版本号为数字数组
func parseVersion(version string) ([]int, error) {
	if version == "" {
		return []int{}, nil
	}

	// 移除可能的前缀 'v'
	if strings.HasPrefix(version, "v") {
		version = version[1:]
	}

	parts := strings.Split(version, ".")
	result := make([]int, len(parts))

	for i, part := range parts {
		// 处理可能包含非数字字符的情况（如 1.20.3-beta）
		numPart := regexp.MustCompile(`^\d+`).FindString(part)
		if numPart == "" {
			return nil, fmt.Errorf("invalid version part: %s", part)
		}

		num, err := strconv.Atoi(numPart)
		if err != nil {
			return nil, fmt.Errorf("invalid version number: %s", part)
		}
		result[i] = num
	}

	return result, nil
}

// compareVersions 比较两个版本号
// 返回值: -1 表示 v1 < v2, 0 表示 v1 == v2, 1 表示 v1 > v2
func compareVersions(v1, v2 []int) int {
	maxLen := len(v1)
	if len(v2) > maxLen {
		maxLen = len(v2)
	}

	for i := 0; i < maxLen; i++ {
		val1 := 0
		if i < len(v1) {
			val1 = v1[i]
		}

		val2 := 0
		if i < len(v2) {
			val2 = v2[i]
		}

		if val1 < val2 {
			return -1
		} else if val1 > val2 {
			return 1
		}
	}

	return 0
}

// compareVersionEqual 版本号相等比较
func compareVersionEqual(fieldValue interface{}, compareValue string) (bool, error) {
	fieldVersion := fmt.Sprintf("%v", fieldValue)

	v1, err := parseVersion(fieldVersion)
	if err != nil {
		return false, fmt.Errorf("invalid field version: %v", err)
	}

	v2, err := parseVersion(compareValue)
	if err != nil {
		return false, fmt.Errorf("invalid compare version: %v", err)
	}

	return compareVersions(v1, v2) == 0, nil
}

// compareVersionGreater 版本号大于比较
func compareVersionGreater(fieldValue interface{}, compareValue string, orEqual bool) (bool, error) {
	fieldVersion := fmt.Sprintf("%v", fieldValue)

	v1, err := parseVersion(fieldVersion)
	if err != nil {
		return false, fmt.Errorf("invalid field version: %v", err)
	}

	v2, err := parseVersion(compareValue)
	if err != nil {
		return false, fmt.Errorf("invalid compare version: %v", err)
	}

	result := compareVersions(v1, v2)
	if orEqual {
		return result >= 0, nil
	}
	return result > 0, nil
}

// compareVersionLess 版本号小于比较
func compareVersionLess(fieldValue interface{}, compareValue string, orEqual bool) (bool, error) {
	fieldVersion := fmt.Sprintf("%v", fieldValue)

	v1, err := parseVersion(fieldVersion)
	if err != nil {
		return false, fmt.Errorf("invalid field version: %v", err)
	}

	v2, err := parseVersion(compareValue)
	if err != nil {
		return false, fmt.Errorf("invalid compare version: %v", err)
	}

	result := compareVersions(v1, v2)
	if orEqual {
		return result <= 0, nil
	}
	return result < 0, nil
}

// compareVersionContains 版本号包含比较
// 检查小版本号是否包含于大版本号中
// 例如: 1.20.3 包含于 1.20, 1.20 包含于 1
func compareVersionContains(smallVersion, bigVersion string) (bool, error) {
	small, err := parseVersion(fmt.Sprintf("%v", smallVersion))
	if err != nil {
		return false, fmt.Errorf("invalid small version: %v", err)
	}

	big, err := parseVersion(bigVersion)
	if err != nil {
		return false, fmt.Errorf("invalid big version: %v", err)
	}

	// 大版本的长度不能超过小版本
	if len(big) > len(small) {
		return false, nil
	}

	// 检查大版本的每一部分是否与小版本对应部分相等
	for i := 0; i < len(big); i++ {
		if big[i] != small[i] {
			return false, nil
		}
	}

	return true, nil
}

// IsPrivateIP 判断IP是否为内网IP
func IsPrivateIP(fieldValue interface{}) (bool, error) {
	if fieldValue == nil {
		return false, fmt.Errorf("IP value is nil")
	}

	ipStr := fmt.Sprintf("%v", fieldValue)
	if ipStr == "" {
		return false, fmt.Errorf("IP value is empty")
	}

	// 解析IP地址
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false, fmt.Errorf("invalid IP address: %s", ipStr)
	}

	// 判断是否为内网IP
	return isPrivateIPAddress(ip), nil
}

// isPublicIP 判断IP是否为外网IP
func isPublicIP(fieldValue interface{}) (bool, error) {
	if fieldValue == nil {
		return false, fmt.Errorf("IP value is nil")
	}

	ipStr := fmt.Sprintf("%v", fieldValue)
	if ipStr == "" {
		return false, fmt.Errorf("IP value is empty")
	}

	// 解析IP地址
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false, fmt.Errorf("invalid IP address: %s", ipStr)
	}

	// 判断是否为外网IP（非内网IP）
	return !isPrivateIPAddress(ip), nil
}

// isPrivateIPAddress 判断IP地址是否为内网地址
func isPrivateIPAddress(ip net.IP) bool {
	// 定义内网IP地址段
	privateIPBlocks := []*net.IPNet{
		// IPv4 内网地址段
		{IP: net.IPv4(10, 0, 0, 0), Mask: net.CIDRMask(8, 32)},     // 10.0.0.0/8
		{IP: net.IPv4(172, 16, 0, 0), Mask: net.CIDRMask(12, 32)},  // **********/12
		{IP: net.IPv4(192, 168, 0, 0), Mask: net.CIDRMask(16, 32)}, // ***********/16
		{IP: net.IPv4(127, 0, 0, 0), Mask: net.CIDRMask(8, 32)},    // *********/8 (localhost)
		{IP: net.IPv4(169, 254, 0, 0), Mask: net.CIDRMask(16, 32)}, // ***********/16 (link-local)

		// IPv6 内网地址段
		{IP: net.ParseIP("::1"), Mask: net.CIDRMask(128, 128)},   // ::1/128 (localhost)
		{IP: net.ParseIP("fc00::"), Mask: net.CIDRMask(7, 128)},  // fc00::/7 (unique local)
		{IP: net.ParseIP("fe80::"), Mask: net.CIDRMask(10, 128)}, // fe80::/10 (link-local)
	}

	// 检查IP是否在任何一个内网地址段中
	for _, block := range privateIPBlocks {
		if block.Contains(ip) {
			return true
		}
	}

	return false
}
