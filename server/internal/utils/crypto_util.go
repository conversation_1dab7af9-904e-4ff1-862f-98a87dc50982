package utils

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"crypto/rand"
	"encoding/base64"
	"encoding/hex"
	"fmt"
)

// pkcs7Pad pads the input to a multiple of blockSize using PKCS#7.
func pkcs7Pad(data []byte, blockSize int) []byte {
	if blockSize <= 0 {
		return data
	}
	padding := blockSize - len(data)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padtext...)
}

// pkcs7Unpad removes PKCS#7 padding.
func pkcs7Unpad(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("invalid padding size")
	}
	l := int(data[len(data)-1])
	if l == 0 || l > len(data) {
		return nil, fmt.Errorf("invalid padding")
	}
	// constant-time-ish check
	for i := 0; i < l; i++ {
		if data[len(data)-1-i] != byte(l) {
			return nil, fmt.<PERSON>rrorf("invalid padding content")
		}
	}
	return data[:len(data)-l], nil
}

// AESEncryptBase64 encrypts plainText using AES-256-CBC with a random IV and returns base64(iv|ciphertext).
// key must be 32 bytes (UTF-8). Returns "" and error if key length invalid or encryption fails.
func AESEncryptBase64(plainText string, key string) (string, error) {
	if len(key) != 32 {
		return "", fmt.Errorf("aes key length must be 32 bytes for AES-256")
	}
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}
	iv := make([]byte, aes.BlockSize)
	if _, err := rand.Read(iv); err != nil {
		return "", err
	}
	content := pkcs7Pad([]byte(plainText), aes.BlockSize)
	cipherText := make([]byte, len(content))
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(cipherText, content)
	// prepend IV
	out := append(iv, cipherText...)
	return base64.StdEncoding.EncodeToString(out), nil
}

// AESDecryptBase64 decrypts base64(iv|ciphertext) produced by AESEncryptBase64 using AES-256-CBC.
func AESDecryptBase64(b64 string, key string) (string, error) {
	if len(key) != 32 {
		return "", fmt.Errorf("aes key length must be 32 bytes for AES-256")
	}
	raw, err := base64.StdEncoding.DecodeString(b64)
	if err != nil {
		return "", err
	}
	if len(raw) < aes.BlockSize || (len(raw)-aes.BlockSize)%aes.BlockSize != 0 {
		return "", fmt.Errorf("invalid ciphertext length")
	}
	iv := raw[:aes.BlockSize]
	ct := raw[aes.BlockSize:]
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}
	mode := cipher.NewCBCDecrypter(block, iv)
	plain := make([]byte, len(ct))
	mode.CryptBlocks(plain, ct)
	unpadded, err := pkcs7Unpad(plain)
	if err != nil {
		return "", err
	}
	return string(unpadded), nil
}

// MD5HexLower returns lower-case hex string of MD5(s).
func MD5HexLower(s string) string {
	h := md5.Sum([]byte(s))
	return hex.EncodeToString(h[:])
}
