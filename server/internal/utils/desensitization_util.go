package utils

import "strings"

// HideName 姓名脱敏
func HideName(name string) string {
	if len(name) <= 0 {
		return name
	}
	r := []rune(name)
	length := len(r)
	if length <= 3 {
		return star(name, 0, length-1)
	}
	if length >= 4 && length < 6 {
		return star(name, 0, length-2)
	}
	if length >= 6 {
		return star(name, 2, length-6)
	}
	return name
}

// HidePhone 手机号脱敏
func HidePhone(phone string) string {
	if len(phone) <= 0 {
		return phone
	}
	if len([]rune(phone)) < 11 {
		return phone
	}
	return star(phone, 3, 3)
}

// HideIdentity 身份证号脱敏
func HideIdentity(identity string) string {
	if len(identity) <= 0 {
		return identity
	}
	if len([]rune(identity)) < 18 {
		return identity
	}
	return star(identity, 5, 3)
}

// HideBankCardNo 银行卡号脱敏
func HideBankCardNo(cardNo string) string {
	if len(cardNo) <= 0 {
		return cardNo
	}
	if len([]rune(cardNo)) < 10 {
		return cardNo
	}
	return star(cardNo, 6, 4)
}

// HideEmail 电子邮箱脱敏
func HideEmail(email string) string {
	if len(email) <= 0 {
		return email
	}
	emails := strings.Split(email, "@")
	if len(emails) <= 1 {
		return email
	}
	var newEmail string
	prefix := emails[0]
	prefixLength := len([]rune(prefix))
	if prefixLength <= 5 {
		newEmail = star(prefix, 0, prefixLength-2)
	}
	if prefixLength > 5 {
		newEmail = star(prefix, 3, 0)
	}
	return newEmail + "@" + emails[1]
}

// HidePassportNo 护照脱敏
func HidePassportNo(passportNo string) string {
	if len(passportNo) <= 0 {
		return passportNo
	}
	if len([]rune(passportNo)) < 4 {
		return passportNo
	}
	return star(passportNo, 1, 3)
}

// HideHKMacTwTravelNo 港澳通行证脱敏
func HideHKMacTwTravelNo(travelNo string) string {
	if len(travelNo) <= 0 {
		return travelNo
	}
	if len([]rune(travelNo)) < 9 {
		return travelNo
	}
	return star(travelNo, 1, 3)
}

// HideAddress 地址脱敏
func HideAddress(address string) string {
	if len(address) <= 0 {
		return address
	}
	length := len([]rune(address))
	if length <= 5 {
		return star(address, 1, 2)
	}
	if length > 5 && length <= 9 {
		return star(address, 0, 5)
	}
	if length >= 10 {
		return star(address, length-9, 5)
	}
	return address
}

func star(s string, prefixCount int, suffixCount int) string {
	r := []rune(s)
	for i := prefixCount; i < len(r)-suffixCount; i++ {
		r[i] = '*'
	}
	return string(r)
}
