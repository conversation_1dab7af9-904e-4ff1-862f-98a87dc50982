package handler

import (
	"net/http"
	"sec-flow-server/internal/model"
	"sec-flow-server/internal/service"
	"sec-flow-server/pkg/response"
	"strings"

	"github.com/gin-gonic/gin"
)

// EventCategoryHandler 事件分类处理器
type EventCategoryHandler struct {
	eventCategoryService *service.EventCategoryService
}

// NewEventCategoryHandler 创建事件分类处理器
func NewEventCategoryHandler() *EventCategoryHandler {
	return &EventCategoryHandler{
		eventCategoryService: service.NewEventCategoryService(),
	}
}

// GetCategories 获取事件分类列表
func (h *EventCategoryHandler) GetCategories(c *gin.Context) {
	var req model.EventCategoryListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	categories, total, err := h.eventCategoryService.GetCategories(&req)
	if err != nil {
		response.ServerError(c, "获取事件分类列表失败")
		return
	}

	response.SuccessWithPagination(c, req.Page, req.PageSize, total, categories)
}

// GetCategory 获取单个事件分类
func (h *EventCategoryHandler) GetCategory(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "分类ID不能为空")
		return
	}

	category, err := h.eventCategoryService.GetCategory(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			response.NotFound(c, "事件分类不存在")
		} else {
			response.ServerError(c, "获取事件分类失败")
		}
		return
	}

	response.Success(c, category)
}

// CreateCategory 创建事件分类
func (h *EventCategoryHandler) CreateCategory(c *gin.Context) {
	var req model.EventCategoryCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	category, err := h.eventCategoryService.CreateCategory(&req)
	if err != nil {
		if strings.Contains(err.Error(), "already exists") {
			response.Error(c, http.StatusConflict, "分类名称已存在")
		} else if strings.Contains(err.Error(), "not found") {
			response.BadRequest(c, "负责人不存在")
		} else {
			response.ServerError(c, "创建事件分类失败")
		}
		return
	}

	response.SuccessWithMessage(c, "创建事件分类成功", category)
}

// UpdateCategory 更新事件分类
func (h *EventCategoryHandler) UpdateCategory(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "分类ID不能为空")
		return
	}

	var req model.EventCategoryUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	category, err := h.eventCategoryService.UpdateCategory(id, &req)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			response.NotFound(c, "事件分类不存在")
		} else if strings.Contains(err.Error(), "already exists") {
			response.Error(c, http.StatusConflict, "分类名称已存在")
		} else {
			response.ServerError(c, "更新事件分类失败")
		}
		return
	}

	response.SuccessWithMessage(c, "更新事件分类成功", category)
}

// DeleteCategory 删除事件分类
func (h *EventCategoryHandler) DeleteCategory(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "分类ID不能为空")
		return
	}

	err := h.eventCategoryService.DeleteCategory(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			response.NotFound(c, "事件分类不存在")
		} else if strings.Contains(err.Error(), "related event operations") {
			response.Error(c, http.StatusConflict, "该分类下存在事件记录，无法删除")
		} else {
			response.ServerError(c, "删除事件分类失败")
		}
		return
	}

	response.SuccessWithMessage(c, "删除事件分类成功", nil)
}

// BatchDeleteCategories 批量删除事件分类
func (h *EventCategoryHandler) BatchDeleteCategories(c *gin.Context) {
	var req struct {
		IDs []string `json:"ids" binding:"required,min=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	err := h.eventCategoryService.BatchDeleteCategories(req.IDs)
	if err != nil {
		if strings.Contains(err.Error(), "related event operations") {
			response.Error(c, http.StatusConflict, "部分分类下存在事件记录，无法删除")
		} else {
			response.ServerError(c, "批量删除事件分类失败")
		}
		return
	}

	response.SuccessWithMessage(c, "批量删除事件分类成功", gin.H{
		"deletedCount": len(req.IDs),
	})
}

// GetCategoryOptions 获取事件分类选项
func (h *EventCategoryHandler) GetCategoryOptions(c *gin.Context) {
	options, err := h.eventCategoryService.GetCategoryOptions()
	if err != nil {
		response.ServerError(c, "获取分类选项失败")
		return
	}

	response.Success(c, gin.H{
		"options": options,
	})
}
