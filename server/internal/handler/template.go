package handler

import (
	"sec-flow-server/internal/model"
	"sec-flow-server/internal/service"
	"sec-flow-server/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

// TemplateHandler 模板处理器
type TemplateHandler struct {
	templateService *service.TemplateService
}

// NewTemplateHandler 创建模板处理器
func NewTemplateHandler() *TemplateHandler {
	return &TemplateHandler{
		templateService: service.NewTemplateService(),
	}
}

// GetTemplates 获取模板列表
func (h *TemplateHandler) GetTemplates(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>ult<PERSON>("pageSize", "10"))
	category := c.Query("category")

	templates, total, err := h.templateService.GetTemplates(page, pageSize, category)
	if err != nil {
		response.ServerError(c, "Failed to get templates")
		return
	}

	response.SuccessWithPagination(c, page, pageSize, total, templates)
}

// GetTemplate 获取单个模板
func (h *TemplateHandler) GetTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "Template ID is required")
		return
	}

	template, err := h.templateService.GetTemplate(id)
	if err != nil {
		response.NotFound(c, "Template not found")
		return
	}

	response.Success(c, template)
}

// CreateTemplate 创建模板
func (h *TemplateHandler) CreateTemplate(c *gin.Context) {
	var req model.CreateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request parameters")
		return
	}

	template, err := h.templateService.CreateTemplate(&req)
	if err != nil {
		response.ServerError(c, "Failed to create template")
		return
	}

	response.SuccessWithMessage(c, "Template created successfully", template)
}

// UpdateTemplate 更新模板
func (h *TemplateHandler) UpdateTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "Template ID is required")
		return
	}

	var req model.UpdateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request parameters")
		return
	}

	template, err := h.templateService.UpdateTemplate(id, &req)
	if err != nil {
		response.ServerError(c, "Failed to update template")
		return
	}

	response.SuccessWithMessage(c, "Template updated successfully", template)
}

// DeleteTemplate 删除模板
func (h *TemplateHandler) DeleteTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "Template ID is required")
		return
	}

	err := h.templateService.DeleteTemplate(id)
	if err != nil {
		response.ServerError(c, "Failed to delete template")
		return
	}

	response.SuccessWithMessage(c, "Template deleted successfully", nil)
}
