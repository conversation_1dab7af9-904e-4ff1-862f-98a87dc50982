package handler

import (
	"sec-flow-server/internal/model"
	"sec-flow-server/internal/service"
	"sec-flow-server/pkg/response"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// EventOperationHandler 事件运营处理器
type EventOperationHandler struct {
	eventOperationService *service.EventOperationService
}

// NewEventOperationHandler 创建事件运营处理器
func NewEventOperationHandler() *EventOperationHandler {
	return &EventOperationHandler{
		eventOperationService: service.NewEventOperationService(),
	}
}

// GetOperations 获取事件运营列表
func (h *EventOperationHandler) GetOperations(c *gin.Context) {
	var req model.EventOperationListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	operations, total, err := h.eventOperationService.GetOperations(&req)
	if err != nil {
		response.ServerError(c, "获取事件运营列表失败")
		return
	}

	response.SuccessWithPagination(c, req.Page, req.PageSize, total, operations)
}

// GetOperation 获取单个事件运营
func (h *EventOperationHandler) GetOperation(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "事件ID不能为空")
		return
	}

	operation, err := h.eventOperationService.GetOperation(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			response.NotFound(c, "事件运营记录不存在")
		} else {
			response.ServerError(c, "获取事件运营记录失败")
		}
		return
	}

	response.Success(c, operation)
}

// CreateOperation 创建事件运营
func (h *EventOperationHandler) CreateOperation(c *gin.Context) {
	var req model.EventOperationCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	operation, err := h.eventOperationService.CreateOperation(&req)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			response.BadRequest(c, "关联数据不存在")
		} else {
			response.ServerError(c, "创建事件运营记录失败")
		}
		return
	}

	response.SuccessWithMessage(c, "创建事件运营记录成功", operation)
}

// UpdateOperation 更新事件运营
func (h *EventOperationHandler) UpdateOperation(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "事件ID不能为空")
		return
	}

	var req model.EventOperationUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	operation, err := h.eventOperationService.UpdateOperation(id, &req)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			response.NotFound(c, "事件运营记录不存在")
		} else {
			response.ServerError(c, "更新事件运营记录失败")
		}
		return
	}

	response.SuccessWithMessage(c, "更新事件运营记录成功", operation)
}

// DeleteOperation 删除事件运营
func (h *EventOperationHandler) DeleteOperation(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "事件ID不能为空")
		return
	}

	err := h.eventOperationService.DeleteOperation(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			response.NotFound(c, "事件运营记录不存在")
		} else {
			response.ServerError(c, "删除事件运营记录失败")
		}
		return
	}

	response.SuccessWithMessage(c, "删除事件运营记录成功", nil)
}

// BatchDeleteOperations 批量删除事件运营
func (h *EventOperationHandler) BatchDeleteOperations(c *gin.Context) {
	var req struct {
		IDs []string `json:"ids" binding:"required,min=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	err := h.eventOperationService.BatchDeleteOperations(req.IDs)
	if err != nil {
		response.ServerError(c, "批量删除事件运营记录失败")
		return
	}

	response.SuccessWithMessage(c, "批量删除事件运营记录成功", gin.H{
		"deletedCount": len(req.IDs),
	})
}

// BatchUpdateOperations 批量更新事件运营
func (h *EventOperationHandler) BatchUpdateOperations(c *gin.Context) {
	var req model.EventOperationBatchUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	err := h.eventOperationService.BatchUpdateOperations(&req)
	if err != nil {
		response.ServerError(c, "批量更新事件运营记录失败")
		return
	}

	response.SuccessWithMessage(c, "批量更新事件运营记录成功", gin.H{
		"updatedCount": len(req.IDs),
	})
}

// SearchEmployees 搜索员工
func (h *EventOperationHandler) SearchEmployees(c *gin.Context) {
	keyword := c.Query("keyword")
	limitStr := c.DefaultQuery("limit", "10")
	
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 10
	}

	employees, err := h.eventOperationService.SearchEmployees(keyword, limit)
	if err != nil {
		response.ServerError(c, "搜索员工失败")
		return
	}

	response.Success(c, gin.H{
		"employees": employees,
	})
}

// GetProcessTypeOptions 获取事件处理分类选项
func (h *EventOperationHandler) GetProcessTypeOptions(c *gin.Context) {
	options := model.GetEventProcessTypeOptions()
	response.Success(c, gin.H{
		"options": options,
	})
}
