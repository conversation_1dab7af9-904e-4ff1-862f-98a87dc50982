package handler

import (
	"sec-flow-server/internal/service"
	"sec-flow-server/pkg/response"

	"github.com/gin-gonic/gin"
)

// RoleHandler 角色处理器
type RoleHandler struct {
	roleService *service.RoleService
}

// NewRoleHandler 创建角色处理器
func NewRoleHandler() *RoleHandler {
	return &RoleHandler{
		roleService: service.NewRoleService(),
	}
}

// GetRoles 获取角色列表
func (h *RoleHandler) GetRoles(c *gin.Context) {
	var req service.RoleListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	roles, total, err := h.roleService.GetRoles(&req)
	if err != nil {
		response.ServerError(c, "获取角色列表失败")
		return
	}

	response.Success(c, gin.H{
		"list":     roles,
		"total":    total,
		"page":     req.Page,
		"pageSize": req.PageSize,
	})
}

// GetRole 获取单个角色
func (h *RoleHandler) GetRole(c *gin.Context) {
	roleID := c.Param("id")
	if roleID == "" {
		response.BadRequest(c, "角色ID不能为空")
		return
	}

	role, err := h.roleService.GetRole(roleID)
	if err != nil {
		response.NotFound(c, err.Error())
		return
	}

	response.Success(c, role)
}

// CreateRole 创建角色
func (h *RoleHandler) CreateRole(c *gin.Context) {
	var req service.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	role, err := h.roleService.CreateRole(&req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMessage(c, "角色创建成功", role)
}

// UpdateRole 更新角色
func (h *RoleHandler) UpdateRole(c *gin.Context) {
	roleID := c.Param("id")
	if roleID == "" {
		response.BadRequest(c, "角色ID不能为空")
		return
	}

	var req service.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	role, err := h.roleService.UpdateRole(roleID, &req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMessage(c, "角色更新成功", role)
}

// DeleteRole 删除角色
func (h *RoleHandler) DeleteRole(c *gin.Context) {
	roleID := c.Param("id")
	if roleID == "" {
		response.BadRequest(c, "角色ID不能为空")
		return
	}

	err := h.roleService.DeleteRole(roleID)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMessage(c, "角色删除成功", nil)
}

// GetAllRoles 获取所有角色（用于下拉选择）
func (h *RoleHandler) GetAllRoles(c *gin.Context) {
	roles, err := h.roleService.GetAllRoles()
	if err != nil {
		response.ServerError(c, "获取角色列表失败")
		return
	}

	response.Success(c, roles)
}
