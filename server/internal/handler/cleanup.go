package handler

import (
	"fmt"
	"sec-flow-server/internal/scheduler"
	"sec-flow-server/internal/service"
	"sec-flow-server/pkg/response"

	"github.com/gin-gonic/gin"
)

// CleanupHandler 清理管理处理器
type CleanupHandler struct {
	cleanupScheduler *scheduler.CleanupScheduler
	executionService *service.FlowExecutionService
}

// NewCleanupHandler 创建清理管理处理器
func NewCleanupHandler(cleanupScheduler *scheduler.CleanupScheduler) *CleanupHandler {
	return &CleanupHandler{
		cleanupScheduler: cleanupScheduler,
		executionService: service.NewFlowExecutionService(),
	}
}

// GetCleanupStatistics 获取清理统计信息
func (h *CleanupHandler) GetCleanupStatistics(c *gin.Context) {
	stats, err := h.cleanupScheduler.GetCleanupStatistics()
	if err != nil {
		response.ServerError(c, "Failed to get cleanup statistics")
		return
	}

	// 添加调度器状态信息
	result := gin.H{
		"statistics":      stats,
		"schedulerStatus": h.cleanupScheduler.IsRunning(),
		"nextCleanupTime": h.cleanupScheduler.GetNextCleanupTime(),
	}

	response.Success(c, result)
}

// RunCleanupNow 立即执行清理任务
func (h *CleanupHandler) RunCleanupNow(c *gin.Context) {
	err := h.cleanupScheduler.RunCleanupNow()
	if err != nil {
		response.ServerError(c, "Failed to run cleanup task")
		return
	}

	response.SuccessWithMessage(c, "Cleanup task executed successfully", nil)
}

// FixStuckExecutions 修复卡住的执行记录
func (h *CleanupHandler) FixStuckExecutions(c *gin.Context) {
	fixedCount, err := h.executionService.FixStuckExecutions()
	if err != nil {
		response.ServerError(c, "Failed to fix stuck executions")
		return
	}

	response.SuccessWithMessage(c, fmt.Sprintf("Fixed %d stuck executions", fixedCount), gin.H{
		"fixedCount": fixedCount,
	})
}

// GetStuckExecutionsCount 获取卡住的执行记录数量
func (h *CleanupHandler) GetStuckExecutionsCount(c *gin.Context) {
	count, err := h.executionService.GetStuckExecutionsCount()
	if err != nil {
		response.ServerError(c, "Failed to get stuck executions count")
		return
	}

	response.Success(c, gin.H{
		"stuckCount": count,
		"message":    fmt.Sprintf("Found %d stuck executions", count),
	})
}

// GetCleanupStatus 获取清理调度器状态
func (h *CleanupHandler) GetCleanupStatus(c *gin.Context) {
	status := gin.H{
		"isRunning":       h.cleanupScheduler.IsRunning(),
		"nextCleanupTime": h.cleanupScheduler.GetNextCleanupTime(),
	}

	response.Success(c, status)
}
