package handler

import (
	"fmt"
	"sec-flow-server/internal/constants"
	"sec-flow-server/internal/flow"
	"sec-flow-server/internal/model"
	"sec-flow-server/internal/service"
	"sec-flow-server/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

// FlowHandler 流程图处理器
type FlowHandler struct {
	flowService    *service.FlowService
	flowExecutor   *flow.FlowExecutor
	exampleService *flow.ExampleService
}

// NewFlowHandler 创建流程图处理器
func NewFlowHandler() *FlowHandler {
	flowService := service.NewFlowService()
	return &FlowHandler{
		flowService:    flowService,
		flowExecutor:   flow.NewFlowExecutor(flowService),
		exampleService: flow.NewExampleService(flowService),
	}
}

// GetFlows 获取流程图列表
func (h *FlowHandler) GetFlows(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.<PERSON><PERSON><PERSON><PERSON>y("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("pageSize", "10"))
	status := c.Query("status")
	keyword := c.Query("keyword")
	description := c.Query("description")

	flows, total, err := h.flowService.GetFlows(page, pageSize, status, keyword, description)
	if err != nil {
		response.ServerError(c, "Failed to get flows")
		return
	}

	response.SuccessWithPagination(c, page, pageSize, total, flows)
}

// GetFlowsWithExecutions 获取带最新执行记录的流程列表
func (h *FlowHandler) GetFlowsWithExecutions(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	status := c.Query("status")
	keyword := c.Query("keyword")
	description := c.Query("description")

	flows, total, err := h.flowService.GetFlowsWithLastExecution(page, pageSize, status, keyword, description)
	if err != nil {
		response.ServerError(c, "Failed to get flows with executions")
		return
	}

	response.SuccessWithPagination(c, page, pageSize, total, flows)
}

// GetFlow 获取单个流程图
func (h *FlowHandler) GetFlow(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "Flow ID is required")
		return
	}

	flow, err := h.flowService.GetFlow(id)
	if err != nil {
		response.NotFound(c, "Flow not found")
		return
	}

	response.Success(c, flow)
}

// CreateFlow 创建流程图
func (h *FlowHandler) CreateFlow(c *gin.Context) {
	var req model.CreateFlowRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request parameters")
		return
	}

	flow, err := h.flowService.CreateFlow(&req)
	if err != nil {
		response.ServerError(c, "Failed to create flow")
		return
	}

	response.SuccessWithMessage(c, "Flow created successfully", flow)
}

// UpdateFlow 更新流程图
func (h *FlowHandler) UpdateFlow(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "Flow ID is required")
		return
	}

	var req model.UpdateFlowRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request parameters")
		return
	}

	flow, err := h.flowService.UpdateFlow(id, &req)
	if err != nil {
		response.ServerError(c, "Failed to update flow")
		return
	}

	response.SuccessWithMessage(c, "Flow updated successfully", flow)
}

// DeleteFlow 删除流程图
func (h *FlowHandler) DeleteFlow(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "Flow ID is required")
		return
	}

	err := h.flowService.DeleteFlow(id)
	if err != nil {
		response.ServerError(c, "Failed to delete flow")
		return
	}

	response.SuccessWithMessage(c, "Flow deleted successfully", nil)
}

// ExecuteFlow 执行流程
func (h *FlowHandler) ExecuteFlow(c *gin.Context) {
	flowID := c.Param("id")
	if flowID == "" {
		response.BadRequest(c, "Flow ID is required")
		return
	}

	var reqBody struct {
		StartNodeID string                 `json:"startNodeId,omitempty"`
		InputData   map[string]interface{} `json:"inputData,omitempty"`
	}
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		response.BadRequest(c, "Invalid request parameters")
		return
	}

	// 构造执行请求
	req := flow.ExecutionRequest{
		FlowID:      flowID,
		StartNodeID: reqBody.StartNodeID,
		InputData:   reqBody.InputData,
	}

	// 执行流程并记录
	result, err := h.flowExecutor.ExecuteFlowWithRecord(
		c.Request.Context(),
		&req,
		constants.TriggerTypeManual,
		"user-manual", // 实际项目中应该从上下文获取用户ID
	)
	if err != nil {
		response.ServerError(c, fmt.Sprintf("Failed to execute flow: %v", err))
		return
	}

	response.SuccessWithMessage(c, "Flow execution completed", result)
}

// GetFlowExample 获取流程示例数据
func (h *FlowHandler) GetFlowExample(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "Flow ID is required")
		return
	}

	// 生成示例数据
	result, err := h.exampleService.GenerateFlowExample(id)
	if err != nil {
		response.ServerError(c, fmt.Sprintf("Failed to generate flow example: %v", err))
		return
	}

	response.SuccessWithMessage(c, "Flow example generated successfully", result)
}

// PreviewFlow 预览流程数据
func (h *FlowHandler) PreviewFlow(c *gin.Context) {
	var req struct {
		FlowData interface{} `json:"flowData" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, fmt.Sprintf("Invalid request: %v", err))
		return
	}

	// 生成预览数据
	result, err := h.exampleService.GenerateFlowPreview(req.FlowData)
	if err != nil {
		response.ServerError(c, fmt.Sprintf("Failed to generate flow preview: %v", err))
		return
	}

	response.SuccessWithMessage(c, "Flow preview generated successfully", result)
}
