package handler

import (
	"net/http"
	"sec-flow-server/internal/scheduler"
	"sec-flow-server/pkg/response"

	"github.com/gin-gonic/gin"
)

// SchedulerHandler 调度器处理器
type SchedulerHandler struct {
	scheduler *scheduler.FlowScheduler
}

// NewSchedulerHandler 创建调度器处理器
func NewSchedulerHandler(scheduler *scheduler.FlowScheduler) *SchedulerHandler {
	return &SchedulerHandler{
		scheduler: scheduler,
	}
}

// GetScheduledFlows 获取当前计划的流程
func (h *SchedulerHandler) GetScheduledFlows(c *gin.Context) {
	scheduledFlows := h.scheduler.GetScheduledFlows()

	response.Success(c, gin.H{
		"scheduledFlows": scheduledFlows,
		"count":          len(scheduledFlows),
		"message":        "获取计划流程成功",
	})
}

// TriggerPreprocess 手动触发预处理（用于调试）
func (h *SchedulerHandler) TriggerPreprocess(c *gin.Context) {
	// 这个方法需要在调度器中添加
	c.JSON(http.StatusOK, gin.H{
		"message": "手动预处理功能待实现",
	})
}
