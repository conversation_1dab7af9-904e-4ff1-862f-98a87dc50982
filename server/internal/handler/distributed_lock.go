package handler

import (
	"sec-flow-server/internal/service"
	"sec-flow-server/pkg/response"

	"github.com/gin-gonic/gin"
)

// DistributedLockHandler 分布式锁处理器
type DistributedLockHandler struct {
	lockService *service.DistributedLockService
}

// NewDistributedLockHandler 创建分布式锁处理器
func NewDistributedLockHandler(lockService *service.DistributedLockService) *DistributedLockHandler {
	return &DistributedLockHandler{
		lockService: lockService,
	}
}

// GetActiveLocks 获取当前实例的活跃锁
func (h *DistributedLockHandler) GetActiveLocks(c *gin.Context) {
	locks, err := h.lockService.GetActiveLocks()
	if err != nil {
		response.ServerError(c, "获取活跃锁失败: "+err.Error())
		return
	}

	result := map[string]interface{}{
		"instanceId": h.lockService.GetInstanceID(),
		"locks":      locks,
		"count":      len(locks),
	}

	response.SuccessWithMessage(c, "获取活跃锁成功", result)
}

// GetLockStatus 检查指定锁的状态
func (h *DistributedLockHandler) GetLockStatus(c *gin.Context) {
	lockType := c.Query("lockType")
	resourceID := c.Query("resourceId")

	if lockType == "" || resourceID == "" {
		response.BadRequest(c, "lockType和resourceId不能为空")
		return
	}

	isHeld, instanceID, err := h.lockService.IsLockHeld(lockType, resourceID)
	if err != nil {
		response.ServerError(c, "检查锁状态失败: "+err.Error())
		return
	}

	result := map[string]interface{}{
		"lockType":   lockType,
		"resourceId": resourceID,
		"isHeld":     isHeld,
		"instanceId": instanceID,
	}

	response.SuccessWithMessage(c, "检查锁状态成功", result)
}
