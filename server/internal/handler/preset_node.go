package handler

import (
	"fmt"
	"sec-flow-server/internal/node"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/pkg/response"
	"time"

	"github.com/gin-gonic/gin"
)

// PresetNodeHandler 预置节点处理器
type PresetNodeHandler struct {
	nodeRegistry *node.Registry
}

// NewPresetNodeHandler 创建预置节点处理器
func NewPresetNodeHandler() *PresetNodeHandler {
	return &PresetNodeHandler{
		nodeRegistry: node.GetGlobalRegistry(),
	}
}

// GetPresetNodes 获取预置节点列表
func (h *PresetNodeHandler) GetPresetNodes(c *gin.Context) {
	category := c.Query("category")

	var configs []*base.NodeConfig
	if category != "" {
		// 根据分类过滤
		nodes := h.nodeRegistry.GetNodesByCategory(category)
		configs = make([]*base.NodeConfig, len(nodes))
		for i, n := range nodes {
			configs[i] = n.GetConfig()
		}
	} else {
		// 获取所有节点配置
		configs = h.nodeRegistry.GetNodeConfigs()
	}

	response.Success(c, configs)
}

// GetPresetNode 获取单个预置节点详情
func (h *PresetNodeHandler) GetPresetNode(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "Preset node ID is required")
		return
	}

	config, err := h.nodeRegistry.GetNodeConfig(id)
	if err != nil {
		response.NotFound(c, "Preset node not found")
		return
	}

	response.Success(c, config)
}

// CreatePresetNodeInstance 创建预置节点实例
func (h *PresetNodeHandler) CreatePresetNodeInstance(c *gin.Context) {
	var req struct {
		PresetNodeID string                 `json:"presetNodeId" binding:"required"`
		X            float64                `json:"x" binding:"required"`
		Y            float64                `json:"y" binding:"required"`
		FormData     map[string]interface{} `json:"formData"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request parameters")
		return
	}

	// 验证节点是否存在
	_, err := h.nodeRegistry.GetNode(req.PresetNodeID)
	if err != nil {
		response.NotFound(c, "Preset node not found")
		return
	}

	// 创建节点实例（这里可以添加更多逻辑，比如保存到数据库）
	instance := map[string]interface{}{
		"id":           fmt.Sprintf("instance_%d", time.Now().UnixNano()),
		"presetNodeId": req.PresetNodeID,
		"x":            req.X,
		"y":            req.Y,
		"formData":     req.FormData,
	}

	response.SuccessWithMessage(c, "Preset node instance created successfully", instance)
}
