package handler

import (
	"sec-flow-server/internal/service"
	"sec-flow-server/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

// FlowExecutionHandler 流程执行记录处理器
type FlowExecutionHandler struct {
	executionService *service.FlowExecutionService
}

// NewFlowExecutionHandler 创建流程执行记录处理器
func NewFlowExecutionHandler() *FlowExecutionHandler {
	return &FlowExecutionHandler{
		executionService: service.NewFlowExecutionService(),
	}
}

// GetExecutions 获取执行记录列表
func (h *FlowExecutionHandler) GetExecutions(c *gin.Context) {
	flowID := c.Query("flowId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.Default<PERSON>uery("pageSize", "10"))

	executions, total, err := h.executionService.GetExecutions(flowID, page, pageSize)
	if err != nil {
		response.ServerError(c, "Failed to get executions")
		return
	}

	response.SuccessWithPagination(c, page, pageSize, total, executions)
}

// GetExecution 获取执行记录详情
func (h *FlowExecutionHandler) GetExecution(c *gin.Context) {
	executionID := c.Param("id")
	if executionID == "" {
		response.BadRequest(c, "Execution ID is required")
		return
	}

	execution, err := h.executionService.GetExecution(executionID)
	if err != nil {
		if err.Error() == "execution record not found" {
			response.NotFound(c, "Execution record not found")
		} else {
			response.ServerError(c, "Failed to get execution record")
		}
		return
	}

	response.Success(c, execution)
}

// GetFlowExecutions 获取指定流程的执行记录列表
func (h *FlowExecutionHandler) GetFlowExecutions(c *gin.Context) {
	flowID := c.Param("flowId")
	if flowID == "" {
		response.BadRequest(c, "Flow ID is required")
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	executions, total, err := h.executionService.GetExecutions(flowID, page, pageSize)
	if err != nil {
		response.ServerError(c, "Failed to get flow executions")
		return
	}

	response.SuccessWithPagination(c, page, pageSize, total, executions)
}

// DeleteFlowExecutions 删除指定流程的所有执行记录
func (h *FlowExecutionHandler) DeleteFlowExecutions(c *gin.Context) {
	flowID := c.Param("flowId")
	if flowID == "" {
		response.BadRequest(c, "Flow ID is required")
		return
	}

	err := h.executionService.DeleteExecutions(flowID)
	if err != nil {
		response.ServerError(c, "Failed to delete flow executions")
		return
	}

	response.SuccessWithMessage(c, "Flow executions deleted successfully", nil)
}
