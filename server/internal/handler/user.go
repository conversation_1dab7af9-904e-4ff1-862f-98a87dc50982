package handler

import (
	"sec-flow-server/internal/model"
	"sec-flow-server/internal/service"
	"sec-flow-server/pkg/response"

	"github.com/gin-gonic/gin"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService *service.UserService
}

// NewUserHandler 创建用户处理器
func NewUserHandler() *UserHandler {
	return &UserHandler{
		userService: service.NewUserService(),
	}
}

// GetProfile 获取当前用户信息
func (h *UserHandler) GetProfile(c *gin.Context) {
	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	userWithPermissions, err := h.userService.GetUserWithPermissions(userID.(string))
	if err != nil {
		response.NotFound(c, "用户不存在")
		return
	}

	response.Success(c, userWithPermissions)
}

// UpdateProfile 更新当前用户信息
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	// 从上下文中获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	var req model.UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	user, err := h.userService.UpdateProfile(userID.(string), &req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMessage(c, "用户信息更新成功", user)
}

// GetUsers 获取用户列表（需要管理员权限）
func (h *UserHandler) GetUsers(c *gin.Context) {
	var req model.UserListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	users, total, err := h.userService.GetUsers(&req)
	if err != nil {
		response.ServerError(c, "获取用户列表失败")
		return
	}

	response.Success(c, gin.H{
		"list":     users,
		"total":    total,
		"page":     req.Page,
		"pageSize": req.PageSize,
	})
}

// GetUser 获取指定用户信息（需要管理员权限）
func (h *UserHandler) GetUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		response.BadRequest(c, "用户ID不能为空")
		return
	}

	user, err := h.userService.GetProfile(userID)
	if err != nil {
		response.NotFound(c, "用户不存在")
		return
	}

	response.Success(c, user)
}

// CreateUser 创建用户（需要管理员权限）
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req model.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	user, err := h.userService.CreateUser(&req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMessage(c, "用户创建成功", user)
}

// UpdateUser 更新用户信息（需要管理员权限）
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		response.BadRequest(c, "用户ID不能为空")
		return
	}

	var req model.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	user, err := h.userService.UpdateUser(userID, &req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMessage(c, "用户更新成功", user)
}

// DeleteUser 删除用户（需要管理员权限）
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		response.BadRequest(c, "用户ID不能为空")
		return
	}

	// 不能删除自己
	currentUserID, _ := c.Get("userID")
	if userID == currentUserID {
		response.BadRequest(c, "不能删除自己")
		return
	}

	err := h.userService.DeleteUser(userID)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMessage(c, "用户删除成功", nil)
}

// GetUserPermissions 获取用户权限列表
func (h *UserHandler) GetUserPermissions(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		// 如果没有提供用户ID，获取当前用户的权限
		currentUserID, exists := c.Get("userID")
		if !exists {
			response.Unauthorized(c, "用户未认证")
			return
		}
		userID = currentUserID.(string)
	}

	permissions, err := h.userService.GetUserPermissions(userID)
	if err != nil {
		response.ServerError(c, "获取用户权限失败")
		return
	}

	response.Success(c, permissions)
}
