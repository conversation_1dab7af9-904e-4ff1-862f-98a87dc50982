package handler

import (
	"sec-flow-server/internal/service"
	"sec-flow-server/pkg/response"

	"github.com/gin-gonic/gin"
)

// PermissionHandler 权限处理器
type PermissionHandler struct {
	permissionService *service.PermissionService
}

// NewPermissionHandler 创建权限处理器
func NewPermissionHandler() *PermissionHandler {
	return &PermissionHandler{
		permissionService: service.NewPermissionService(),
	}
}

// GetPermissions 获取权限列表
func (h *PermissionHandler) GetPermissions(c *gin.Context) {
	var req service.PermissionListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	permissions, total, err := h.permissionService.GetPermissions(&req)
	if err != nil {
		response.ServerError(c, "获取权限列表失败")
		return
	}

	response.Success(c, gin.H{
		"list":     permissions,
		"total":    total,
		"page":     req.Page,
		"pageSize": req.PageSize,
	})
}

// GetPermission 获取单个权限
func (h *PermissionHandler) GetPermission(c *gin.Context) {
	permissionID := c.Param("id")
	if permissionID == "" {
		response.BadRequest(c, "权限ID不能为空")
		return
	}

	permission, err := h.permissionService.GetPermission(permissionID)
	if err != nil {
		response.NotFound(c, err.Error())
		return
	}

	response.Success(c, permission)
}

// CreatePermission 创建权限
func (h *PermissionHandler) CreatePermission(c *gin.Context) {
	var req service.CreatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	permission, err := h.permissionService.CreatePermission(&req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMessage(c, "权限创建成功", permission)
}

// UpdatePermission 更新权限
func (h *PermissionHandler) UpdatePermission(c *gin.Context) {
	permissionID := c.Param("id")
	if permissionID == "" {
		response.BadRequest(c, "权限ID不能为空")
		return
	}

	var req service.UpdatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误")
		return
	}

	permission, err := h.permissionService.UpdatePermission(permissionID, &req)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMessage(c, "权限更新成功", permission)
}

// DeletePermission 删除权限
func (h *PermissionHandler) DeletePermission(c *gin.Context) {
	permissionID := c.Param("id")
	if permissionID == "" {
		response.BadRequest(c, "权限ID不能为空")
		return
	}

	err := h.permissionService.DeletePermission(permissionID)
	if err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	response.SuccessWithMessage(c, "权限删除成功", nil)
}

// GetAllPermissions 获取所有权限（用于下拉选择）
func (h *PermissionHandler) GetAllPermissions(c *gin.Context) {
	permissions, err := h.permissionService.GetAllPermissions()
	if err != nil {
		response.ServerError(c, "获取权限列表失败")
		return
	}

	response.Success(c, permissions)
}

// GetPermissionsByResource 根据资源获取权限
func (h *PermissionHandler) GetPermissionsByResource(c *gin.Context) {
	resource := c.Query("resource")
	if resource == "" {
		response.BadRequest(c, "资源名称不能为空")
		return
	}

	permissions, err := h.permissionService.GetPermissionsByResource(resource)
	if err != nil {
		response.ServerError(c, "获取权限列表失败")
		return
	}

	response.Success(c, permissions)
}
