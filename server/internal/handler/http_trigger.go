package handler

import (
	"encoding/json"
	"net/http"
	"sec-flow-server/internal/flow"
	"sec-flow-server/pkg/response"

	"github.com/gin-gonic/gin"
)

// HttpTriggerHandler HTTP触发器处理器
type HttpTriggerHandler struct {
	httpTriggerService *flow.HttpTriggerService
}

// NewHttpTriggerHandler 创建HTTP触发器处理器
func NewHttpTriggerHandler(httpTriggerService *flow.HttpTriggerService) *HttpTriggerHandler {
	return &HttpTriggerHandler{
		httpTriggerService: httpTriggerService,
	}
}

// ExecuteFlow 通过HTTP触发执行流程
func (h *HttpTriggerHandler) ExecuteFlow(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		response.Error(c, http.StatusBadRequest, "缺少触发Key参数")
		return
	}

	// 解析请求体
	var requestData map[string]interface{}
	if err := c.ShouldBindJSON(&requestData); err != nil {
		response.Error(c, http.StatusBadRequest, "请求体格式错误: "+err.Error())
		return
	}

	// 触发流程执行
	result, err := h.httpTriggerService.TriggerFlow(key, requestData)
	if err != nil {
		response.Error(c, http.StatusNotFound, err.Error())
		return
	}

	response.Success(c, gin.H{
		"executionId": result.ExecutionID,
		"flowId":      result.FlowID,
		"status":      result.Status,
		"startTime":   result.StartTime,
		"endTime":     result.EndTime,
		"message":     "流程触发成功",
	})
}

// GetRegisteredTriggers 获取所有注册的HTTP触发器
func (h *HttpTriggerHandler) GetRegisteredTriggers(c *gin.Context) {
	triggers := h.httpTriggerService.GetRegisteredTriggers()

	response.Success(c, gin.H{
		"triggers": triggers,
		"count":    len(triggers),
		"message":  "获取HTTP触发器列表成功",
	})
}

// GetTriggerByKey 根据Key获取触发器信息
func (h *HttpTriggerHandler) GetTriggerByKey(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		response.Error(c, http.StatusBadRequest, "缺少触发Key参数")
		return
	}

	trigger, exists := h.httpTriggerService.GetTriggerByKey(key)
	if !exists {
		response.Error(c, http.StatusNotFound, "未找到指定的HTTP触发器")
		return
	}

	response.Success(c, gin.H{
		"trigger": trigger,
		"message": "获取HTTP触发器信息成功",
	})
}

// RefreshTriggers 手动刷新HTTP触发器映射
func (h *HttpTriggerHandler) RefreshTriggers(c *gin.Context) {
	h.httpTriggerService.RefreshHttpTriggers()

	triggers := h.httpTriggerService.GetRegisteredTriggers()

	response.Success(c, gin.H{
		"triggers": triggers,
		"count":    len(triggers),
		"message":  "HTTP触发器映射刷新成功",
	})
}

// ValidateKey 验证触发Key是否可用
func (h *HttpTriggerHandler) ValidateKey(c *gin.Context) {
	var req struct {
		Key           string `json:"key" binding:"required"`
		ExcludeFlowID string `json:"excludeFlowId"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	err := h.httpTriggerService.ValidateKey(req.Key, req.ExcludeFlowID)
	if err != nil {
		response.Error(c, http.StatusConflict, err.Error())
		return
	}

	response.Success(c, gin.H{
		"key":       req.Key,
		"available": true,
		"message":   "触发Key可用",
	})
}

// TestTrigger 测试HTTP触发器（用于调试）
func (h *HttpTriggerHandler) TestTrigger(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		response.Error(c, http.StatusBadRequest, "缺少触发Key参数")
		return
	}

	// 获取触发器信息
	trigger, exists := h.httpTriggerService.GetTriggerByKey(key)
	if !exists {
		response.Error(c, http.StatusNotFound, "未找到指定的HTTP触发器")
		return
	}

	// 生成测试数据
	var testData map[string]interface{}
	if dataStructure, ok := trigger.NodeConfig["dataStructure"].(string); ok {
		if err := json.Unmarshal([]byte(dataStructure), &testData); err != nil {
			testData = map[string]interface{}{
				"test":      true,
				"message":   "测试数据",
				"timestamp": "2025-01-04T10:30:00Z",
			}
		}
	} else {
		testData = map[string]interface{}{
			"test":      true,
			"message":   "测试数据",
			"timestamp": "2025-01-04T10:30:00Z",
		}
	}

	// 触发流程执行
	result, err := h.httpTriggerService.TriggerFlow(key, testData)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "测试触发失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{
		"executionId": result.ExecutionID,
		"flowId":      result.FlowID,
		"status":      result.Status,
		"testData":    testData,
		"message":     "测试触发成功",
	})
}
