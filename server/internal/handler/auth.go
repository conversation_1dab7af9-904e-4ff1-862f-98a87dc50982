package handler

import (
	"log"
	"sec-flow-server/internal/config"
	"sec-flow-server/internal/service"
	"sec-flow-server/pkg/response"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	userService *service.UserService
	jwtSecret   string
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler() *AuthHandler {
	cfg := config.Get()
	return &AuthHandler{
		userService: service.NewUserService(),
		jwtSecret:   cfg.JWT.Secret,
	}
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
}

// Login 用户登录（仅需要用户名）
func (h *AuthHandler) Login(c *gin.Context) {
	// 仅允许在local环境使用该登录接口
	cfg := config.Get()
	if cfg != nil && cfg.App.Env != "local" {
		response.Forbidden(c, "Login endpoint is only available in local environment")
		return
	}

	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("❌ 登录请求参数错误: %v", err)
		response.BadRequest(c, "请求参数错误")
		return
	}

	log.Printf("🔐 开始处理登录请求，用户名: %s", req.Username)

	// 获取或创建用户
	user, err := h.userService.GetOrCreateUserByUsername(req.Username)
	if err != nil {
		log.Printf("❌ 用户服务返回错误: %v", err)
		response.ServerError(c, "用户认证失败")
		return
	}

	log.Printf("✅ 用户获取成功: %s (ID: %s)", user.Username, user.ID)

	// 生成JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"data": map[string]interface{}{
			"username": user.Username,
			"userId":   user.ID,
		},
		"exp": time.Now().Add(time.Hour * 24 * 7).Unix(), // 7天过期
		"iat": time.Now().Unix(),
	})

	tokenString, err := token.SignedString([]byte(h.jwtSecret))
	if err != nil {
		log.Printf("❌ 生成token失败: %v", err)
		response.ServerError(c, "生成token失败")
		return
	}

	log.Printf("✅ Token生成成功，设置Cookie")

	// 设置Cookie（本地调试用）
	c.SetCookie(
		"secgate_token", // cookie名称
		tokenString,     // cookie值
		7*24*3600,       // 7天过期
		"/",             // path
		"",              // domain
		false,           // secure (开发环境设为false)
		true,            // httpOnly
	)

	log.Printf("🎉 用户 %s 登录成功", user.Username)

	response.SuccessWithMessage(c, "登录成功", gin.H{
		"user":  user,
		"token": tokenString,
	})
}

// Logout 用户登出
func (h *AuthHandler) Logout(c *gin.Context) {
	// 仅允许在local环境使用该登出接口
	cfg := config.Get()
	if cfg != nil && cfg.App.Env != "local" {
		response.Forbidden(c, "Logout endpoint is only available in local environment")
		return
	}

	// 清除Cookie
	c.SetCookie(
		"secgate_token",
		"",
		-1, // 立即过期
		"/",
		"",
		false,
		true,
	)

	response.SuccessWithMessage(c, "登出成功", nil)
}
