package scheduler

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"sec-flow-server/internal/config"
	"sec-flow-server/internal/constants"
	"sec-flow-server/internal/flow"
	"sec-flow-server/internal/model"
	"sec-flow-server/internal/service"
	"sort"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
)

// FlowScheduler 流程调度器
type FlowScheduler struct {
	flowService        *service.FlowService
	flowExecutor       *flow.FlowExecutor
	httpTriggerService *flow.HttpTriggerService
	lockService        *service.DistributedLockService
	scheduledFlows     map[string]*ScheduledFlow // 下一分钟要执行的流程
	assignedFlows      map[string]*ScheduledFlow // 分配给当前实例的流程
	instanceID         string                    // 当前实例ID
	mutex              sync.RWMutex
	ctx                context.Context
	cancel             context.CancelFunc
	ticker             *time.Ticker
}

// ScheduledFlow 计划执行的流程
type ScheduledFlow struct {
	FlowID           string                 `json:"flowId"`
	FlowName         string                 `json:"flowName"`
	CronExpression   string                 `json:"cronExpression"`
	Timezone         string                 `json:"timezone"`
	StartNodeID      string                 `json:"startNodeId"`
	NodeConfig       map[string]interface{} `json:"nodeConfig"`
	ScheduledTime    time.Time              `json:"scheduledTime"`
	AssignedInstance string                 `json:"assignedInstance"` // 分配的实例ID
}

// TaskAssignment 任务分配信息
type TaskAssignment struct {
	ScheduledTime time.Time                   `json:"scheduledTime"`
	Assignments   map[string][]*ScheduledFlow `json:"assignments"` // instanceID -> flows
	CreatedAt     time.Time                   `json:"createdAt"`
}

// InstanceInfo 实例信息
type InstanceInfo struct {
	InstanceID string    `json:"instanceId"`
	LastSeen   time.Time `json:"lastSeen"`
	IsActive   bool      `json:"isActive"`
}

// NewFlowScheduler 创建新的流程调度器
func NewFlowScheduler(flowService *service.FlowService, flowExecutor *flow.FlowExecutor, httpTriggerService *flow.HttpTriggerService, lockService *service.DistributedLockService) *FlowScheduler {
	ctx, cancel := context.WithCancel(context.Background())

	return &FlowScheduler{
		flowService:        flowService,
		flowExecutor:       flowExecutor,
		httpTriggerService: httpTriggerService,
		lockService:        lockService,
		scheduledFlows:     make(map[string]*ScheduledFlow),
		assignedFlows:      make(map[string]*ScheduledFlow),
		instanceID:         lockService.GetInstanceID(),
		ctx:                ctx,
		cancel:             cancel,
	}
}

// Start 启动调度器
func (s *FlowScheduler) Start() {
	log.Println("🕐 启动流程调度器...")

	// 创建定时器，每分钟的30秒执行一次
	s.ticker = time.NewTicker(time.Minute)

	// 立即执行一次预处理和调度
	go s.preprocessAndScheduleNextMinute()

	// 启动心跳更新器
	s.startHeartbeatUpdater()

	// 启动定时任务
	go func() {
		for {
			select {
			case <-s.ctx.Done():
				log.Println("🛑 流程调度器已停止")
				return
			case t := <-s.ticker.C:
				// 在每分钟的30秒执行预处理
				go s.scheduleAtThirtySeconds(t)
			}
		}
	}()

	log.Println("✅ 流程调度器启动成功")
}

// hashString 计算字符串的MD5哈希值
func (s *FlowScheduler) hashString(input string) string {
	hash := md5.Sum([]byte(input))
	return hex.EncodeToString(hash[:])
}

// assignFlowsToInstances 使用一致性哈希将流程分配给实例
func (s *FlowScheduler) assignFlowsToInstances(flows []*ScheduledFlow, instances []string) map[string][]*ScheduledFlow {
	if len(instances) == 0 {
		return make(map[string][]*ScheduledFlow)
	}

	// 排序实例列表确保一致性
	sort.Strings(instances)

	assignments := make(map[string][]*ScheduledFlow)
	for _, instance := range instances {
		assignments[instance] = make([]*ScheduledFlow, 0)
	}

	// 使用一致性哈希分配流程
	for _, flow := range flows {
		// 基于流程ID和流程名称生成哈希（确保不同流程有不同哈希）
		hashInput := fmt.Sprintf("%s:%s", flow.FlowID, flow.FlowName)
		hash := s.hashString(hashInput)

		// 将MD5哈希的前8个字符转换为整数
		hashInt := uint64(0)
		for j := 0; j < 8 && j < len(hash); j++ {
			var digit uint64
			b := hash[j]
			if b >= '0' && b <= '9' {
				digit = uint64(b - '0')
			} else if b >= 'a' && b <= 'f' {
				digit = uint64(b - 'a' + 10)
			}
			hashInt = hashInt*16 + digit
		}

		instanceIndex := int(hashInt) % len(instances)
		selectedInstance := instances[instanceIndex]

		// 设置分配的实例
		flow.AssignedInstance = selectedInstance
		assignments[selectedInstance] = append(assignments[selectedInstance], flow)

		log.Printf("🎯 流程 [%s] 分配给实例: %s (哈希输入: %s, 哈希: %s, 索引: %d/%d)",
			flow.FlowName, selectedInstance, hashInput, hash[:8], instanceIndex, len(instances))
	}

	return assignments
}

// getActiveInstances 获取活跃的实例列表
func (s *FlowScheduler) getActiveInstances() ([]string, error) {
	// 先清理过期的心跳记录
	err := s.lockService.CleanupExpiredHeartbeats()
	if err != nil {
		log.Printf("⚠️ 清理过期心跳失败: %v", err)
	}

	// 更新当前实例的心跳
	err = s.updateInstanceHeartbeat()
	if err != nil {
		log.Printf("⚠️ 更新实例心跳失败: %v", err)
	}

	// 获取所有活跃实例
	cfg := config.Get()
	instances, err := s.lockService.GetActiveInstances(cfg.Scheduler.ActiveInstanceTimeout)
	if err != nil {
		return nil, fmt.Errorf("获取活跃实例失败: %v", err)
	}

	if len(instances) == 0 {
		// 如果没有其他实例，至少包含当前实例
		instances = []string{s.instanceID}
	}

	log.Printf("📋 发现 %d 个活跃实例 (%v内心跳): %v", len(instances), cfg.Scheduler.ActiveInstanceTimeout, instances)
	return instances, nil
}

// updateInstanceHeartbeat 更新实例心跳
func (s *FlowScheduler) updateInstanceHeartbeat() error {
	return s.lockService.UpdateInstanceHeartbeat(s.instanceID)
}

// startHeartbeatUpdater 启动心跳更新器
func (s *FlowScheduler) startHeartbeatUpdater() {
	go func() {
		// 立即发送一次心跳
		if err := s.updateInstanceHeartbeat(); err != nil {
			log.Printf("⚠️ 初始心跳发送失败: %v", err)
		} else {
			log.Printf("💓 实例心跳已启动: %s", s.instanceID)
		}

		// 定期发送心跳
		cfg := config.Get()
		heartbeatTicker := time.NewTicker(cfg.Scheduler.HeartbeatInterval)
		defer heartbeatTicker.Stop()

		for {
			select {
			case <-s.ctx.Done():
				log.Printf("💔 实例心跳已停止: %s", s.instanceID)
				return
			case <-heartbeatTicker.C:
				if err := s.updateInstanceHeartbeat(); err != nil {
					log.Printf("⚠️ 心跳更新失败 [%s]: %v", s.instanceID, err)
				} else {
					log.Printf("💓 心跳更新成功: %s", s.instanceID)
				}
			}
		}
	}()
}

// preprocessAndScheduleNextMinute 预处理并调度下一分钟的流程
func (s *FlowScheduler) preprocessAndScheduleNextMinute() {
	// 执行预处理
	s.preprocessNextMinuteFlows()

	// 计算下一分钟的时间
	nextMinute := time.Now().Truncate(time.Minute).Add(time.Minute)
	waitToExecution := time.Until(nextMinute)

	if waitToExecution > 0 {
		time.Sleep(waitToExecution)
	}

	// 执行计划的流程
	s.executeScheduledFlows()
}

// Stop 停止调度器
func (s *FlowScheduler) Stop() {
	log.Println("🛑 正在停止流程调度器...")

	if s.ticker != nil {
		s.ticker.Stop()
	}

	s.cancel()
	log.Println("✅ 流程调度器已停止")
}

// scheduleAtThirtySeconds 在每分钟的30秒执行预处理
func (s *FlowScheduler) scheduleAtThirtySeconds(currentTime time.Time) {
	// 等待到30秒
	waitDuration := time.Duration(30-currentTime.Second()) * time.Second
	if waitDuration > 0 {
		time.Sleep(waitDuration)
	}

	// 执行预处理
	s.preprocessNextMinuteFlows()

	// 等待到下一分钟的0秒，然后执行流程
	nextMinute := currentTime.Truncate(time.Minute).Add(time.Minute)
	waitToExecution := time.Until(nextMinute)

	if waitToExecution > 0 {
		time.Sleep(waitToExecution)
	}

	// 执行计划的流程
	s.executeScheduledFlows()
}

// preprocessNextMinuteFlows 预处理下一分钟要执行的流程
func (s *FlowScheduler) preprocessNextMinuteFlows() {
	// 计算下一分钟的时间
	nextMinute := time.Now().Truncate(time.Minute).Add(time.Minute)
	lockResourceID := nextMinute.Format("2006-01-02-15:04")

	// 尝试获取预处理锁，只有一个实例能进行预处理
	metadata := &model.LockMetadata{
		ScheduledTime: nextMinute.Format("2006-01-02 15:04:05"),
	}

	_, err := s.lockService.TryLock(model.LockTypeSchedulerPreprocess, lockResourceID, 2*time.Minute, metadata)
	if err != nil {
		log.Printf("🔒 未能获取预处理锁，跳过预处理: %v", err)
		return
	}

	// 确保在函数结束时释放锁
	defer func() {
		if err := s.lockService.ReleaseLock(model.LockTypeSchedulerPreprocess, lockResourceID); err != nil {
			log.Printf("⚠️  释放预处理锁失败: %v", err)
		}
	}()

	log.Printf("🔍 开始预处理下一分钟的流程: %s (实例: %s)",
		nextMinute.Format("15:04"), s.lockService.GetInstanceID())

	// 同时刷新HTTP触发器映射
	if s.httpTriggerService != nil {
		s.httpTriggerService.RefreshHttpTriggers()
	}

	log.Printf("📅 目标执行时间: %s", nextMinute.Format("2006-01-02 15:04:05"))

	// 查询所有已发布的流程
	publishedFlows, err := s.getPublishedFlows()
	if err != nil {
		log.Printf("❌ 获取已发布流程失败: %v", err)
		return
	}

	log.Printf("📋 找到 %d 个已发布的流程", len(publishedFlows))

	// 清空之前的计划
	s.mutex.Lock()
	s.scheduledFlows = make(map[string]*ScheduledFlow)
	s.mutex.Unlock()

	// 筛选符合条件的流程
	var candidateFlows []*ScheduledFlow
	for _, flow := range publishedFlows {
		scheduledFlow := s.checkFlowForScheduling(flow, nextMinute)
		if scheduledFlow != nil {
			candidateFlows = append(candidateFlows, scheduledFlow)
		}
	}

	log.Printf("📋 找到 %d 个需要执行的流程", len(candidateFlows))

	// 获取活跃实例列表
	activeInstances, err := s.getActiveInstances()
	if err != nil {
		log.Printf("❌ 获取活跃实例失败: %v", err)
		return
	}

	// 使用一致性哈希分配任务
	assignments := s.assignFlowsToInstances(candidateFlows, activeInstances)

	// 将分配结果存储到数据库
	err = s.storeTaskAssignments(nextMinute, assignments)
	if err != nil {
		log.Printf("❌ 存储任务分配失败: %v", err)
		return
	}

	// 更新本地调度列表（所有流程，用于统计）
	s.mutex.Lock()
	s.scheduledFlows = make(map[string]*ScheduledFlow)
	for _, flows := range assignments {
		for _, flow := range flows {
			s.scheduledFlows[flow.FlowID] = flow
		}
	}
	s.mutex.Unlock()

	log.Printf("⏰ 预处理完成，总计划执行 %d 个流程，分配给 %d 个实例",
		len(candidateFlows), len(activeInstances))
}

// getPublishedFlows 获取所有已发布的流程
func (s *FlowScheduler) getPublishedFlows() ([]*model.Flow, error) {
	// 使用 FlowService 的状态查询功能
	flowResponses, _, err := s.flowService.GetFlows(1, 1000, constants.FlowStatusPublished.String(), "", "")
	if err != nil {
		return nil, err
	}

	// 转换为 model.Flow 格式
	var publishedFlows []*model.Flow
	for _, flowResp := range flowResponses {
		// 将 FlowData 转换为 JSON 字符串
		dataJSON, err := json.Marshal(flowResp.Data)
		if err != nil {
			log.Printf("❌ 序列化流程数据失败 [%s]: %v", flowResp.Name, err)
			continue
		}

		flow := &model.Flow{
			ID:          flowResp.ID,
			Name:        flowResp.Name,
			Description: flowResp.Description,
			Data:        string(dataJSON),
			Status:      flowResp.Status,
			CreatedAt:   flowResp.CreatedAt,
			UpdatedAt:   flowResp.UpdatedAt,
			CreatedBy:   flowResp.CreatedBy,
		}
		publishedFlows = append(publishedFlows, flow)
	}

	return publishedFlows, nil
}

// checkFlowForScheduling 检查流程是否需要在指定时间执行
func (s *FlowScheduler) checkFlowForScheduling(flow *model.Flow, targetTime time.Time) *ScheduledFlow {
	// 解析流程数据
	var flowData model.FlowData
	if err := json.Unmarshal([]byte(flow.Data), &flowData); err != nil {
		log.Printf("⚠️  解析流程数据失败 [%s]: %v", flow.Name, err)
		return nil
	}

	// 查找 cron_start 节点
	cronStartNode := s.findCronStartNode(flowData.Nodes)
	if cronStartNode == nil {
		return nil
	}

	// 获取 cron 配置
	properties := cronStartNode.Properties
	if properties == nil {
		log.Printf("⚠️  流程 [%s] 的 cron_start 节点缺少属性", flow.Name)
		return nil
	}

	// 尝试从formData中获取配置（新的预置节点格式）
	var cronExpression string
	if formData, exists := properties["formData"].(map[string]interface{}); exists {
		if expr, ok := formData["cronExpression"].(string); ok {
			cronExpression = expr
		}
	} else if config, exists := properties["config"].(map[string]interface{}); exists {
		// 兼容旧的配置格式
		if expr, ok := config["cronExpression"].(string); ok {
			cronExpression = expr
		}
	}

	if cronExpression == "" {
		log.Printf("⚠️  流程 [%s] 的 cron_start 节点缺少 Cron 表达式", flow.Name)
		return nil
	}

	// 验证 Cron 表达式并检查是否匹配
	parser := cron.NewParser(cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow)
	schedule, err := parser.Parse(cronExpression)
	if err != nil {
		log.Printf("⚠️  流程 [%s] 的 Cron 表达式无效: %s, 错误: %v", flow.Name, cronExpression, err)
		return nil
	}

	// 检查目标时间是否匹配 Cron 表达式
	nextExecution := schedule.Next(targetTime.Add(-time.Minute))
	timeDiff := nextExecution.Sub(targetTime).Abs()

	if timeDiff < time.Minute {
		log.Printf("🎯 Cron匹配: %s 在 %s (下次执行: %s, 误差: %s)",
			cronExpression, targetTime.Format("15:04"),
			nextExecution.Format("15:04"), timeDiff)

		scheduledFlow := &ScheduledFlow{
			FlowID:         flow.ID,
			FlowName:       flow.Name,
			CronExpression: cronExpression,
			Timezone:       "Asia/Shanghai", // 默认时区
			StartNodeID:    cronStartNode.ID,
			NodeConfig:     properties,
			ScheduledTime:  targetTime,
		}

		log.Printf("⏰ 计划执行流程 [%s] 在 %s (Cron: %s)",
			flow.Name, targetTime.Format("15:04:05"), cronExpression)

		return scheduledFlow
	}

	return nil
}

// storeTaskAssignments 将任务分配结果存储到数据库
func (s *FlowScheduler) storeTaskAssignments(scheduledTime time.Time, assignments map[string][]*ScheduledFlow) error {
	assignment := &TaskAssignment{
		ScheduledTime: scheduledTime,
		Assignments:   assignments,
		CreatedAt:     time.Now(),
	}

	// 将分配信息序列化为JSON
	assignmentJSON, err := json.Marshal(assignment)
	if err != nil {
		return fmt.Errorf("序列化任务分配失败: %v", err)
	}

	// 存储到分布式锁表中（复用现有表结构）
	lockResourceID := fmt.Sprintf("task_assignment:%s", scheduledTime.Format("2006-01-02-15:04"))
	metadata := &model.LockMetadata{
		TaskAssignment: string(assignmentJSON),
	}

	_, err = s.lockService.TryLock("task_assignment", lockResourceID, 5*time.Minute, metadata)
	if err != nil {
		return fmt.Errorf("存储任务分配失败: %v", err)
	}

	log.Printf("💾 任务分配已存储: %s", lockResourceID)
	return nil
}

// checkAndScheduleFlow 检查流程是否需要在指定时间执行
func (s *FlowScheduler) checkAndScheduleFlow(flow *model.Flow, targetTime time.Time) bool {
	// 解析流程数据
	var flowData model.FlowData
	if err := json.Unmarshal([]byte(flow.Data), &flowData); err != nil {
		log.Printf("❌ 解析流程数据失败 [%s]: %v", flow.Name, err)
		return false
	}

	// 查找 cron_start 节点
	cronStartNode := s.findCronStartNode(flowData.Nodes)
	if cronStartNode == nil {
		// 没有 cron_start 节点，跳过
		return false
	}

	// 检查是否有入边（cron_start 应该是开始节点）
	if s.hasIncomingEdges(cronStartNode.ID, flowData.Edges) {
		// cron_start 节点有入边，不是开始节点，跳过
		return false
	}

	// 获取 cron 配置
	properties := cronStartNode.Properties
	if properties == nil {
		log.Printf("⚠️  流程 [%s] 的 cron_start 节点缺少属性", flow.Name)
		return false
	}

	// 尝试从formData中获取配置（新的预置节点格式）
	var cronExpression string
	if formData, exists := properties["formData"].(map[string]interface{}); exists {
		if expr, ok := formData["cronExpression"].(string); ok {
			cronExpression = expr
		}
	} else if config, exists := properties["config"].(map[string]interface{}); exists {
		// 兼容旧的配置格式
		if expr, ok := config["cronExpression"].(string); ok {
			cronExpression = expr
		}
	}

	if cronExpression == "" {
		log.Printf("⚠️  流程 [%s] 的 cron_start 节点缺少 Cron 表达式", flow.Name)
		return false
	}

	// 固定使用中国时区
	timezone := "Asia/Shanghai"

	// 验证是否匹配执行时间
	if s.shouldExecuteAtTime(cronExpression, timezone, targetTime) {
		// 添加到计划执行列表
		scheduledFlow := &ScheduledFlow{
			FlowID:         flow.ID,
			FlowName:       flow.Name,
			CronExpression: cronExpression,
			Timezone:       timezone,
			StartNodeID:    cronStartNode.ID,
			NodeConfig:     properties,
			ScheduledTime:  targetTime,
		}

		s.mutex.Lock()
		s.scheduledFlows[flow.ID] = scheduledFlow
		s.mutex.Unlock()

		log.Printf("⏰ 计划执行流程 [%s] 在 %s (Cron: %s)",
			flow.Name, targetTime.Format("15:04:05"), cronExpression)
		return true
	}

	return false
}

// findCronStartNode 查找 cron_start 节点
func (s *FlowScheduler) findCronStartNode(nodes []model.Node) *model.Node {
	for i := range nodes {
		// 检查是否是预置节点且presetNodeId为cron_start
		if nodes[i].Type == "CustomHtmlPresetNode" {
			if nodes[i].Properties != nil {
				if presetNodeId, exists := nodes[i].Properties["presetNodeId"].(string); exists && presetNodeId == "cron_start" {
					return &nodes[i]
				}
			}
		}
		// 兼容旧的节点类型
		if nodes[i].Type == "cron_start" {
			return &nodes[i]
		}
	}
	return nil
}

// hasIncomingEdges 检查节点是否有入边
func (s *FlowScheduler) hasIncomingEdges(nodeID string, edges []model.Edge) bool {
	for _, edge := range edges {
		if edge.TargetNodeId == nodeID {
			return true
		}
	}
	return false
}

// shouldExecuteAtTime 检查 Cron 表达式是否匹配指定时间
func (s *FlowScheduler) shouldExecuteAtTime(cronExpression, timezone string, targetTime time.Time) bool {
	// 加载中国时区
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		log.Printf("⚠️  无法加载时区 %s，使用 UTC", timezone)
		loc = time.UTC
	}

	// 转换到指定时区
	targetTimeInTZ := targetTime.In(loc)

	// 解析 Cron 表达式
	parser := cron.NewParser(cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow)
	schedule, err := parser.Parse(cronExpression)
	if err != nil {
		log.Printf("❌ 无效的 Cron 表达式: %s, 错误: %v", cronExpression, err)
		return false
	}

	// 获取目标时间之前的时间点，然后计算下一次执行时间
	beforeTarget := targetTimeInTZ.Add(-time.Minute)
	nextTime := schedule.Next(beforeTarget)

	// 检查下一次执行时间是否与目标时间匹配（允许1分钟误差）
	timeDiff := targetTimeInTZ.Sub(nextTime).Abs()
	shouldExecute := timeDiff <= time.Minute

	if shouldExecute {
		log.Printf("🎯 Cron匹配: %s 在 %s (下次执行: %s, 误差: %v)",
			cronExpression, targetTimeInTZ.Format("15:04"), nextTime.Format("15:04"), timeDiff)
	}

	return shouldExecute
}

// executeScheduledFlows 执行分配给当前实例的流程
func (s *FlowScheduler) executeScheduledFlows() {
	// 获取当前时间的任务分配
	currentTime := time.Now().Truncate(time.Minute)
	assignedFlows, err := s.getAssignedFlows(currentTime)
	if err != nil {
		log.Printf("❌ 获取任务分配失败: %v", err)
		return
	}

	if len(assignedFlows) == 0 {
		log.Printf("📭 当前实例 [%s] 没有分配到流程", s.instanceID)
		return
	}

	log.Printf("🚀 当前实例 [%s] 开始执行 %d 个分配的流程", s.instanceID, len(assignedFlows))

	// 为每个流程启动一个 goroutine
	var wg sync.WaitGroup
	for _, flow := range assignedFlows {
		wg.Add(1)
		go func(sf *ScheduledFlow) {
			defer wg.Done()
			s.executeAssignedFlow(sf)
		}(flow)
	}

	// 等待所有流程执行完成（或设置超时）
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Printf("✅ 实例 [%s] 的所有分配流程执行完成", s.instanceID)
	case <-time.After(5 * time.Minute):
		log.Printf("⚠️  实例 [%s] 流程执行超时，部分流程可能仍在运行", s.instanceID)
	}
}

// getAssignedFlows 获取分配给当前实例的流程
func (s *FlowScheduler) getAssignedFlows(scheduledTime time.Time) ([]*ScheduledFlow, error) {
	// 从数据库获取任务分配信息
	lockResourceID := fmt.Sprintf("task_assignment:%s", scheduledTime.Format("2006-01-02-15:04"))

	lock, err := s.lockService.GetLock("task_assignment", lockResourceID)
	if err != nil {
		return nil, fmt.Errorf("获取任务分配信息失败: %v", err)
	}

	if lock == nil || lock.Metadata == nil || lock.Metadata.TaskAssignment == "" {
		return []*ScheduledFlow{}, nil
	}

	// 解析任务分配信息
	var assignment TaskAssignment
	if err := json.Unmarshal([]byte(lock.Metadata.TaskAssignment), &assignment); err != nil {
		return nil, fmt.Errorf("解析任务分配信息失败: %v", err)
	}

	// 获取分配给当前实例的流程
	assignedFlows := assignment.Assignments[s.instanceID]
	if assignedFlows == nil {
		assignedFlows = []*ScheduledFlow{}
	}

	log.Printf("📋 实例 [%s] 分配到 %d 个流程", s.instanceID, len(assignedFlows))
	return assignedFlows, nil
}

// executeAssignedFlow 执行分配的流程（无需抢锁）
func (s *FlowScheduler) executeAssignedFlow(scheduledFlow *ScheduledFlow) {
	log.Printf("🎯 实例 [%s] 开始执行分配的流程: %s (ID: %s)",
		s.instanceID, scheduledFlow.FlowName, scheduledFlow.FlowID)

	// 构造执行请求
	req := &flow.ExecutionRequest{
		FlowID:      scheduledFlow.FlowID,
		StartNodeID: scheduledFlow.StartNodeID,
		InputData:   nil,
	}

	// 直接执行流程，无需抢锁（因为已经在预处理阶段分配好了）
	result, err := s.flowExecutor.ExecuteFlowWithRecord(
		context.Background(),
		req,
		constants.TriggerTypeCron,
		fmt.Sprintf("scheduler-%s", s.instanceID),
	)
	if err != nil {
		log.Printf("❌ 流程执行失败 [%s]: %v", scheduledFlow.FlowName, err)
		return
	}

	log.Printf("✅ 流程执行完成 [%s]: 实例=%s, 状态=%s, 执行ID=%s",
		scheduledFlow.FlowName, s.instanceID, result.Status, result.ExecutionID)
}

// executeFlow 执行单个流程
func (s *FlowScheduler) executeFlow(scheduledFlow *ScheduledFlow) {
	// 尝试获取流程执行锁
	lockResourceID := fmt.Sprintf("%s-%s", scheduledFlow.FlowID, scheduledFlow.ScheduledTime.Format("2006-01-02-15:04"))

	metadata := &model.LockMetadata{
		FlowName:       scheduledFlow.FlowName,
		ScheduledTime:  scheduledFlow.ScheduledTime.Format("2006-01-02 15:04:05"),
		CronExpression: scheduledFlow.CronExpression,
	}

	_, err := s.lockService.TryLock(model.LockTypeFlowExecution, lockResourceID, 10*time.Minute, metadata)
	if err != nil {
		log.Printf("🔒 未能获取流程执行锁，跳过执行 [%s]: %v", scheduledFlow.FlowName, err)
		return
	}

	// 确保在函数结束时释放锁
	defer func() {
		if err := s.lockService.ReleaseLock(model.LockTypeFlowExecution, lockResourceID); err != nil {
			log.Printf("⚠️  释放流程执行锁失败 [%s]: %v", scheduledFlow.FlowName, err)
		}
	}()

	log.Printf("🎯 开始执行流程: %s (ID: %s, 实例: %s)",
		scheduledFlow.FlowName, scheduledFlow.FlowID, s.lockService.GetInstanceID())

	// 创建执行请求
	req := &flow.ExecutionRequest{
		FlowID:      scheduledFlow.FlowID,
		StartNodeID: scheduledFlow.StartNodeID,
		InputData:   make(map[string]interface{}),
	}

	// 执行流程
	result, err := s.flowExecutor.ExecuteFlow(s.ctx, req)
	if err != nil {
		log.Printf("❌ 流程执行失败 [%s]: %v", scheduledFlow.FlowName, err)
		return
	}

	log.Printf("✅ 流程执行完成 [%s]: 状态=%s, 执行ID=%s",
		scheduledFlow.FlowName, result.Status, result.ExecutionID)
}

// GetScheduledFlows 获取当前计划的流程（用于调试）
func (s *FlowScheduler) GetScheduledFlows() []*ScheduledFlow {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	flows := make([]*ScheduledFlow, 0, len(s.scheduledFlows))
	for _, flow := range s.scheduledFlows {
		flows = append(flows, flow)
	}

	return flows
}
