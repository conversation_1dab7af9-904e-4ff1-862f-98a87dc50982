package scheduler

import (
	"log"
	"sec-flow-server/internal/service"

	"time"

	"github.com/robfig/cron/v3"
)

// CleanupScheduler 清理任务调度器
type CleanupScheduler struct {
	cron             *cron.Cron
	cleanupService   *service.ExecutionCleanupService
	executionService *service.FlowExecutionService
	isRunning        bool
}

// NewCleanupScheduler 创建清理任务调度器
func NewCleanupScheduler() *CleanupScheduler {
	// 创建带有秒级精度的cron调度器
	c := cron.New(cron.WithSeconds())

	return &CleanupScheduler{
		cron:             c,
		cleanupService:   service.NewExecutionCleanupService(),
		executionService: service.NewFlowExecutionService(),
		isRunning:        false,
	}
}

// Start 启动清理任务调度器
func (s *CleanupScheduler) Start() error {
	if s.isRunning {
		log.Printf("⚠️ 清理任务调度器已经在运行中")
		return nil
	}

	log.Printf("🚀 启动执行记录清理调度器...")

	// 添加每天凌晨2点执行的清理任务
	_, err := s.cron.AddFunc("0 0 2 * * *", func() {
		log.Printf("⏰ 开始执行定时清理任务...")

		// 1. 修复卡住的执行记录
		if fixedCount, err := s.executionService.FixStuckExecutions(); err != nil {
			log.Printf("❌ 修复卡住执行记录失败: %v", err)
		} else if fixedCount > 0 {
			log.Printf("✅ 修复了 %d 个卡住的执行记录", fixedCount)
		}

		// 2. 清理过期的执行记录
		if err := s.cleanupService.CleanupExpiredExecutions(); err != nil {
			log.Printf("❌ 定时清理任务执行失败: %v", err)
		} else {
			log.Printf("✅ 定时清理任务执行完成")
		}
	})

	// 添加每小时执行一次的修复任务（修复卡住的执行记录）
	_, err2 := s.cron.AddFunc("0 0 * * * *", func() {
		if fixedCount, err := s.executionService.FixStuckExecutions(); err != nil {
			log.Printf("❌ 小时修复任务失败: %v", err)
		} else if fixedCount > 0 {
			log.Printf("✅ 小时修复任务完成，修复了 %d 个卡住的执行记录", fixedCount)
		}
	})

	if err != nil {
		return err
	}

	if err2 != nil {
		return err2
	}

	// 启动调度器
	s.cron.Start()
	s.isRunning = true

	log.Printf("✅ 执行记录清理调度器启动成功，每天凌晨2点执行清理任务")
	return nil
}

// Stop 停止清理任务调度器
func (s *CleanupScheduler) Stop() {
	if !s.isRunning {
		return
	}

	log.Printf("🛑 停止执行记录清理调度器...")
	s.cron.Stop()
	s.isRunning = false
	log.Printf("✅ 执行记录清理调度器已停止")
}

// IsRunning 检查调度器是否在运行
func (s *CleanupScheduler) IsRunning() bool {
	return s.isRunning
}

// RunCleanupNow 立即执行一次清理任务（用于测试或手动触发）
func (s *CleanupScheduler) RunCleanupNow() error {
	log.Printf("🧹 手动触发执行记录清理任务...")
	return s.cleanupService.CleanupExpiredExecutions()
}

// GetCleanupStatistics 获取清理统计信息
func (s *CleanupScheduler) GetCleanupStatistics() (*service.CleanupStatistics, error) {
	return s.cleanupService.GetCleanupStatistics()
}

// GetNextCleanupTime 获取下次清理时间
func (s *CleanupScheduler) GetNextCleanupTime() *time.Time {
	if !s.isRunning {
		return nil
	}

	entries := s.cron.Entries()
	if len(entries) == 0 {
		return nil
	}

	// 返回最近的下次执行时间
	nextTime := entries[0].Next
	return &nextTime
}
