package model

import "time"

// User 用户模型
type User struct {
	ID        string    `json:"id" gorm:"primaryKey;size:36"`
	Username  string    `json:"username" gorm:"size:100;not null;uniqueIndex" binding:"required"`
	Email     string    `json:"email" gorm:"size:255;uniqueIndex"`
	Name      string    `json:"name" gorm:"size:100"`
	Avatar    string    `json:"avatar" gorm:"size:500"`
	Status    string    `json:"status" gorm:"size:20;default:active"` // active, inactive
	CreatedAt time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
	Roles     []Role    `json:"roles" gorm:"many2many:user_roles;"`
}

// Role 角色模型
type Role struct {
	ID          string       `json:"id" gorm:"primaryKey;size:36"`
	Name        string       `json:"name" gorm:"size:100;not null;uniqueIndex"`
	DisplayName string       `json:"displayName" gorm:"size:100;not null"`
	Description string       `json:"description" gorm:"type:text"`
	IsDefault   bool         `json:"isDefault" gorm:"default:false"` // 是否为默认角色
	CreatedAt   time.Time    `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time    `json:"updatedAt" gorm:"autoUpdateTime"`
	Users       []User       `json:"users" gorm:"many2many:user_roles;"`
	Permissions []Permission `json:"permissions" gorm:"many2many:role_permissions;"`
}

// Permission 权限模型
type Permission struct {
	ID          string    `json:"id" gorm:"primaryKey;size:36"`
	Name        string    `json:"name" gorm:"size:100;not null;uniqueIndex"`
	DisplayName string    `json:"displayName" gorm:"size:100;not null"`
	Description string    `json:"description" gorm:"type:text"`
	Resource    string    `json:"resource" gorm:"size:100;not null"` // 资源名称，如 flows, users, templates
	Action      string    `json:"action" gorm:"size:50;not null"`    // 操作名称，如 read, write, delete
	CreatedAt   time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
	Roles       []Role    `json:"roles" gorm:"many2many:role_permissions;"`
}

// UserRole 用户角色关联表
type UserRole struct {
	UserID string `json:"userId" gorm:"primaryKey;size:36"`
	RoleID string `json:"roleId" gorm:"primaryKey;size:36"`
}

// RolePermission 角色权限关联表
type RolePermission struct {
	RoleID       string `json:"roleId" gorm:"primaryKey;size:36"`
	PermissionID string `json:"permissionId" gorm:"primaryKey;size:36"`
}

// UpdateProfileRequest 更新用户信息请求
type UpdateProfileRequest struct {
	Username string `json:"username"`
	Email    string `json:"email" binding:"omitempty,email"`
	Avatar   string `json:"avatar"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string   `json:"username" binding:"required"`
	Email    string   `json:"email" binding:"omitempty,email"`
	Avatar   string   `json:"avatar"`
	RoleIDs  []string `json:"roleIds"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Username string   `json:"username"`
	Email    string   `json:"email" binding:"omitempty,email"`
	Avatar   string   `json:"avatar"`
	Status   string   `json:"status"`
	RoleIDs  []string `json:"roleIds"`
}

// UserListRequest 用户列表请求
type UserListRequest struct {
	Page     int    `form:"page"`
	PageSize int    `form:"pageSize"`
	Keyword  string `form:"keyword"`
	Status   string `form:"status"`
	RoleID   string `form:"roleId"`
}

// UserWithPermissions 用户及其权限信息
type UserWithPermissions struct {
	User        *User        `json:"user"`
	Roles       []Role       `json:"roles"`
	Permissions []Permission `json:"permissions"`
}
