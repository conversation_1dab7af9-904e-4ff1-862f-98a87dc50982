package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// EventSuppressionRecord 事件去重记录
type EventSuppressionRecord struct {
	ID                  string         `json:"id" gorm:"primaryKey;type:varchar(36)"`
	DedupKey            string         `json:"dedupKey" gorm:"type:varchar(255);not null;index:idx_dedup_key_window"`
	OriginalEventID     string         `json:"originalEventId" gorm:"type:varchar(36)"`
	FirstOccurrenceTime time.Time      `json:"firstOccurrenceTime" gorm:"not null"`
	LastOccurrenceTime  time.Time      `json:"lastOccurrenceTime" gorm:"not null"`
	OccurrenceCount     int            `json:"occurrenceCount" gorm:"default:1"`
	WindowEndTime       time.Time      `json:"windowEndTime" gorm:"not null;index:idx_dedup_key_window"`
	FieldValues         FieldValuesMap `json:"fieldValues" gorm:"type:json"`
	PreviousFieldValues FieldValuesMap `json:"previousFieldValues" gorm:"type:json"`
	SuppressionCount    int            `json:"suppressionCount" gorm:"default:0"`
	LastBreakCondition  string         `json:"lastBreakCondition" gorm:"type:text"`
	CreatedAt           time.Time      `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt           time.Time      `json:"updatedAt" gorm:"autoUpdateTime"`
}

// FieldValuesMap 字段值映射，用于存储变化字段的值
type FieldValuesMap map[string]interface{}

// Value 实现 driver.Valuer 接口
func (f FieldValuesMap) Value() (driver.Value, error) {
	if f == nil {
		return nil, nil
	}
	return json.Marshal(f)
}

// Scan 实现 sql.Scanner 接口
func (f *FieldValuesMap) Scan(value interface{}) error {
	if value == nil {
		*f = make(FieldValuesMap)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into FieldValuesMap", value)
	}

	if len(bytes) == 0 {
		*f = make(FieldValuesMap)
		return nil
	}

	return json.Unmarshal(bytes, f)
}

// BreakCondition 突破压制条件配置
type BreakCondition struct {
	Field    string `json:"field"`    // 字段路径
	Operator string `json:"operator"` // 操作符
	Value    string `json:"value"`    // 比较值
}

// SuppressionConfig 去重配置
type SuppressionConfig struct {
	TimeWindow       string           `json:"timeWindow"`       // 时间窗口
	CustomTimeWindow string           `json:"customTimeWindow"` // 自定义时间窗口
	DedupKeyTemplate string           `json:"dedupKeyTemplate"` // 去重键模板
	BreakConditions  []BreakCondition `json:"breakConditions"`  // 突破压制条件列表
}

// TableName 指定表名
func (EventSuppressionRecord) TableName() string {
	return "tb_event_suppression_records"
}
