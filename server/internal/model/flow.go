package model

import (
	"sec-flow-server/internal/constants"
	"time"
)

// Flow 流程图模型
type Flow struct {
	ID                     string               `json:"id" gorm:"primaryKey;size:36" binding:"required"`
	Name                   string               `json:"name" gorm:"size:255;not null" binding:"required"`
	ExecutionTime          string               `json:"executionTime" gorm:"size:255"` // 执行时机
	Description            string               `json:"description" gorm:"type:text"`
	Data                   string               `json:"data" gorm:"type:longtext;not null"` // 存储JSON字符串
	ExecutionRetentionDays int                  `json:"executionRetentionDays" gorm:"default:30;comment:执行记录保存天数，0表示永久保存"`
	CreatedAt              time.Time            `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt              time.Time            `json:"updatedAt" gorm:"autoUpdateTime"`
	CreatedBy              string               `json:"createdBy" gorm:"size:36"`
	Status                 constants.FlowStatus `json:"status" gorm:"size:20;default:draft"` // 流程状态
}

// FlowData LogicFlow数据结构
type FlowData struct {
	Nodes []Node `json:"nodes"`
	Edges []Edge `json:"edges"`
}

// Node 节点模型
type Node struct {
	ID         string                 `json:"id" binding:"required"`
	Type       string                 `json:"type" binding:"required"`
	X          float64                `json:"x" binding:"required"`
	Y          float64                `json:"y" binding:"required"`
	Text       string                 `json:"text"`
	Properties map[string]interface{} `json:"properties,omitempty"`
}

// Edge 连线模型
type Edge struct {
	ID           string                 `json:"id" binding:"required"`
	Type         string                 `json:"type" binding:"required"`
	SourceNodeId string                 `json:"sourceNodeId" binding:"required"`
	TargetNodeId string                 `json:"targetNodeId" binding:"required"`
	Text         string                 `json:"text,omitempty"`
	StartPoint   *Point                 `json:"startPoint,omitempty"`
	EndPoint     *Point                 `json:"endPoint,omitempty"`
	PointsList   []Point                `json:"pointsList,omitempty"`
	Properties   map[string]interface{} `json:"properties,omitempty"`
}

// Point 坐标点
type Point struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

// CreateFlowRequest 创建流程图请求
type CreateFlowRequest struct {
	Name                   string   `json:"name" binding:"required"`
	ExecutionTime          string   `json:"executionTime"`
	Description            string   `json:"description"`
	Data                   FlowData `json:"data" binding:"required"`
	ExecutionRetentionDays int      `json:"executionRetentionDays"`
}

// UpdateFlowRequest 更新流程图请求
type UpdateFlowRequest struct {
	Name                   string               `json:"name"`
	ExecutionTime          string               `json:"executionTime"`
	Description            string               `json:"description"`
	Data                   FlowData             `json:"data"`
	Status                 constants.FlowStatus `json:"status"`
	ExecutionRetentionDays *int                 `json:"executionRetentionDays"` // 使用指针以区分0值和未设置
}

// ExecuteFlowRequest 执行流程图请求
type ExecuteFlowRequest struct {
	StartNodeId string                 `json:"startNodeId"`
	InputData   map[string]interface{} `json:"inputData"`
}

// ExecuteFlowResponse 执行流程图响应
type ExecuteFlowResponse struct {
	ExecutionId string                 `json:"executionId"`
	Status      string                 `json:"status"` // running, completed, failed
	Result      map[string]interface{} `json:"result,omitempty"`
	Steps       []ExecutionStep        `json:"steps"`
}

// ExecutionStep 执行步骤
type ExecutionStep struct {
	NodeId    string                 `json:"nodeId"`
	NodeType  string                 `json:"nodeType"`
	Status    string                 `json:"status"` // pending, running, completed, failed
	StartTime time.Time              `json:"startTime"`
	EndTime   *time.Time             `json:"endTime,omitempty"`
	Input     map[string]interface{} `json:"input,omitempty"`
	Output    map[string]interface{} `json:"output,omitempty"`
	Error     string                 `json:"error,omitempty"`
}

// FlowResponse 流程图响应模型（包含解析后的Data）
type FlowResponse struct {
	ID                     string               `json:"id"`
	Name                   string               `json:"name"`
	ExecutionTime          string               `json:"executionTime"`
	Description            string               `json:"description"`
	Data                   FlowData             `json:"data"`
	ExecutionRetentionDays int                  `json:"executionRetentionDays"`
	CreatedAt              time.Time            `json:"createdAt"`
	UpdatedAt              time.Time            `json:"updatedAt"`
	CreatedBy              string               `json:"createdBy"`
	Status                 constants.FlowStatus `json:"status"`
}
