package model

// PresetNode 预置节点模型
type PresetNode struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Category    string                 `json:"category"`    // user, data, logic, api等
	NodeType    string                 `json:"nodeType"`    // rect, circle, diamond等
	Icon        string                 `json:"icon"`        // 图标名称
	Config      PresetNodeConfig       `json:"config"`      // 节点配置
	FormFields  []PresetNodeFormField  `json:"formFields"`  // 表单字段定义
	Style       PresetNodeStyle        `json:"style"`       // 样式配置
}

// PresetNodeConfig 预置节点配置
type PresetNodeConfig struct {
	Width       int                    `json:"width"`
	Height      int                    `json:"height"`
	Resizable   bool                   `json:"resizable"`
	Deletable   bool                   `json:"deletable"`
	Editable    bool                   `json:"editable"`
	Properties  map[string]interface{} `json:"properties"`
}

// PresetNodeFormField 表单字段定义
type PresetNodeFormField struct {
	Key         string                 `json:"key"`         // 字段key
	Label       string                 `json:"label"`       // 显示标签
	Type        string                 `json:"type"`        // input, select, textarea等
	Required    bool                   `json:"required"`    // 是否必填
	DefaultValue interface{}           `json:"defaultValue"` // 默认值
	Options     []FormFieldOption      `json:"options,omitempty"` // 下拉选项
	Placeholder string                 `json:"placeholder,omitempty"` // 占位符
	Validation  map[string]interface{} `json:"validation,omitempty"`  // 验证规则
}

// FormFieldOption 表单字段选项
type FormFieldOption struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

// PresetNodeStyle 节点样式
type PresetNodeStyle struct {
	Fill        string `json:"fill"`        // 填充色
	Stroke      string `json:"stroke"`      // 边框色
	StrokeWidth int    `json:"strokeWidth"` // 边框宽度
	FontSize    int    `json:"fontSize"`    // 字体大小
	FontColor   string `json:"fontColor"`   // 字体颜色
	BorderRadius int   `json:"borderRadius,omitempty"` // 圆角半径
}

// CreatePresetNodeInstanceRequest 创建预置节点实例请求
type CreatePresetNodeInstanceRequest struct {
	PresetNodeID string                 `json:"presetNodeId" binding:"required"`
	X            float64                `json:"x" binding:"required"`
	Y            float64                `json:"y" binding:"required"`
	FormData     map[string]interface{} `json:"formData"`
}

// PresetNodeInstance 预置节点实例
type PresetNodeInstance struct {
	ID           string                 `json:"id"`
	PresetNodeID string                 `json:"presetNodeId"`
	X            float64                `json:"x"`
	Y            float64                `json:"y"`
	FormData     map[string]interface{} `json:"formData"`
	Config       PresetNodeConfig       `json:"config"`
	Style        PresetNodeStyle        `json:"style"`
}
