package model

import (
	"sec-flow-server/internal/constants"
	"time"
)

// FlowExecution 流程执行记录模型
type FlowExecution struct {
	ID            string                    `json:"id" gorm:"primaryKey;size:36"`
	FlowID        string                    `json:"flowId" gorm:"size:36;not null;index"`
	FlowName      string                    `json:"flowName" gorm:"size:255;not null"`
	FlowSnapshot  string                    `json:"flowSnapshot" gorm:"type:longtext;not null"` // 执行时的流程配置快照
	TriggerType   constants.TriggerType     `json:"triggerType" gorm:"size:20;not null"`        // 触发类型：manual, cron, http
	TriggerSource string                    `json:"triggerSource" gorm:"size:255"`              // 触发来源：用户ID、定时任务、HTTP Key等
	Status        constants.ExecutionStatus `json:"status" gorm:"size:20;not null"`             // 执行状态
	StartTime     time.Time                 `json:"startTime" gorm:"not null"`
	EndTime       *time.Time                `json:"endTime"`
	Duration      int64                     `json:"duration"`                          // 执行时长（毫秒）
	InputData     string                    `json:"inputData" gorm:"type:text"`        // 输入数据JSON
	OutputData    string                    `json:"outputData" gorm:"type:longtext"`   // 输出数据JSON
	ExecutionLog  string                    `json:"executionLog" gorm:"type:longtext"` // 执行日志JSON
	ErrorMessage  string                    `json:"errorMessage" gorm:"type:text"`     // 错误信息
	CreatedAt     time.Time                 `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt     time.Time                 `json:"updatedAt" gorm:"autoUpdateTime"`
}

// FlowExecutionStep 执行步骤记录
type FlowExecutionStep struct {
	StepID       string                 `json:"stepId"`
	NodeID       string                 `json:"nodeId"`
	NodeType     string                 `json:"nodeType"`
	NodeName     string                 `json:"nodeName"`
	Status       string                 `json:"status"`
	StartTime    int64                  `json:"startTime"`
	EndTime      int64                  `json:"endTime"`
	Duration     int64                  `json:"duration"`
	InputData    map[string]interface{} `json:"inputData"`
	OutputData   map[string]interface{} `json:"outputData"`
	FlowData     map[string]interface{} `json:"flowData"`
	ErrorMessage string                 `json:"errorMessage,omitempty"`
}

// FlowExecutionLog 执行日志结构
type FlowExecutionLog struct {
	ExecutionID string                 `json:"executionId"`
	Steps       []FlowExecutionStep    `json:"steps"`
	NodeResults map[string]interface{} `json:"nodeResults"`
	Summary     FlowExecutionSummary   `json:"summary"`
}

// FlowExecutionSummary 执行摘要
type FlowExecutionSummary struct {
	TotalSteps    int    `json:"totalSteps"`
	SuccessSteps  int    `json:"successSteps"`
	FailedSteps   int    `json:"failedSteps"`
	SkippedSteps  int    `json:"skippedSteps"`
	ExecutionPath string `json:"executionPath"`
}

// CreateFlowExecutionRequest 创建执行记录请求
type CreateFlowExecutionRequest struct {
	FlowID        string                 `json:"flowId" binding:"required"`
	TriggerType   constants.TriggerType  `json:"triggerType" binding:"required"`
	TriggerSource string                 `json:"triggerSource"`
	InputData     map[string]interface{} `json:"inputData"`
}

// FlowExecutionListResponse 执行记录列表响应
type FlowExecutionListResponse struct {
	ID            string                    `json:"id"`
	FlowID        string                    `json:"flowId"`
	FlowName      string                    `json:"flowName"`
	TriggerType   constants.TriggerType     `json:"triggerType"`
	TriggerSource string                    `json:"triggerSource"`
	Status        constants.ExecutionStatus `json:"status"`
	StartTime     time.Time                 `json:"startTime"`
	EndTime       *time.Time                `json:"endTime"`
	Duration      int64                     `json:"duration"`
	ErrorMessage  string                    `json:"errorMessage"`
	CreatedAt     time.Time                 `json:"createdAt"`
}

// FlowExecutionDetailResponse 执行记录详情响应
type FlowExecutionDetailResponse struct {
	FlowExecutionListResponse
	FlowSnapshot FlowData               `json:"flowSnapshot"`
	InputData    map[string]interface{} `json:"inputData"`
	OutputData   map[string]interface{} `json:"outputData"`
	ExecutionLog FlowExecutionLog       `json:"executionLog"`
}

// FlowWithLastExecution 带最新执行记录的流程
type FlowWithLastExecution struct {
	Flow
	LastExecution *FlowExecutionListResponse `json:"lastExecution"`
}
