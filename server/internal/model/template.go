package model

import "time"

// Template 节点模板模型
type Template struct {
	ID          string    `json:"id" gorm:"primaryKey;size:36"`
	Name        string    `json:"name" gorm:"size:255;not null" binding:"required"`
	Description string    `json:"description" gorm:"type:text"`
	Category    string    `json:"category" gorm:"size:50;not null" binding:"required"` // basic, advanced, custom
	NodeType    string    `json:"nodeType" gorm:"size:50;not null" binding:"required"` // rect, circle, diamond, etc.
	Icon        string    `json:"icon" gorm:"size:100"`
	Config      string    `json:"config" gorm:"type:text"` // 存储JSON字符串
	CreatedAt   time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// CreateTemplateRequest 创建模板请求
type CreateTemplateRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description"`
	Category    string                 `json:"category" binding:"required"`
	NodeType    string                 `json:"nodeType" binding:"required"`
	Icon        string                 `json:"icon"`
	Config      map[string]interface{} `json:"config"`
}

// UpdateTemplateRequest 更新模板请求
type UpdateTemplateRequest struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Category    string                 `json:"category"`
	NodeType    string                 `json:"nodeType"`
	Icon        string                 `json:"icon"`
	Config      map[string]interface{} `json:"config"`
}
