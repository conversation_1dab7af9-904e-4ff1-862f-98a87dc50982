package model

import "time"

// DistributedLock 分布式锁模型
type DistributedLock struct {
	ID         string    `json:"id" gorm:"primaryKey;size:100"`           // 锁ID，通常是任务标识
	LockType   string    `json:"lockType" gorm:"size:50;not null"`        // 锁类型：flow_execution, scheduler_preprocess等
	ResourceID string    `json:"resourceId" gorm:"size:100;not null"`     // 资源ID，如流程ID
	InstanceID string    `json:"instanceId" gorm:"size:100;not null"`     // 持有锁的实例ID
	ExpiresAt  time.Time `json:"expiresAt" gorm:"not null;index"`         // 锁过期时间
	CreatedAt  time.Time `json:"createdAt" gorm:"autoCreateTime"`         // 创建时间
	UpdatedAt  time.Time `json:"updatedAt" gorm:"autoUpdateTime"`         // 更新时间
	Metadata   string    `json:"metadata" gorm:"type:text"`               // 额外元数据，JSON格式
}

// LockMetadata 锁元数据
type LockMetadata struct {
	FlowName       string `json:"flowName,omitempty"`       // 流程名称
	ScheduledTime  string `json:"scheduledTime,omitempty"`  // 计划执行时间
	CronExpression string `json:"cronExpression,omitempty"` // Cron表达式
	ExecutionID    string `json:"executionId,omitempty"`    // 执行ID
	TaskAssignment string `json:"taskAssignment,omitempty"` // 任务分配信息（JSON格式）
}

// 锁类型常量
const (
	LockTypeFlowExecution      = "flow_execution"      // 流程执行锁
	LockTypeSchedulerPreprocess = "scheduler_preprocess" // 调度器预处理锁
	LockTypeSchedulerExecution = "scheduler_execution"  // 调度器执行锁
)
