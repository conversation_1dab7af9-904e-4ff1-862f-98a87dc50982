package model

import (
	"time"

	"gorm.io/gorm"
)

// EventCategory 事件分类模型
type EventCategory struct {
	ID          string         `json:"id" gorm:"primaryKey;size:36"`
	Name        string         `json:"name" gorm:"size:100;not null;uniqueIndex"`
	Description string         `json:"description" gorm:"size:500"`
	OwnerID     string         `json:"ownerId" gorm:"size:36;not null;index"`
	Owner       *User          `json:"owner,omitempty" gorm:"foreignKey:OwnerID"`
	CreatedAt   time.Time      `json:"createdAt"`
	UpdatedAt   time.Time      `json:"updatedAt"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// EventProcessType 事件处理分类
type EventProcessType string

const (
	EventProcessTypeWhitelist   EventProcessType = "whitelist"   // 加白
	EventProcessTypeFalseAlarm  EventProcessType = "false_alarm" // 误报
	EventProcessTypeIgnore      EventProcessType = "ignore"      // 忽略
	EventProcessTypePending     EventProcessType = "pending"     // 暂不处理
	EventProcessTypeCompleted   EventProcessType = "completed"   // 已完成
	EventProcessTypeUnprocessed EventProcessType = "unprocessed" // 未处理
)

// GetEventProcessTypeDisplayName 获取事件处理分类的显示名称
func (t EventProcessType) GetDisplayName() string {
	switch t {
	case EventProcessTypeWhitelist:
		return "加白"
	case EventProcessTypeFalseAlarm:
		return "误报"
	case EventProcessTypeIgnore:
		return "忽略"
	case EventProcessTypePending:
		return "暂不处理"
	case EventProcessTypeCompleted:
		return "已完成"
	case EventProcessTypeUnprocessed:
		return "未处理"
	default:
		return "未知"
	}
}

// EventOperation 事件运营模型
type EventOperation struct {
	ID                 string           `json:"id" gorm:"primaryKey;size:36"`
	OccurredAt         time.Time        `json:"occurredAt" gorm:"not null;index"`
	OccurredCondition  string           `json:"occurredCondition" gorm:"size:1000;not null"`
	CategoryID         string           `json:"categoryId" gorm:"size:36;not null;index"`
	Category           *EventCategory   `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	Description        string           `json:"description" gorm:"size:2000"`
	OwnerID            string           `json:"ownerId" gorm:"size:36;not null;index"`
	Owner              *User            `json:"owner,omitempty" gorm:"foreignKey:OwnerID"`
	RelatedEmployeeIDs string           `json:"relatedEmployeeIds" gorm:"size:1000"` // JSON数组字符串
	RelatedEmployees   []*Employee      `json:"relatedEmployees,omitempty" gorm:"-"`
	ProcessType        EventProcessType `json:"processType" gorm:"size:20;not null;index;default:'unprocessed'"`
	ProcessDescription string           `json:"processDescription" gorm:"size:2000"`
	ProcessCompletedAt *time.Time       `json:"processCompletedAt" gorm:"index"`
	CreatedAt          time.Time        `json:"createdAt"`
	UpdatedAt          time.Time        `json:"updatedAt"`
	DeletedAt          gorm.DeletedAt   `json:"-" gorm:"index"`
}

// EventCategoryListRequest 事件分类列表请求
type EventCategoryListRequest struct {
	Page     int `form:"page" binding:"min=1"`
	PageSize int `form:"pageSize" binding:"min=1,max=100"`
}

// EventCategoryCreateRequest 创建事件分类请求
type EventCategoryCreateRequest struct {
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
	OwnerID     string `json:"ownerId" binding:"required"`
}

// EventCategoryUpdateRequest 更新事件分类请求
type EventCategoryUpdateRequest struct {
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
	OwnerID     string `json:"ownerId" binding:"required"`
}

// EventOperationListRequest 事件运营列表请求
type EventOperationListRequest struct {
	Page              int              `form:"page" binding:"min=1"`
	PageSize          int              `form:"pageSize" binding:"min=1,max=100"`
	CategoryID        string           `form:"categoryId"`
	OwnerID           string           `form:"ownerId"`
	ProcessType       EventProcessType `form:"processType"`
	OccurredStartTime string           `form:"occurredStartTime"` // 格式: 2006-01-02
	OccurredEndTime   string           `form:"occurredEndTime"`   // 格式: 2006-01-02
	Keyword           string           `form:"keyword"`           // 搜索关键词
}

// EventOperationCreateRequest 创建事件运营请求
type EventOperationCreateRequest struct {
	OccurredAt         time.Time        `json:"occurredAt" binding:"required"`
	OccurredCondition  string           `json:"occurredCondition" binding:"required,max=1000"`
	CategoryID         string           `json:"categoryId" binding:"required"`
	Description        string           `json:"description" binding:"max=2000"`
	OwnerID            string           `json:"ownerId" binding:"required"`
	RelatedEmployeeIDs []string         `json:"relatedEmployeeIds"`
	ProcessType        EventProcessType `json:"processType" binding:"required"`
	ProcessDescription string           `json:"processDescription" binding:"max=2000"`
	ProcessCompletedAt *time.Time       `json:"processCompletedAt"`
}

// EventOperationUpdateRequest 更新事件运营请求
type EventOperationUpdateRequest struct {
	OccurredAt         time.Time        `json:"occurredAt" binding:"required"`
	OccurredCondition  string           `json:"occurredCondition" binding:"required,max=1000"`
	CategoryID         string           `json:"categoryId" binding:"required"`
	Description        string           `json:"description" binding:"max=2000"`
	OwnerID            string           `json:"ownerId" binding:"required"`
	RelatedEmployeeIDs []string         `json:"relatedEmployeeIds"`
	ProcessType        EventProcessType `json:"processType" binding:"required"`
	ProcessDescription string           `json:"processDescription" binding:"max=2000"`
	ProcessCompletedAt *time.Time       `json:"processCompletedAt"`
}

// EventOperationBatchUpdateRequest 批量更新事件运营请求
type EventOperationBatchUpdateRequest struct {
	IDs                []string         `json:"ids" binding:"required,min=1"`
	ProcessType        EventProcessType `json:"processType"`
	ProcessDescription string           `json:"processDescription" binding:"max=2000"`
	ProcessCompletedAt *time.Time       `json:"processCompletedAt"`
}

// EventOperationListResponse 事件运营列表响应
type EventOperationListResponse struct {
	EventOperation
	CategoryName string `json:"categoryName"`
	OwnerName    string `json:"ownerName"`
}

// EventCategoryOption 事件分类选项
type EventCategoryOption struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// EventProcessTypeOption 事件处理分类选项
type EventProcessTypeOption struct {
	Value string `json:"value"`
	Label string `json:"label"`
}

// GetEventProcessTypeOptions 获取所有事件处理分类选项
func GetEventProcessTypeOptions() []EventProcessTypeOption {
	return []EventProcessTypeOption{
		{Value: string(EventProcessTypeUnprocessed), Label: EventProcessTypeUnprocessed.GetDisplayName()},
		{Value: string(EventProcessTypeWhitelist), Label: EventProcessTypeWhitelist.GetDisplayName()},
		{Value: string(EventProcessTypeFalseAlarm), Label: EventProcessTypeFalseAlarm.GetDisplayName()},
		{Value: string(EventProcessTypeIgnore), Label: EventProcessTypeIgnore.GetDisplayName()},
		{Value: string(EventProcessTypePending), Label: EventProcessTypePending.GetDisplayName()},
		{Value: string(EventProcessTypeCompleted), Label: EventProcessTypeCompleted.GetDisplayName()},
	}
}
