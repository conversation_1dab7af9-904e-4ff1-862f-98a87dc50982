package config

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
	"os"
	"reflect"
	"strings"
)

const (
	// 加密标识前缀
	EncryptPrefix = "ENC("
	EncryptSuffix = ")"
	// 密钥文件路径
	KeyFilePath = ".key"
)

// CryptoManager 加密管理器
type CryptoManager struct {
	key []byte
}

// NewCryptoManager 创建加密管理器
func NewCryptoManager() (*CryptoManager, error) {
	key, err := getEncryptionKey()
	print(string(key))
	if err != nil {
		return nil, fmt.Errorf("获取加密密钥失败: %v", err)
	}

	return &CryptoManager{key: key}, nil
}

// getEncryptionKey 获取加密密钥
// 优先从环境变量CONF_KEY获取，如果没有则从.key文件读取
func getEncryptionKey() ([]byte, error) {
	// 1. 优先从环境变量获取
	if key := os.Getenv("CONF_KEY"); key != "" {
		keyBytes := []byte(key)
		// AES密钥长度必须是16、24或32字节
		if len(keyBytes) < 32 {
			// 如果密钥长度不足32字节，用0填充
			paddedKey := make([]byte, 32)
			copy(paddedKey, keyBytes)
			return paddedKey, nil
		}
		return keyBytes[:32], nil // 取前32字节
	}

	// 2. 从.key文件读取
	keyData, err := os.ReadFile(KeyFilePath)
	if err != nil {
		return nil, fmt.Errorf("读取密钥文件失败: %v", err)
	}

	key := strings.TrimSpace(string(keyData))
	if key == "" {
		return nil, fmt.Errorf("密钥文件为空")
	}

	keyBytes := []byte(key)
	if len(keyBytes) < 32 {
		// 如果密钥长度不足32字节，用0填充
		paddedKey := make([]byte, 32)
		copy(paddedKey, keyBytes)
		return paddedKey, nil
	}
	return keyBytes[:32], nil // 取前32字节
}

// Encrypt 加密明文
func (cm *CryptoManager) Encrypt(plaintext string) (string, error) {
	if plaintext == "" {
		return "", nil
	}

	block, err := aes.NewCipher(cm.key)
	if err != nil {
		return "", fmt.Errorf("创建AES加密器失败: %v", err)
	}

	// 使用GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建GCM失败: %v", err)
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("生成nonce失败: %v", err)
	}

	// 加密
	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)

	// 返回base64编码的密文
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// Decrypt 解密密文
func (cm *CryptoManager) Decrypt(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}

	// base64解码
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("base64解码失败: %v", err)
	}

	block, err := aes.NewCipher(cm.key)
	if err != nil {
		return "", fmt.Errorf("创建AES解密器失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建GCM失败: %v", err)
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("密文长度不足")
	}

	nonce, ciphertext_bytes := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext_bytes, nil)
	if err != nil {
		return "", fmt.Errorf("解密失败: %v", err)
	}

	return string(plaintext), nil
}

// isEncrypted 检查字符串是否是加密格式 ENC(...)
func isEncrypted(value string) bool {
	return strings.HasPrefix(value, EncryptPrefix) && strings.HasSuffix(value, EncryptSuffix)
}

// extractEncryptedValue 从ENC(...)格式中提取密文
func extractEncryptedValue(value string) string {
	if !isEncrypted(value) {
		return value
	}
	// 去掉ENC(和)
	return value[len(EncryptPrefix) : len(value)-len(EncryptSuffix)]
}

// DecryptConfig 解密配置中的敏感数据
func (cm *CryptoManager) DecryptConfig(config *Config) error {
	return cm.decryptStruct(reflect.ValueOf(config).Elem())
}

// decryptStruct 递归解密结构体中的字符串字段
func (cm *CryptoManager) decryptStruct(v reflect.Value) error {
	switch v.Kind() {
	case reflect.Struct:
		for i := 0; i < v.NumField(); i++ {
			field := v.Field(i)
			if field.CanSet() {
				if err := cm.decryptStruct(field); err != nil {
					return err
				}
			}
		}
	case reflect.String:
		str := v.String()
		if isEncrypted(str) {
			encryptedValue := extractEncryptedValue(str)
			decrypted, err := cm.Decrypt(encryptedValue)
			if err != nil {
				return fmt.Errorf("解密字段失败: %v", err)
			}
			v.SetString(decrypted)
		}
	case reflect.Slice:
		for i := 0; i < v.Len(); i++ {
			if err := cm.decryptStruct(v.Index(i)); err != nil {
				return err
			}
		}
	}
	return nil
}

// EncryptForConfig 为配置文件格式化加密文本
func (cm *CryptoManager) EncryptForConfig(plaintext string) (string, error) {
	encrypted, err := cm.Encrypt(plaintext)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s%s%s", EncryptPrefix, encrypted, EncryptSuffix), nil
}

// GenerateKey 生成一个32字节的随机密钥
func GenerateKey() (string, error) {
	key := make([]byte, 32)
	if _, err := rand.Read(key); err != nil {
		return "", fmt.Errorf("生成密钥失败: %v", err)
	}
	return base64.StdEncoding.EncodeToString(key), nil
}

// SaveKeyToFile 保存密钥到.key文件
func SaveKeyToFile(key string) error {
	return os.WriteFile(KeyFilePath, []byte(key), 0600)
}
