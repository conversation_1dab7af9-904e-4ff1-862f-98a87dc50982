package config

import (
	"fmt"
	"os"
	"path/filepath"
	"testing"
)

// TestEncryptSensitiveData 测试加密敏感数据
// 使用方法：将需要加密的明文放在这个测试中，运行 go test -v -run TestEncryptSensitiveData
// 然后将输出的密文复制到配置文件中
func TestEncryptSensitiveData(t *testing.T) {
	// 确保有密钥文件或环境变量
	setupTestKey(t)

	// 创建加密管理器
	cm, err := NewCryptoManager()
	if err != nil {
		t.Fatalf("创建加密管理器失败: %v", err)
	}

	// 需要加密的敏感数据列表
	sensitiveData := []string{
		"12345678", // 数据库密码
		"1hnoFU23MUIzV7rza0BB",
	}

	fmt.Println("=== 加密结果 ===")
	fmt.Println("将以下密文复制到配置文件中：")
	fmt.Println()

	for i, plaintext := range sensitiveData {
		encrypted, err := cm.EncryptForConfig(plaintext)
		if err != nil {
			t.<PERSON>rrorf("加密失败 [%d]: %v", i, err)
			continue
		}

		fmt.Printf("明文: %s\n", plaintext)
		fmt.Printf("密文: %s\n", encrypted)
		fmt.Println("---")
	}
}

// TestDecryptSensitiveData 测试解密功能
func TestDecryptSensitiveData(t *testing.T) {
	setupTestKey(t)

	cm, err := NewCryptoManager()
	if err != nil {
		t.Fatalf("创建加密管理器失败: %v", err)
	}

	encrypted := "ENC()"

	// 解密
	decrypted, err := cm.Decrypt(extractEncryptedValue(encrypted))
	if err != nil {
		t.Fatalf("解密失败: %v", err)
	}

	fmt.Printf("✅ 加密解密测试通过: %s -> %s\n", encrypted, decrypted)
}

// setupTestKey 设置测试密钥
func setupTestKey(t *testing.T) {
	// 切换到项目根目录（server目录）
	originalDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("获取当前工作目录失败: %v", err)
	}

	// 尝试切换到项目根目录
	projectRoot := findProjectRoot(originalDir)
	if projectRoot == "" {
		t.Fatalf("无法找到项目根目录")
	}

	err = os.Chdir(projectRoot)
	if err != nil {
		t.Fatalf("切换到项目根目录失败: %v", err)
	}

	// 测试结束后恢复原目录
	t.Cleanup(func() {
		os.Chdir(originalDir)
	})

	// 如果没有环境变量，创建测试密钥文件
	if os.Getenv("CONF_KEY") == "" {
		_, err := os.Stat(KeyFilePath)
		if os.IsNotExist(err) {
			testKey := "test-key-for-unit-testing-32-bytes"
			if len(testKey) < 32 {
				testKey = testKey + "000000000000000000000000000000000"[:32-len(testKey)]
			}
			err := SaveKeyToFile(testKey)
			if err != nil {
				t.Fatalf("创建测试密钥文件失败: %v", err)
			}
			t.Logf("✅ 测试密钥文件已创建: %s", KeyFilePath)
		}
	}
}

// findProjectRoot 查找项目根目录（包含 go.mod 文件的目录）
func findProjectRoot(currentDir string) string {
	dir := currentDir
	for {
		// 检查当前目录是否包含 go.mod 文件
		if _, err := os.Stat(filepath.Join(dir, "go.mod")); err == nil {
			return dir
		}

		// 向上查找父目录
		parentDir := filepath.Dir(dir)
		if parentDir == dir {
			// 已经到达根目录
			return ""
		}
		dir = parentDir
	}
}
