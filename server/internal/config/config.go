package config

import (
	"fmt"
	"os"
	"strconv"
	"time"

	"gopkg.in/yaml.v3"
)

// Config 应用配置结构
type Config struct {
	App       AppConfig        `yaml:"app"`
	Server    ServerConfig     `yaml:"server"`
	Database  DatabaseConfig   `yaml:"database"`
	Log       LogConfig        `yaml:"log"`
	Scheduler SchedulerConfig  `yaml:"scheduler"`
	CORS      CORSConfig       `yaml:"cors"`
	JWT       JWTConfig        `yaml:"jwt"`
	Other     OtherConfig      `yaml:"other"`
	Third     ThirdPartyConfig `yaml:"third"`
	Databases []DbConfig       `yaml:"databases"`
}

// AppConfig 应用配置
type AppConfig struct {
	Name    string `yaml:"name"`
	Version string `yaml:"version"`
	Env     string `yaml:"env"`
	Debug   bool   `yaml:"debug"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string        `yaml:"host"`
	Port         int           `yaml:"port"`
	ReadTimeout  time.Duration `yaml:"read_timeout"`
	WriteTimeout time.Duration `yaml:"write_timeout"`
	IdleTimeout  time.Duration `yaml:"idle_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver          string        `yaml:"driver"`
	Host            string        `yaml:"host"`
	Port            int           `yaml:"port"`
	Username        string        `yaml:"username"`
	Password        string        `yaml:"password"`
	Database        string        `yaml:"database"`
	Charset         string        `yaml:"charset"`
	ParseTime       bool          `yaml:"parse_time"`
	Loc             string        `yaml:"loc"`
	MaxIdleConns    int           `yaml:"max_idle_conns"`
	MaxOpenConns    int           `yaml:"max_open_conns"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
	LogLevel        string        `yaml:"log_level"`    // 数据库日志级别：silent, error, warn, info
	AutoMigrate     bool          `yaml:"auto_migrate"` // 是否自动迁移表结构，生产环境建议设为false
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `yaml:"level"`
	Format     string `yaml:"format"`
	Output     string `yaml:"output"`
	FilePath   string `yaml:"file_path"`
	MaxSize    int    `yaml:"max_size"`
	MaxBackups int    `yaml:"max_backups"`
	MaxAge     int    `yaml:"max_age"`
	Compress   bool   `yaml:"compress"`
}

// SchedulerConfig 调度器配置
type SchedulerConfig struct {
	HeartbeatInterval     time.Duration `yaml:"heartbeat_interval"`
	HeartbeatTimeout      time.Duration `yaml:"heartbeat_timeout"`
	ActiveInstanceTimeout time.Duration `yaml:"active_instance_timeout"`
	PreprocessLockTimeout time.Duration `yaml:"preprocess_lock_timeout"`
	TaskAssignmentTimeout time.Duration `yaml:"task_assignment_timeout"`
}

// CORSConfig CORS配置
type CORSConfig struct {
	AllowOrigins     []string `yaml:"allow_origins"`
	AllowMethods     []string `yaml:"allow_methods"`
	AllowHeaders     []string `yaml:"allow_headers"`
	AllowCredentials bool     `yaml:"allow_credentials"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret string `yaml:"secret"`
}

type OtherConfig struct {
	AesKey string `yaml:"aes_key"`
}

type DbConfig struct {
	Database string `yaml:"database"`
	Host     string `yaml:"host"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
}

type LarkConfig struct {
	AppId     string `yaml:"app_id"`
	AppSecret string `yaml:"app_secret"`
}

type MongoConfig struct {
	Address  string `yaml:"address"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
}

type AliyunConfig struct {
	AccessKey   string `yaml:"access_key"`
	AccessKeyId string `yaml:"access_key_id"`
}

type YonganConfig struct {
	SNKEY  string `yaml:"snkey"`
	SNUSER string `yaml:"snuser"`
}

type ThirdPartyConfig struct {
	Lark   LarkConfig   `yaml:"lark"`
	Mongo  MongoConfig  `yaml:"mongo"`
	Aliyun AliyunConfig `yaml:"aliyun"`
	Yongan YonganConfig `yaml:"yongan"`
}

var globalConfig *Config

// Load 加载配置文件
func Load() (*Config, error) {
	env := getEnv()
	configFile := fmt.Sprintf("conf/config-%s.yaml", env)

	// 检查配置文件是否存在
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configFile)
	}

	// 读取配置文件
	data, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析YAML
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 应用环境变量覆盖
	applyEnvOverrides(&config)

	// 解密配置中的敏感数据
	if err := decryptSensitiveData(&config); err != nil {
		return nil, fmt.Errorf("解密配置失败: %v", err)
	}

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	globalConfig = &config
	fmt.Printf("✅ 配置加载成功: 环境=%s, 文件=%s\n", env, configFile)

	return &config, nil
}

// Get 获取全局配置
func Get() *Config {
	return globalConfig
}

// getEnv 获取环境变量，默认为local
func getEnv() string {
	env := os.Getenv("APP_ENV")
	if env == "" {
		env = "local"
	}

	// 验证环境名称
	validEnvs := []string{"local", "dev", "test", "pre", "prod"}
	for _, validEnv := range validEnvs {
		if env == validEnv {
			return env
		}
	}

	fmt.Printf("⚠️ 无效的环境变量 APP_ENV=%s, 使用默认值: local\n", env)
	return "local"
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	if config.App.Name == "" {
		return fmt.Errorf("app.name 不能为空")
	}

	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		return fmt.Errorf("server.port 必须在 1-65535 范围内")
	}

	if config.Database.Host == "" {
		return fmt.Errorf("database.host 不能为空")
	}

	if config.Database.Username == "" {
		return fmt.Errorf("database.username 不能为空")
	}

	if config.Database.Database == "" {
		return fmt.Errorf("database.database 不能为空")
	}

	return nil
}

// GetDSN 获取数据库连接字符串
func (c *Config) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%t&loc=%s",
		c.Database.Username,
		c.Database.Password,
		c.Database.Host,
		c.Database.Port,
		c.Database.Database,
		c.Database.Charset,
		c.Database.ParseTime,
		c.Database.Loc,
	)
}

// GetServerAddr 获取服务器地址
func (c *Config) GetServerAddr() string {
	return fmt.Sprintf("%s:%d", c.Server.Host, c.Server.Port)
}

// applyEnvOverrides 应用环境变量覆盖
func applyEnvOverrides(config *Config) {
	// 服务器端口覆盖
	if port := os.Getenv("SERVER_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Server.Port = p
			fmt.Printf("🔧 环境变量覆盖: SERVER_PORT=%d\n", p)
		}
	}

	// 数据库主机覆盖
	if host := os.Getenv("DB_HOST"); host != "" {
		config.Database.Host = host
		fmt.Printf("🔧 环境变量覆盖: DB_HOST=%s\n", host)
	}

	// 数据库端口覆盖
	if port := os.Getenv("DB_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Database.Port = p
			fmt.Printf("🔧 环境变量覆盖: DB_PORT=%d\n", p)
		}
	}

	// 数据库用户名覆盖
	if username := os.Getenv("DB_USERNAME"); username != "" {
		config.Database.Username = username
		fmt.Printf("🔧 环境变量覆盖: DB_USERNAME=%s\n", username)
	}

	// 数据库密码覆盖
	if password := os.Getenv("DB_PASSWORD"); password != "" {
		config.Database.Password = password
		fmt.Printf("🔧 环境变量覆盖: DB_PASSWORD=***\n")
	}

	// 数据库名称覆盖
	if database := os.Getenv("DB_DATABASE"); database != "" {
		config.Database.Database = database
		fmt.Printf("🔧 环境变量覆盖: DB_DATABASE=%s\n", database)
	}

	// 日志级别覆盖
	if level := os.Getenv("LOG_LEVEL"); level != "" {
		config.Log.Level = level
		fmt.Printf("🔧 环境变量覆盖: LOG_LEVEL=%s\n", level)
	}
}

// decryptSensitiveData 解密配置中的敏感数据
func decryptSensitiveData(config *Config) error {
	// 创建加密管理器
	cm, err := NewCryptoManager()
	if err != nil {
		// 如果无法创建加密管理器（比如没有密钥），跳过解密
		fmt.Printf("⚠️ 无法创建加密管理器，跳过解密: %v\n", err)
		return nil
	}

	// 解密配置
	if err := cm.DecryptConfig(config); err != nil {
		return err
	}

	fmt.Printf("🔐 配置解密完成\n")
	return nil
}
