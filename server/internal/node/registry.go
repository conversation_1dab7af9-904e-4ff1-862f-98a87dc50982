package node

import (
	"fmt"
	"sec-flow-server/internal/node/base"
	"sync"
)

// Registry 节点注册中心
type Registry struct {
	nodes map[string]base.Node
	mutex sync.RWMutex
}

// NewRegistry 创建节点注册中心
func NewRegistry() *Registry {
	return &Registry{
		nodes: make(map[string]base.Node),
	}
}

// Register 注册节点
func (r *Registry) Register(node base.Node) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	nodeID := node.GetID()
	if nodeID == "" {
		return fmt.Errorf("node ID cannot be empty")
	}

	if _, exists := r.nodes[nodeID]; exists {
		return fmt.Errorf("node with ID %s already exists", nodeID)
	}

	r.nodes[nodeID] = node
	return nil
}

// GetNode 获取节点
func (r *Registry) GetNode(nodeID string) (base.Node, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	node, exists := r.nodes[nodeID]
	if !exists {
		return nil, fmt.Errorf("node with ID %s not found", nodeID)
	}

	return node, nil
}

// GetAllNodes 获取所有节点
func (r *Registry) GetAllNodes() []base.Node {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	nodes := make([]base.Node, 0, len(r.nodes))
	for _, node := range r.nodes {
		nodes = append(nodes, node)
	}

	return nodes
}

// GetNodesByCategory 根据分类获取节点
func (r *Registry) GetNodesByCategory(category string) []base.Node {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var nodes []base.Node
	for _, node := range r.nodes {
		if node.GetConfig().Category == category {
			nodes = append(nodes, node)
		}
	}

	return nodes
}

// GetNodeConfigs 获取所有节点配置
func (r *Registry) GetNodeConfigs() []*base.NodeConfig {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	configs := make([]*base.NodeConfig, 0, len(r.nodes))
	for _, node := range r.nodes {
		configs = append(configs, node.GetConfig())
	}

	return configs
}

// GetNodeConfig 获取单个节点配置
func (r *Registry) GetNodeConfig(nodeID string) (*base.NodeConfig, error) {
	node, err := r.GetNode(nodeID)
	if err != nil {
		return nil, err
	}

	return node.GetConfig(), nil
}

// 全局注册中心实例
var globalRegistry *Registry
var once sync.Once

// GetGlobalRegistry 获取全局注册中心
func GetGlobalRegistry() *Registry {
	once.Do(func() {
		globalRegistry = NewRegistry()
	})
	return globalRegistry
}
