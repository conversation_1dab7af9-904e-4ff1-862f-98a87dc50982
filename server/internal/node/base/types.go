package base

import "context"

// Node 节点接口
type Node interface {
	// GetConfig 获取节点配置（用于前端渲染）
	GetConfig() *NodeConfig
	// Execute 执行节点逻辑
	Execute(ctx context.Context, input *ExecutionInput) (*ExecutionOutput, error)
	// Validate 验证节点参数
	Validate(params map[string]interface{}) error
	// GetID 获取节点ID
	GetID() string
	// Example 生成示例数据，展示节点如何处理数据
	Example(input *ExampleInput) *ExampleOutput
}

// NodeConfig 节点配置
type NodeConfig struct {
	ID          string      `json:"id"`
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Category    string      `json:"category"`
	NodeType    string      `json:"nodeType"`
	Icon        string      `json:"icon"`
	FormFields  []FormField `json:"formFields"`
	Style       NodeStyle   `json:"style"`
}

// FormField 表单字段
type FormField struct {
	Key          string                 `json:"key"`
	Label        string                 `json:"label"`
	Type         string                 `json:"type"` // input, select, textarea
	Required     bool                   `json:"required"`
	DefaultValue interface{}            `json:"defaultValue"`
	Options      []FormFieldOption      `json:"options,omitempty"`
	Placeholder  string                 `json:"placeholder,omitempty"`
	Validation   map[string]interface{} `json:"validation,omitempty"`
}

// FormFieldOption 表单字段选项
type FormFieldOption struct {
	Label string                 `json:"label"`
	Value string                 `json:"value"`
	Extra map[string]interface{} `json:"extra,omitempty"` // 额外配置信息
}

// NodeStyle 节点样式
type NodeStyle struct {
	Fill         string `json:"fill"`
	Stroke       string `json:"stroke"`
	StrokeWidth  int    `json:"strokeWidth"`
	FontSize     int    `json:"fontSize"`
	FontColor    string `json:"fontColor"`
	BorderRadius int    `json:"borderRadius,omitempty"`
}

// ExecutionInput 执行输入
type ExecutionInput struct {
	NodeID      string                 `json:"nodeId"`
	Params      map[string]interface{} `json:"params"`     // 节点配置参数
	FlowData    map[string]interface{} `json:"flowData"`   // 流程共享数据（主要的数据载体）
	PrevOutput  map[string]interface{} `json:"prevOutput"` // 上一个节点的输出（用于调试和日志）
	FlowID      string                 `json:"flowId"`
	ExecutionID string                 `json:"executionId"`
	CurrentStep int                    `json:"currentStep"`
}

// ExecutionOutput 执行输出
type ExecutionOutput struct {
	Success   bool                   `json:"success"`             // 节点是否执行成功
	Continue  bool                   `json:"continue"`            // 是否继续执行后续节点
	Data      map[string]interface{} `json:"data"`                // 节点输出数据（用于调试和日志）
	FlowData  map[string]interface{} `json:"flowData"`            // 更新后的流程共享数据
	Message   string                 `json:"message"`             // 执行消息
	Error     string                 `json:"error,omitempty"`     // 错误信息
	Metadata  map[string]interface{} `json:"metadata,omitempty"`  // 元数据（如执行时间、资源使用等）
	NextNodes []string               `json:"nextNodes,omitempty"` // 下一个要执行的节点（用于条件分支）
}

// ExampleInput 示例输入数据
type ExampleInput struct {
	FlowData map[string]interface{} `json:"flowData"` // 当前流程数据
	Config   map[string]interface{} `json:"config"`   // 节点配置参数
}

// ExampleOutput 示例输出数据
type ExampleOutput struct {
	FlowData    map[string]interface{} `json:"flowData"`    // 处理后的流程数据
	Description string                 `json:"description"` // 节点处理描述
	Changes     []DataChange           `json:"changes"`     // 数据变化详情
}

// DataChange 描述数据变化
type DataChange struct {
	Field       string      `json:"field"`              // 变化的字段
	Action      string      `json:"action"`             // 操作类型: "add", "update", "delete"
	OldValue    interface{} `json:"oldValue,omitempty"` // 旧值
	NewValue    interface{} `json:"newValue,omitempty"` // 新值
	Description string      `json:"description"`        // 变化描述
}

// ExecutionContext 执行上下文
type ExecutionContext struct {
	FlowID      string                      `json:"flowId"`
	ExecutionID string                      `json:"executionId"`
	NodeResults map[string]*ExecutionOutput `json:"nodeResults"`
	FlowData    map[string]interface{}      `json:"flowData"`
	StartTime   int64                       `json:"startTime"`
	CurrentStep int                         `json:"currentStep"`
}

// FlowDefinition 流程定义
type FlowDefinition struct {
	ID    string     `json:"id"`
	Name  string     `json:"name"`
	Nodes []FlowNode `json:"nodes"`
	Edges []FlowEdge `json:"edges"`
}

// FlowNode 流程节点
type FlowNode struct {
	ID         string                 `json:"id"`
	Type       string                 `json:"type"`
	X          float64                `json:"x"`
	Y          float64                `json:"y"`
	Text       string                 `json:"text"`
	Properties map[string]interface{} `json:"properties"`
}

// FlowEdge 流程连线
type FlowEdge struct {
	ID     string `json:"id"`
	Source string `json:"source"`
	Target string `json:"target"`
	Type   string `json:"type"`
}
