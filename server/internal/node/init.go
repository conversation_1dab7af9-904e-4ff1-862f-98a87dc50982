package node

import (
	"log"
	"sec-flow-server/internal/node/business/aliyun_log_query"
	"sec-flow-server/internal/node/business/employee_resigned_scan"
	"sec-flow-server/internal/node/business/event_generation"
	"sec-flow-server/internal/node/business/event_suppression"
	"sec-flow-server/internal/node/business/ip_info"
	"sec-flow-server/internal/node/business/lark_notice"
	"sec-flow-server/internal/node/business/mongo_event"
	"sec-flow-server/internal/node/business/mongo_query"

	"sec-flow-server/internal/node/business/prime_decode"
	"sec-flow-server/internal/node/business/user_info"
	"sec-flow-server/internal/node/common/aggregate"
	"sec-flow-server/internal/node/common/api_call"
	"sec-flow-server/internal/node/common/array_filter"
	"sec-flow-server/internal/node/common/condition"
	"sec-flow-server/internal/node/common/cron_start"
	"sec-flow-server/internal/node/common/data_transform"
	"sec-flow-server/internal/node/common/http_start"
	"sec-flow-server/internal/node/common/iterator"
	"sec-flow-server/internal/node/common/print"
)

// InitNodes 初始化所有节点
func InitNodes() {
	registry := GetGlobalRegistry()

	// 注册用户信息补充节点
	userInfoNode := user_info.NewUserInfoNode()
	if err := registry.Register(userInfoNode); err != nil {
		log.Printf("Failed to register user info node: %v", err)
	} else {
		log.Printf("Registered node: %s", userInfoNode.GetID())
	}

	// 注册数据转换节点
	dataTransformNode := data_transform.NewDataTransformNode()
	if err := registry.Register(dataTransformNode); err != nil {
		log.Printf("Failed to register data transform node: %v", err)
	} else {
		log.Printf("Registered node: %s", dataTransformNode.GetID())
	}

	// 注册API调用节点
	apiCallNode := api_call.NewAPICallNode()
	if err := registry.Register(apiCallNode); err != nil {
		log.Printf("Failed to register API call node: %v", err)
	} else {
		log.Printf("Registered node: %s", apiCallNode.GetID())
	}

	// 注册条件判断节点
	conditionNode := condition.NewConditionNode()
	if err := registry.Register(conditionNode); err != nil {
		log.Printf("Failed to register condition node: %v", err)
	} else {
		log.Printf("Registered node: %s", conditionNode.GetID())
	}

	// 注册Cron开始节点
	cronStartNode := cron_start.NewCronStartNode()
	if err := registry.Register(cronStartNode); err != nil {
		log.Printf("Failed to register cron start node: %v", err)
	} else {
		log.Printf("Registered node: %s", cronStartNode.GetID())
	}

	// 注册HTTP开始节点
	httpStartNode := http_start.NewHttpStartNode()
	if err := registry.Register(httpStartNode); err != nil {
		log.Printf("Failed to register http start node: %v", err)
	} else {
		log.Printf("Registered node: %s", httpStartNode.GetID())
	}

	// 注册Print节点
	printNode := print.NewPrintNode()
	if err := registry.Register(printNode); err != nil {
		log.Printf("Failed to register print node: %v", err)
	} else {
		log.Printf("Registered node: %s", printNode.GetID())
	}

	// 注册数组过滤节点
	arrayFilterNode := array_filter.NewArrayFilterNode()
	if err := registry.Register(arrayFilterNode); err != nil {
		log.Printf("Failed to register array filter node: %v", err)
	} else {
		log.Printf("Registered node: %s", arrayFilterNode.GetID())
	}

	// 注册聚合节点
	aggregateNode := aggregate.NewAggregateNode()
	if err := registry.Register(aggregateNode); err != nil {
		log.Printf("Failed to register aggregate node: %v", err)
	} else {
		log.Printf("Registered node: %s", aggregateNode.GetID())
	}

	larkNoticeNode := lark_notice.NewLarkNoticeNode()
	if err := registry.Register(larkNoticeNode); err != nil {
		log.Printf("Failed to register lark notice node: %v", err)
	} else {
		log.Printf("Registered node: %s", larkNoticeNode.GetID())
	}

	// 注册事件生成节点
	eventGenerationNode := event_generation.NewEventGenerationNode()
	if err := registry.Register(eventGenerationNode); err != nil {
		log.Printf("Failed to register event generation node: %v", err)
	} else {
		log.Printf("Registered node: %s", eventGenerationNode.GetID())
	}

	// 注册事件压制节点
	eventSuppressionNode := event_suppression.NewEventSuppressionNode()
	if err := registry.Register(eventSuppressionNode); err != nil {
		log.Printf("Failed to register event suppression node: %v", err)
	} else {
		log.Printf("Registered node: %s", eventSuppressionNode.GetID())
	}

	// 注册迭代器节点
	iteratorNode := iterator.NewIteratorNode()
	if err := registry.Register(iteratorNode); err != nil {
		log.Printf("Failed to register iterator node: %v", err)
	} else {
		log.Printf("Registered node: %s", iteratorNode.GetID())
	}

	// 注册连锁解码手机号日志节点
	primeDecodeNode := prime_decode.NewPrimeDecodeNode()
	if err := registry.Register(primeDecodeNode); err != nil {
		log.Printf("Failed to register prime decode node: %v", err)
	} else {
		log.Printf("Registered node: %s", primeDecodeNode.GetID())
	}

	// 注册通用阿里云日志查询节点
	aliyunLogQueryNode := aliyun_log_query.NewAliyunLogQueryNode()
	if err := registry.Register(aliyunLogQueryNode); err != nil {
		log.Printf("Failed to register aliyun log query node: %v", err)
	} else {
		log.Printf("Registered node: %s", aliyunLogQueryNode.GetID())
	}
	// 注册Mongo事件节点
	mongoEventNode := mongo_event.NewMongoEventNode()
	if err := registry.Register(mongoEventNode); err != nil {
		log.Printf("Failed to register mongo event node: %v", err)
	} else {
		log.Printf("Registered node: %s", mongoEventNode.GetID())
	}

	// 注册Mongo查询节点
	mongoQueryNode := mongo_query.NewMongoQueryNode()
	if err := registry.Register(mongoQueryNode); err != nil {
		log.Printf("Failed to register mongo query node: %v", err)
	} else {
		log.Printf("Registered node: %s", mongoQueryNode.GetID())
	}

	// 注册IP查询节点
	ipInfoNode := ip_info.NewIPInfoNode()
	if err := registry.Register(ipInfoNode); err != nil {
		log.Printf("Failed to register ip info node: %v", err)
	} else {
		log.Printf("Registered node: %s", ipInfoNode.GetID())
	}

	// 注册员工离职扫描节点
	employeeResignedScanNode := employee_resigned_scan.NewEmployeeResignedScanNode()
	if err := registry.Register(employeeResignedScanNode); err != nil {
		log.Printf("Failed to register employee resigned scan node: %v", err)
	} else {
		log.Printf("Registered node: %s", employeeResignedScanNode.GetID())
	}

	log.Printf("Node initialization completed. Total nodes: %d", len(registry.GetAllNodes()))
}
