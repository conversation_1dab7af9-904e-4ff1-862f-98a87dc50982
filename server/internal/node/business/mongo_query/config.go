package mongo_query

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取Mongo查询节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "mongo_query",
		Name:        "Mongo查询",
		Description: "从MongoDB数据库中执行查询语句，获取数据并输出到指定字段",
		Category:    "business",
		NodeType:    "rect",
		Icon:        "search",
		FormFields: []base.FormField{
			{
				Key:          "dbName",
				Label:        "数据库名称",
				Type:         "select",
				Required:     true,
				DefaultValue: "Reaper",
				Options: []base.FormFieldOption{
					{Label: "Reaper", Value: "Reaper"},
					{Label: "app", Value: "app"},
				},
			},
			{
				Key:          "collection",
				Label:        "集合名称",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "Mongo集合名称",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 100,
				},
			},
			{
				Key:          "queryType",
				Label:        "查询类型",
				Type:         "radio",
				Required:     true,
				DefaultValue: "find",
				Options: []base.FormFieldOption{
					{Label: "普通查询", Value: "find"},
					{Label: "Pipeline聚合", Value: "pipeline"},
				},
			},
			{
				Key:          "query",
				Label:        "查询语句",
				Type:         "textarea",
				Required:     true,
				DefaultValue: "{}",
				Placeholder:  "普通查询：MongoDB查询条件，JSON格式\nPipeline聚合：聚合管道数组，JSON格式\n支持${var}变量（支持多级，如 data.user.id）",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 5000,
				},
			},
			{
				Key:          "outputField",
				Label:        "输出字段",
				Type:         "input",
				Required:     true,
				DefaultValue: "mongoQueryResult",
				Placeholder:  "支持JSON路径，如: result.data, queryData",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 50,
				},
			},
			{
				Key:          "sampleData",
				Label:        "样例查询结果",
				Type:         "textarea",
				Required:     true,
				DefaultValue: `[{"_id": "507f1f77bcf86cd799439011", "name": "示例文档", "status": "active", "createdAt": "2025-01-04T10:30:00Z"}]`,
				Placeholder:  "请输入JSON格式的样例查询结果数据，用于流程预览和调试",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 10000,
				},
			},
		},
		Style: base.NodeStyle{
			Fill:         "#f6ffed",
			Stroke:       "#52c41a",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}
}
