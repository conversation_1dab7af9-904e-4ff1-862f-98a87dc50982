package mongo_query

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"sec-flow-server/internal/node/base"
	mongoSvc "sec-flow-server/internal/service/third_party/mongo"
	"sec-flow-server/internal/utils"
)

// MongoQueryNode Mongo查询节点
type MongoQueryNode struct {
	config *base.NodeConfig
}

// NewMongoQueryNode 构造函数
func NewMongoQueryNode() *MongoQueryNode {
	return &MongoQueryNode{config: GetNodeConfig()}
}

// GetID 获取节点ID
func (n *MongoQueryNode) GetID() string { return n.config.ID }

// GetConfig 获取节点配置
func (n *MongoQueryNode) GetConfig() *base.NodeConfig { return n.config }

// Validate 基本校验
func (n *MongoQueryNode) Validate(params map[string]interface{}) error {
	// 基础校验
	dbName, ok := params["dbName"].(string)
	if !ok || dbName == "" {
		return fmt.Errorf("dbName is required")
	}

	collection, ok := params["collection"].(string)
	if !ok || collection == "" {
		return fmt.Errorf("collection is required")
	}

	// 查询类型校验
	queryType := "find"
	if v, ok := params["queryType"].(string); ok && v != "" {
		queryType = v
	}
	if queryType != "find" && queryType != "pipeline" {
		return fmt.Errorf("invalid queryType: %s", queryType)
	}

	// 查询语句必填
	if s, ok := params["query"].(string); !ok || strings.TrimSpace(s) == "" {
		return fmt.Errorf("query is required and must be JSON string")
	}

	// 输出字段必填
	if outputField, ok := params["outputField"]; !ok || outputField == "" {
		return fmt.Errorf("outputField is required")
	}

	return nil
}

// Execute 执行
func (n *MongoQueryNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行Mongo查询节点: %s", input.NodeID)

	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{Success: false, Continue: false, Error: err.Error(), FlowData: input.FlowData}, nil
	}

	dbName := input.Params["dbName"].(string)
	collection := input.Params["collection"].(string)
	outputField := input.Params["outputField"].(string)

	// 获取查询类型
	queryType := "find"
	if v, ok := input.Params["queryType"].(string); ok && v != "" {
		queryType = v
	}

	// 处理查询语句，支持变量替换
	queryStr := input.Params["query"].(string)
	// 先对未被引号包裹的占位符进行 JSON 级替换（可注入数组/对象）
	queryStr = utils.ReplaceUnquotedVariablesInJSONTemplate(queryStr, input.FlowData)

	// 执行查询
	var results []map[string]interface{}
	var queryObj interface{}
	var err error

	if queryType == "pipeline" {
		// Pipeline 聚合查询
		var pipeline []interface{}
		if err := json.Unmarshal([]byte(queryStr), &pipeline); err != nil {
			return &base.ExecutionOutput{Success: false, Continue: false, Error: fmt.Sprintf("invalid pipeline JSON: %v", err), FlowData: input.FlowData}, nil
		}

		// 对解析后的 pipeline 执行占位符替换
		replaced := utils.ReplaceVariablesInObject(pipeline, input.FlowData)
		if p, okCast := replaced.([]interface{}); okCast {
			pipeline = p
		}

		queryObj = pipeline
		err = mongoSvc.Aggregate(ctx, dbName, collection, pipeline, &results)
	} else {
		// 普通查询
		var filter map[string]interface{}
		if err := json.Unmarshal([]byte(queryStr), &filter); err != nil {
			return &base.ExecutionOutput{Success: false, Continue: false, Error: fmt.Sprintf("invalid query JSON: %v", err), FlowData: input.FlowData}, nil
		}

		// 对解析后的对象执行占位符替换（仅替换字符串值内部的占位符）
		replaced := utils.ReplaceVariablesInObject(filter, input.FlowData)
		if m, okCast := replaced.(map[string]interface{}); okCast {
			filter = m
		}

		queryObj = filter
		err = mongoSvc.FindMany(ctx, dbName, collection, filter, &results)
	}

	if err != nil {
		return &base.ExecutionOutput{Success: false, Continue: false, Error: fmt.Sprintf("query failed: %v", err), FlowData: input.FlowData}, nil
	}

	// 构造输出数据
	outputData := map[string]interface{}{
		"queryType":   queryType,
		"query":       queryObj,
		"collection":  collection,
		"database":    dbName,
		"recordCount": len(results),
		"data":        results,
		"executedAt":  time.Now().Format("2006-01-02 15:04:05"),
	}

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 将查询结果添加到流程数据中 (支持JSON路径表达式)
	if !utils.SetFieldValue(updatedFlowData, outputField, outputData) {
		// 如果设置失败，回退到简单字段设置
		updatedFlowData[outputField] = outputData
	}

	// 构造成功消息
	var message string
	if queryType == "pipeline" {
		message = fmt.Sprintf("成功执行MongoDB聚合查询，从 %s.%s 中获得 %d 条结果",
			dbName, collection, len(results))
	} else {
		message = fmt.Sprintf("成功查询MongoDB，从 %s.%s 中找到 %d 条记录",
			dbName, collection, len(results))
	}

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true,
		Data:     outputData,
		FlowData: updatedFlowData,
		Message:  message,
		Metadata: map[string]interface{}{
			"nodeType":    "mongo_query",
			"queryType":   queryType,
			"outputField": outputField,
			"recordCount": len(results),
			"database":    dbName,
			"collection":  collection,
			"processedAt": time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// Example 生成示例数据
func (n *MongoQueryNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置参数
	outputField := "mongoQueryResult"
	sampleData := `[{"_id": "507f1f77bcf86cd799439011", "name": "示例文档", "status": "active", "createdAt": "2025-01-04T10:30:00Z"}]`
	dbName := "Reaper"
	collection := "events"
	query := "{}"
	queryType := "find"

	if input.Config != nil {
		if of, ok := input.Config["outputField"].(string); ok && of != "" {
			outputField = of
		}
		if sd, ok := input.Config["sampleData"].(string); ok && sd != "" {
			sampleData = sd
		}
		if db, ok := input.Config["dbName"].(string); ok && db != "" {
			dbName = db
		}
		if coll, ok := input.Config["collection"].(string); ok && coll != "" {
			collection = coll
		}
		if q, ok := input.Config["query"].(string); ok && q != "" {
			query = q
		}
		if qt, ok := input.Config["queryType"].(string); ok && qt != "" {
			queryType = qt
		}
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 解析样例数据
	var sampleResults interface{}
	if err := json.Unmarshal([]byte(sampleData), &sampleResults); err != nil {
		// 如果解析失败，使用默认格式
		sampleResults = []map[string]interface{}{
			{
				"error": "样例数据解析失败",
				"raw":   sampleData,
			},
		}
	}

	// 计算记录数量
	recordCount := getRecordCount(sampleResults)

	// 构造示例输出数据（与实际执行时的格式完全一致）
	outputData := map[string]interface{}{
		"queryType":   queryType,
		"query":       query,
		"collection":  collection,
		"database":    dbName,
		"recordCount": recordCount,
		"data":        sampleResults,
		"executedAt":  time.Now().Format("2006-01-02 15:04:05"),
	}

	// 将查询结果添加到流程数据中 (支持JSON路径表达式)
	if !utils.SetFieldValue(resultFlowData, outputField, outputData) {
		// 如果设置失败，回退到简单字段设置
		resultFlowData[outputField] = outputData
	}

	// 记录数据变化
	var description string
	if queryType == "pipeline" {
		description = fmt.Sprintf("执行MongoDB聚合查询 (数据库: %s, 集合: %s)", dbName, collection)
	} else {
		description = fmt.Sprintf("查询MongoDB数据 (数据库: %s, 集合: %s)", dbName, collection)
	}

	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			NewValue:    outputData,
			Description: description,
		},
	}

	var exampleDescription string
	if queryType == "pipeline" {
		exampleDescription = fmt.Sprintf("执行MongoDB聚合查询，将结果存储到 %s 字段", outputField)
	} else {
		exampleDescription = fmt.Sprintf("查询MongoDB数据，将结果存储到 %s 字段", outputField)
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: exampleDescription,
		Changes:     changes,
	}
}

// getRecordCount 获取记录数量
func getRecordCount(data interface{}) int {
	if results, ok := data.([]interface{}); ok {
		return len(results)
	}
	return 0
}
