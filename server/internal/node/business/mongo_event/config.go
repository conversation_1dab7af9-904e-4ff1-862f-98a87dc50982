package mongo_event

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取Mongo事件节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "mongo_event",
		Name:        "Mongo事件",
		Description: "向MongoDB写入/更新事件",
		Category:    "business",
		NodeType:    "rect",
		Icon:        "database",
		FormFields: []base.FormField{
			{
				Key:          "dbName",
				Label:        "数据库名称",
				Type:         "select",
				Required:     true,
				DefaultValue: "Reaper",
				Options: []base.FormFieldOption{
					{Label: "Reaper", Value: "Reaper"},
					{Label: "app", Value: "app"},
				},
			},
			{
				Key:          "collection",
				Label:        "集合名称",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "Mongo集合名称",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 100,
				},
			},
			{
				Key:          "checkMode",
				Label:        "检查模式",
				Type:         "select",
				Required:     true,
				DefaultValue: "none",
				Options: []base.FormFieldOption{
					{Label: "不检查", Value: "none"},
					{Label: "检查是否存在", Value: "exists"},
				},
			},
			{
				Key:          "actionIfAbsent",
				Label:        "不存在时动作",
				Type:         "select",
				Required:     true,
				DefaultValue: "insert",
				Options: []base.FormFieldOption{
					{Label: "不操作", Value: "none"},
					{Label: "插入并继续", Value: "insert"},
				},
			},
			{
				Key:          "actionIfExists",
				Label:        "存在时动作",
				Type:         "select",
				Required:     true,
				DefaultValue: "none_stop",
				Options: []base.FormFieldOption{
					{Label: "不操作并停止", Value: "none_stop"},
					{Label: "更新并继续", Value: "update_continue"},
					{Label: "更新并停止", Value: "update_stop"},
					{Label: "增长则更新并继续，否则停止", Value: "update_if_increased_continue_else_stop"},
				},
			},
			{
				Key:          "doc",
				Label:        "文档内容",
				Type:         "textarea",
				Required:     true,
				DefaultValue: "{}",
				Placeholder:  "完整的JSON文档，支持${var}变量（支持多级，如 data.user.id）",
			},
			{
				Key:          "dedupFields",
				Label:        "去重字段",
				Type:         "textarea",
				Required:     false,
				DefaultValue: "[]",
				Placeholder:  "JSON数组或逗号分隔的字段路径，如 [\"eventId\"] 或 eventId, data.user.id",
			},
			{
				Key:          "updateFields",
				Label:        "允许更新字段",
				Type:         "textarea",
				Required:     false,
				DefaultValue: "[]",
				Placeholder:  "JSON数组或逗号分隔的字段路径，如 [\"count\"] 或 count, data.score",
			},
			{
				Key:          "compareFields",
				Label:        "比较字段",
				Type:         "textarea",
				Required:     false,
				DefaultValue: "[]",
				Placeholder:  "JSON数组或逗号分隔，用于增长判断的字段路径，仅比较数值",
			},
		},
		Style: base.NodeStyle{
			Fill:         "#fff7e6",
			Stroke:       "#fa8c16",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}
}
