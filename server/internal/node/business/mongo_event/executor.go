package mongo_event

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"

	"sec-flow-server/internal/node/base"
	mongoSvc "sec-flow-server/internal/service/third_party/mongo"
	"sec-flow-server/internal/utils"
)

// MongoEventNode Mongo事件节点
type MongoEventNode struct {
	config *base.NodeConfig
}

// NewMongoEventNode 构造函数
func NewMongoEventNode() *MongoEventNode {
	return &MongoEventNode{config: GetNodeConfig()}
}

// GetID 获取节点ID
func (n *MongoEventNode) GetID() string { return n.config.ID }

// GetConfig 获取节点配置
func (n *MongoEventNode) GetConfig() *base.NodeConfig { return n.config }

// Validate 基本校验
func (n *MongoEventNode) Validate(params map[string]interface{}) error {
	// 基础校验
	dbName, ok := params["dbName"].(string)
	if !ok || dbName == "" {
		return fmt.Errorf("dbName is required")
	}
	coll, ok := params["collection"].(string)
	if !ok || coll == "" {
		return fmt.Errorf("collection is required")
	}

	// 策略字段
	checkMode := "none"
	if v, ok := params["checkMode"].(string); ok && v != "" {
		checkMode = v
	}
	if checkMode != "none" && checkMode != "exists" {
		return fmt.Errorf("invalid checkMode: %s", checkMode)
	}

	actionIfAbsent := "insert"
	if v, ok := params["actionIfAbsent"].(string); ok && v != "" {
		actionIfAbsent = v
	}
	if actionIfAbsent != "none" && actionIfAbsent != "insert" {
		return fmt.Errorf("invalid actionIfAbsent: %s", actionIfAbsent)
	}

	actionIfExists := "none_stop"
	if v, ok := params["actionIfExists"].(string); ok && v != "" {
		actionIfExists = v
	}
	validExists := map[string]bool{
		"none_stop":                              true,
		"update_continue":                        true,
		"update_stop":                            true,
		"update_if_increased_continue_else_stop": true,
	}
	if !validExists[actionIfExists] {
		return fmt.Errorf("invalid actionIfExists: %s", actionIfExists)
	}

	// 文档内容必填
	if s, ok := params["doc"].(string); !ok || strings.TrimSpace(s) == "" {
		return fmt.Errorf("doc is required and must be JSON string")
	}
	// checkMode=exists 时，要求提供去重字段
	if checkMode == "exists" {
		fields := parseStringListParam(params["dedupFields"])
		if len(fields) == 0 {
			return fmt.Errorf("dedupFields is required when checkMode=exists")
		}
	}

	return nil
}

// Execute 执行
func (n *MongoEventNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行Mongo事件节点: %s", input.NodeID)

	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{Success: false, Continue: false, Error: err.Error(), FlowData: input.FlowData}, nil
	}

	dbName := input.Params["dbName"].(string)
	coll := input.Params["collection"].(string)

	// 读取控制参数
	checkMode := "none"
	if v, ok := input.Params["checkMode"].(string); ok && v != "" {
		checkMode = v
	}
	actionIfAbsent := "insert"
	if v, ok := input.Params["actionIfAbsent"].(string); ok && v != "" {
		actionIfAbsent = v
	}
	actionIfExists := "none_stop"
	if v, ok := input.Params["actionIfExists"].(string); ok && v != "" {
		actionIfExists = v
	}

	// 先对未被引号包裹的占位符进行 JSON 级替换（可注入数组/对象）
	// 再解析为对象，并对对象内字符串字段做占位符替换
	docObj := map[string]interface{}{}
	if s, ok := input.Params["doc"].(string); ok && strings.TrimSpace(s) != "" {
		s = utils.ReplaceUnquotedVariablesInJSONTemplate(s, input.FlowData)
		if err := json.Unmarshal([]byte(s), &docObj); err != nil {
			return &base.ExecutionOutput{Success: false, Continue: false, Error: fmt.Sprintf("invalid doc JSON: %v", err), FlowData: input.FlowData}, nil
		}
		// 对解析后的对象执行占位符替换（仅替换字符串值内部的占位符）
		replaced := utils.ReplaceVariablesInObject(docObj, input.FlowData)
		if m, okCast := replaced.(map[string]interface{}); okCast {
			docObj = m
		}
	}

	// 从文档中提取去重/更新/比较字段
	dedupFields := parseStringListParam(input.Params["dedupFields"])     // []string
	updateFields := parseStringListParam(input.Params["updateFields"])   // []string
	compareFields := parseStringListParam(input.Params["compareFields"]) // []string

	// 构造 dedup 过滤
	dedupObj := make(map[string]interface{})
	for _, f := range dedupFields {
		if v, ok := utils.GetFieldValue(docObj, f); ok {
			dedupObj[f] = v
		}
	}

	// 工具：从 doc 选择白名单字段，构建 $set 对象（支持多级路径，以扁平路径为key写入）
	filterByWhitelist := func(src map[string]interface{}, white []string) map[string]interface{} {
		if len(white) == 0 {
			// 默认：不做限制，使用整个 docObj
			// 但考虑只更新必要字段，仍然按全部扁平路径写入
			flat := make(map[string]interface{})
			flattenJSON("", src, flat)
			return flat
		}
		res := make(map[string]interface{}, len(white))
		for _, k := range white {
			if v, ok := utils.GetFieldValue(src, k); ok {
				res[k] = v
			}
		}
		return res
	}

	// 辅助更新函数
	doUpdate := func() (interface{}, error) {
		set := filterByWhitelist(docObj, updateFields)
		if len(set) == 0 {
			return map[string]interface{}{"updated": 0, "reason": "empty update set"}, nil
		}
		return mongoSvc.UpdateOne(ctx, dbName, coll, dedupObj, map[string]interface{}{"$set": set})
	}

	// 如果不检查，根据不存在时动作处理
	if checkMode == "none" {
		if actionIfAbsent == "insert" {
			res, err := mongoSvc.InsertOne(ctx, dbName, coll, docObj)
			if err != nil {
				return &base.ExecutionOutput{Success: false, Continue: false, Error: err.Error(), FlowData: input.FlowData}, nil
			}
			return &base.ExecutionOutput{Success: true, Continue: true, Data: map[string]interface{}{"inserted": true, "result": res}, FlowData: input.FlowData, Message: "未检查：插入并继续"}, nil
		}
		return &base.ExecutionOutput{Success: true, Continue: true, Data: map[string]interface{}{"noop": true}, FlowData: input.FlowData, Message: "未检查：不操作并继续"}, nil
	}

	// 检查存在
	var exist map[string]interface{}
	findErr := mongoSvc.FindOne(ctx, dbName, coll, dedupObj, &exist)
	found := (findErr == nil && len(exist) > 0)
	if findErr != nil && findErr.Error() != "mongo: no documents in result" {
		return &base.ExecutionOutput{Success: false, Continue: false, Error: fmt.Sprintf("find failed: %v", findErr), FlowData: input.FlowData}, nil
	}

	if !found {
		// 不存在：根据动作
		if actionIfAbsent == "insert" {
			res, err := mongoSvc.InsertOne(ctx, dbName, coll, docObj)
			if err != nil {
				return &base.ExecutionOutput{Success: false, Continue: false, Error: err.Error(), FlowData: input.FlowData}, nil
			}
			return &base.ExecutionOutput{Success: true, Continue: true, Data: map[string]interface{}{"inserted": true, "result": res}, FlowData: input.FlowData, Message: "不存在：插入并继续"}, nil
		}
		return &base.ExecutionOutput{Success: true, Continue: true, Data: map[string]interface{}{"notFound": true, "noop": true}, FlowData: input.FlowData, Message: "不存在：不操作并继续"}, nil
	}

	// 已存在：按存在时动作处理
	if actionIfExists == "none_stop" {
		return &base.ExecutionOutput{Success: true, Continue: false, Data: map[string]interface{}{"exists": true, "noop": true}, FlowData: input.FlowData, Message: "已存在：不操作并停止"}, nil
	}
	if actionIfExists == "update_continue" {
		res, err := doUpdate()
		if err != nil {
			return &base.ExecutionOutput{Success: false, Continue: false, Error: err.Error(), FlowData: input.FlowData}, nil
		}
		return &base.ExecutionOutput{Success: true, Continue: true, Data: map[string]interface{}{"updated": true, "result": res}, FlowData: input.FlowData, Message: "已存在：更新并继续"}, nil
	}
	if actionIfExists == "update_stop" {
		res, err := doUpdate()
		if err != nil {
			return &base.ExecutionOutput{Success: false, Continue: false, Error: err.Error(), FlowData: input.FlowData}, nil
		}
		return &base.ExecutionOutput{Success: true, Continue: false, Data: map[string]interface{}{"updated": true, "result": res}, FlowData: input.FlowData, Message: "已存在：更新并停止"}, nil
	}

	// update_if_increased_continue_else_stop：比较字段决定是否更新与是否继续
	// 默认比较字段：compareFields；为空则从 docObj 中选择可解析为数字的字段
	fields := compareFields
	if len(fields) == 0 {
		for k, v := range flattenToMap(docObj) {
			if _, ok := toFloat64(v); ok {
				fields = append(fields, k)
			}
		}
	}
	increased := false
	for _, f := range fields {
		if rawNew, okPath := getByPath(docObj, f); okPath {
			if vNew, okNew := toFloat64(rawNew); okNew {
				if rawOld, exists := getByPath(exist, f); exists {
					if vOldNum, okOld := toFloat64(rawOld); okOld {
						if vNew > vOldNum {
							increased = true
							break
						}
					}
				} else {
					increased = true
					break
				}
			}
		}
	}
	if !increased {
		return &base.ExecutionOutput{Success: true, Continue: false, Data: map[string]interface{}{"exists": true, "increased": false}, FlowData: input.FlowData, Message: "未增长，停止"}, nil
	}
	res, err := doUpdate()
	if err != nil {
		return &base.ExecutionOutput{Success: false, Continue: false, Error: err.Error(), FlowData: input.FlowData}, nil
	}
	return &base.ExecutionOutput{Success: true, Continue: true, Data: map[string]interface{}{"updated": true, "increased": true, "result": res}, FlowData: input.FlowData, Message: "增长，更新并继续"}, nil
}

// Example 生成示例数据
func (n *MongoEventNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	return &base.ExampleOutput{FlowData: input.FlowData, Description: "Mongo事件节点不修改原数据", Changes: []base.DataChange{}}
}

// toFloat64 尝试将任意值转为float64，用于数值比较
func toFloat64(v interface{}) (float64, bool) {
	switch t := v.(type) {
	case int:
		return float64(t), true
	case int8:
		return float64(t), true
	case int16:
		return float64(t), true
	case int32:
		return float64(t), true
	case int64:
		return float64(t), true
	case uint:
		return float64(t), true
	case uint8:
		return float64(t), true
	case uint16:
		return float64(t), true
	case uint32:
		return float64(t), true
	case uint64:
		return float64(t), true
	case float32:
		return float64(t), true
	case float64:
		return t, true
	case string:
		// 如果是字符串，尝试解析为数字
		if s := strings.TrimSpace(t); s != "" {
			if num, err := strconv.ParseFloat(s, 64); err == nil {
				return num, true
			}
		}
		return 0, false
	default:
		return 0, false
	}
}

// parseStringListParam 解析字符串列表参数，支持：
// - JSON数组字符串，如 ["count","score"]
// - 逗号分隔字符串，如 "count, score"
// - 已经是数组类型（[]string 或 []interface{}）
func parseStringListParam(v interface{}) []string {
	result := make([]string, 0)
	if v == nil {
		return result
	}
	switch val := v.(type) {
	case string:
		s := strings.TrimSpace(val)
		if s == "" {
			return result
		}
		// 优先尝试按JSON数组解析
		if strings.HasPrefix(s, "[") {
			var arrStr []string
			if err := json.Unmarshal([]byte(s), &arrStr); err == nil {
				// 去掉空白项
				for _, it := range arrStr {
					item := strings.TrimSpace(it)
					if item != "" {
						result = append(result, item)
					}
				}
				return result
			}
			// 兼容 ["a",1] 这类，转成字符串
			var arrAny []interface{}
			if err := json.Unmarshal([]byte(s), &arrAny); err == nil {
				for _, it := range arrAny {
					item := strings.TrimSpace(fmt.Sprintf("%v", it))
					if item != "" {
						result = append(result, item)
					}
				}
				return result
			}
		}
		// 按逗号分隔
		parts := strings.Split(s, ",")
		for _, p := range parts {
			item := strings.TrimSpace(p)
			if item != "" {
				result = append(result, item)
			}
		}
		return result
	case []string:
		for _, it := range val {
			item := strings.TrimSpace(it)
			if item != "" {
				result = append(result, item)
			}
		}
		return result
	case []interface{}:
		for _, it := range val {
			item := strings.TrimSpace(fmt.Sprintf("%v", it))
			if item != "" {
				result = append(result, item)
			}
		}
		return result
	default:
		// 其他类型一律转字符串并按逗号分隔
		s := strings.TrimSpace(fmt.Sprintf("%v", val))
		if s == "" {
			return result
		}
		parts := strings.Split(s, ",")
		for _, p := range parts {
			item := strings.TrimSpace(p)
			if item != "" {
				result = append(result, item)
			}
		}
		return result
	}
}

// flattenJSON 将嵌套map扁平化为路径->值的map，例如 {a:{b:1}} => {"a.b":1}
func flattenJSON(prefix string, src map[string]interface{}, out map[string]interface{}) {
	for k, v := range src {
		path := k
		if prefix != "" {
			path = prefix + "." + k
		}
		switch child := v.(type) {
		case map[string]interface{}:
			flattenJSON(path, child, out)
		default:
			out[path] = v
		}
	}
}

// flattenToMap 便捷函数：返回扁平化的路径->值映射
func flattenToMap(src map[string]interface{}) map[string]interface{} {
	out := make(map[string]interface{})
	flattenJSON("", src, out)
	return out
}

// getByPath 从嵌套对象中取值（点路径）
func getByPath(obj map[string]interface{}, path string) (interface{}, bool) {
	if obj == nil || path == "" {
		return nil, false
	}
	parts := strings.Split(path, ".")
	cur := interface{}(obj)
	for _, p := range parts {
		if m, ok := cur.(map[string]interface{}); ok {
			if val, exist := m[p]; exist {
				cur = val
			} else {
				return nil, false
			}
		} else {
			return nil, false
		}
	}
	return cur, true
}
