package aliyun_log_query

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取通用阿里云日志查询节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "aliyun_log_query",
		Name:        "阿里云日志查询",
		Description: "通用的阿里云SLS日志查询节点，支持自定义查询语句、项目和日志库",
		Category:    "business",
		NodeType:    "rect",
		Icon:        "search",
		FormFields: []base.FormField{
			{
				Key:          "windowMode",
				Label:        "时间窗口模式",
				Type:         "select",
				Required:     true,
				DefaultValue: "relative",
				Options: []base.FormFieldOption{
					{Label: "相对时长 (如前10分钟/前1小时)", Value: "relative"},
					{Label: "锚点时刻 (如当天0点/当天22点/前一天22点)", Value: "anchored"},
				},
			},
			{
				Key:         "relativeDuration",
				Label:       "相对时长",
				Type:        "input",
				Required:    false,
				Placeholder: "Go时长：10m、1h、2h30m",
			},
			{
				Key:         "anchorHHmm",
				Label:       "锚点时刻(HH:mm)",
				Type:        "input",
				Required:    false,
				Placeholder: "如 00:00、22:00、23:30",
			},
			{
				Key:          "anchorDayShift",
				Label:        "锚点日偏移(整数)",
				Type:         "input",
				Required:     false,
				DefaultValue: 0,
				Placeholder:  "0=今天，-1=昨天，+1=明天",
				Validation: map[string]interface{}{
					"type": "number",
				},
			},
			{
				Key:          "project",
				Label:        "阿里云SLS项目名",
				Type:         "input",
				Required:     true,
				DefaultValue: "sy-security-log",
				Placeholder:  "阿里云SLS项目名称",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 100,
				},
			},
			{
				Key:          "logstore",
				Label:        "日志库名称",
				Type:         "input",
				Required:     true,
				DefaultValue: "event_log",
				Placeholder:  "阿里云SLS日志库名称",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 100,
				},
			},
			{
				Key:         "query",
				Label:       "查询语句",
				Type:        "textarea",
				Required:    true,
				Placeholder: `例如: req.host:"example.com" and req.path: "/api/v1/data"`,
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 2000,
				},
			},
			{
				Key:          "outputField",
				Label:        "输出字段",
				Type:         "input",
				Required:     true,
				DefaultValue: "logQueryResult",
				Placeholder:  "支持JSON路径，如: result.logs, queryData",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 50,
				},
			},
			{
				Key:          "sampleData",
				Label:        "样例日志数据",
				Type:         "textarea",
				Required:     true,
				DefaultValue: `[{"timestamp": "2025-01-04 10:30:00", "level": "INFO", "message": "示例日志", "req": "{\"path\":\"/api/test\",\"method\":\"GET\"}", "resp": "{\"status\":200}"}]`,
				Placeholder:  "请输入JSON格式的样例日志数据，对应查询结果中的data字段内容，用于流程预览和调试",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 10000,
				},
			},
		},
		Style: base.NodeStyle{
			Fill:         "#e6f7ff",
			Stroke:       "#1890ff",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}
}
