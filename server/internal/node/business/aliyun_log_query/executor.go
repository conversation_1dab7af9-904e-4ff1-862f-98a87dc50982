package aliyun_log_query

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/service/third_party/aliyun"
	"sec-flow-server/internal/utils"
	"strconv"
	"time"
)

// AliyunLogQueryNode 通用阿里云日志查询节点
type AliyunLogQueryNode struct {
	config *base.NodeConfig
}

// NewAliyunLogQueryNode 创建通用阿里云日志查询节点
func NewAliyunLogQueryNode() *AliyunLogQueryNode {
	return &AliyunLogQueryNode{
		config: GetNodeConfig(),
	}
}

// GetID 获取节点ID
func (n *AliyunLogQueryNode) GetID() string {
	return n.config.ID
}

// GetConfig 获取节点配置
func (n *AliyunLogQueryNode) GetConfig() *base.NodeConfig {
	return n.config
}

// Validate 验证节点参数
func (n *AliyunLogQueryNode) Validate(params map[string]interface{}) error {
	// 验证时间窗口模式
	windowMode, ok := params["windowMode"].(string)
	if !ok || windowMode == "" {
		return fmt.Errorf("windowMode is required")
	}
	if windowMode != "relative" && windowMode != "anchored" {
		return fmt.Errorf("invalid windowMode: %s", windowMode)
	}

	// 验证输出字段
	if outputField, ok := params["outputField"]; !ok || outputField == "" {
		return fmt.Errorf("outputField is required")
	}

	// 验证项目名
	if project, ok := params["project"]; !ok || project == "" {
		return fmt.Errorf("project is required")
	}

	// 验证日志库名称
	if logstore, ok := params["logstore"]; !ok || logstore == "" {
		return fmt.Errorf("logstore is required")
	}

	// 验证查询语句
	if query, ok := params["query"]; !ok || query == "" {
		return fmt.Errorf("query is required")
	}

	// 验证样例数据格式
	if sampleData, ok := params["sampleData"].(string); ok && sampleData != "" {
		var testData interface{}
		if err := json.Unmarshal([]byte(sampleData), &testData); err != nil {
			return fmt.Errorf("sampleData is not valid JSON: %v", err)
		}
	}

	// 根据时间窗口模式验证相应参数
	if windowMode == "relative" {
		if relativeDuration, ok := params["relativeDuration"].(string); !ok || relativeDuration == "" {
			return fmt.Errorf("relativeDuration is required when windowMode is relative")
		} else {
			// 验证时长格式
			if _, err := time.ParseDuration(relativeDuration); err != nil {
				return fmt.Errorf("invalid relativeDuration format: %v", err)
			}
		}
	} else if windowMode == "anchored" {
		if anchorHHmm, ok := params["anchorHHmm"].(string); !ok || anchorHHmm == "" {
			return fmt.Errorf("anchorHHmm is required when windowMode is anchored")
		}
	}

	return nil
}

// Execute 执行节点逻辑
func (n *AliyunLogQueryNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行阿里云日志查询节点: %s", input.NodeID)

	// 验证参数
	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 获取参数
	windowMode := input.Params["windowMode"].(string)
	outputField := input.Params["outputField"].(string)
	project := input.Params["project"].(string)
	logstore := input.Params["logstore"].(string)
	query := input.Params["query"].(string)

	var startTime, endTime time.Time

	// 获取执行时间，优先使用cron_start节点提供的currentTime
	executeTime := time.Now() // 默认值
	if input.FlowData != nil {
		if executeInfo, exists := input.FlowData["executeInfo"]; exists {
			if executeInfoMap, ok := executeInfo.(map[string]interface{}); ok {
				if cronTrigger, exists := executeInfoMap["cronTrigger"]; exists {
					if cronTriggerMap, ok := cronTrigger.(map[string]interface{}); ok {
						if currentTimeStr, exists := cronTriggerMap["currentTime"]; exists {
							if currentTimeString, ok := currentTimeStr.(string); ok {
								// 加载 Asia/Shanghai 时区
								loc, err := time.LoadLocation("Asia/Shanghai")
								if err != nil {
									loc = time.UTC
									log.Printf("⚠️  无法加载 Asia/Shanghai 时区，使用 UTC: %v", err)
								}
								if parsedTime, err := time.ParseInLocation("2006-01-02 15:04:05", currentTimeString, loc); err == nil {
									executeTime = parsedTime
									log.Printf("✅ 使用 cron_start 提供的执行时间: %s", executeTime.Format("2006-01-02 15:04:05"))
								} else {
									log.Printf("⚠️  解析 currentTime 失败，使用当前时间: %v", err)
								}
							}
						}
					}
				}
			}
		}
	}

	// 计算 start/end
	endTime = executeTime
	if windowMode == "relative" {
		durStr := input.Params["relativeDuration"].(string)
		dur, _ := time.ParseDuration(durStr)
		startTime = endTime.Add(-dur)
	} else {
		hhmm := input.Params["anchorHHmm"].(string)
		dayShift := 0
		if v, ok := input.Params["anchorDayShift"].(float64); ok { // 来自表单可能是数字
			dayShift = int(v)
		} else if vStr, ok := input.Params["anchorDayShift"].(string); ok && vStr != "" {
			// 兜底：字符串转数字
			if parsed, err := time.ParseDuration(vStr + "h"); err == nil {
				dayShift = int(parsed.Hours() / 24)
			}
		}

		// 解析锚点时刻并计算时间范围
		// 组装当天日期+偏移
		loc, _ := time.LoadLocation("Asia/Shanghai")
		endInLoc := endTime.In(loc)
		baseDate := time.Date(endInLoc.Year(), endInLoc.Month(), endInLoc.Day(), 0, 0, 0, 0, loc)
		// 设置时分
		hour := (int(hhmm[0]-'0')*10 + int(hhmm[1]-'0'))
		minute := (int(hhmm[3]-'0')*10 + int(hhmm[4]-'0'))
		startCandidate := baseDate.AddDate(0, 0, dayShift).Add(time.Duration(hour)*time.Hour + time.Duration(minute)*time.Minute)
		if startCandidate.After(endInLoc) {
			startCandidate = startCandidate.Add(-24 * time.Hour)
		}
		startTime = startCandidate
		endTime = endInLoc
	}

	// 调用阿里云SLS查询
	log.Printf("参数 - windowMode: %s, start: %s, end: %s, project: %s, logstore: %s, query: %s, outputField: %s",
		windowMode, startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"),
		project, logstore, query, outputField)

	queryResult, err := n.queryAliyunLogs(startTime, endTime, project, logstore, query)
	if err != nil {
		log.Printf("阿里云日志查询失败: %v", err)
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("阿里云日志查询失败: %v", err),
		}, nil
	}
	recordCount := len(queryResult)

	log.Printf("查询完成 - 时间范围: %s 到 %s, 找到 %d 条记录",
		startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"), recordCount)

	// 构造输出数据
	outputData := map[string]interface{}{
		"startTime":   startTime.Format("2006-01-02 15:04:05"),
		"endTime":     endTime.Format("2006-01-02 15:04:05"),
		"recordCount": recordCount,
		"data":        queryResult,
		"project":     project,
		"logstore":    logstore,
		"query":       query,
	}

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 将查询结果添加到流程数据中 (支持JSON路径表达式)
	if !utils.SetFieldValue(updatedFlowData, outputField, outputData) {
		// 如果设置失败，回退到简单字段设置
		updatedFlowData[outputField] = outputData
	}

	if recordCount == 0 {
		return &base.ExecutionOutput{
			Success:  true,
			Continue: false, // 继续执行后续节点
			Data:     outputData,
			FlowData: updatedFlowData,
			Message: fmt.Sprintf("成功查询阿里云日志，在时间 %s 到 %s 内找到 %d 条记录",
				startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"), recordCount),
			Metadata: map[string]interface{}{
				"nodeType":    "aliyun_log_query",
				"outputField": outputField,
				"recordCount": recordCount,
				"startTime":   startTime.Format("2006-01-02 15:04:05"),
				"endTime":     endTime.Format("2006-01-02 15:04:05"),
				"project":     project,
				"logstore":    logstore,
				"processedAt": time.Now().Format("2006-01-02 15:04:05"),
			},
		}, nil
	}
	return &base.ExecutionOutput{
		Success:  true,
		Continue: true, // 继续执行后续节点
		Data:     outputData,
		FlowData: updatedFlowData,
		Message: fmt.Sprintf("成功查询阿里云日志，在时间 %s 到 %s 内找到 %d 条记录",
			startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"), recordCount),
		Metadata: map[string]interface{}{
			"nodeType":    "aliyun_log_query",
			"outputField": outputField,
			"recordCount": recordCount,
			"startTime":   startTime.Format("2006-01-02 15:04:05"),
			"endTime":     endTime.Format("2006-01-02 15:04:05"),
			"project":     project,
			"logstore":    logstore,
			"processedAt": time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// queryAliyunLogs 查询阿里云日志
func (n *AliyunLogQueryNode) queryAliyunLogs(startTime, endTime time.Time, project, logstore, query string) ([]interface{}, error) {
	// 获取阿里云SLS客户端
	client := getAliyunSLSClient()
	if client == nil {
		return nil, fmt.Errorf("阿里云SLS客户端未初始化")
	}

	var allLogs []interface{}

	// 分页查询日志
	for i := 0; i < 100; i++ { // 最多查询100页，每页50条
		args := map[string]string{
			"from":   strconv.FormatInt(startTime.Unix(), 10),
			"to":     strconv.FormatInt(endTime.Unix(), 10),
			"query":  query,
			"type":   "log",
			"line":   "50",
			"offset": strconv.Itoa(i * 50),
		}

		getLogsReq := aliyun.GetLogsRequest{
			Project:  project,
			Logstore: logstore,
			Args:     args,
		}

		res, err := client.GetLogs(&getLogsReq)
		if err != nil {
			return nil, err
		}

		// 检查阿里云返回的错误
		if res.Error != "" {
			return nil, fmt.Errorf("阿里云SLS错误: %s", res.Error)
		}

		logs, ok := res.Data.([]interface{})
		if !ok || len(logs) == 0 {
			break // 没有更多数据了
		}

		allLogs = append(allLogs, logs...)

		// 如果返回的数据少于50条，说明已经是最后一页
		if len(logs) < 50 {
			break
		}
	}

	return allLogs, nil
}

// Example 生成示例数据
func (n *AliyunLogQueryNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置参数
	outputField := "logQueryResult"
	sampleData := `[{"timestamp": "2025-01-04 10:30:00", "level": "INFO", "message": "示例日志", "req": "{\"path\":\"/api/test\",\"method\":\"GET\"}", "resp": "{\"status\":200}"}]`
	project := "sy-security-log"
	logstore := "event_log"
	query := `req.host:"example.com" and req.path: "/api/v1/data"`

	if input.Config != nil {
		if of, ok := input.Config["outputField"].(string); ok && of != "" {
			outputField = of
		}
		if sd, ok := input.Config["sampleData"].(string); ok && sd != "" {
			sampleData = sd
		}
		if p, ok := input.Config["project"].(string); ok && p != "" {
			project = p
		}
		if ls, ok := input.Config["logstore"].(string); ok && ls != "" {
			logstore = ls
		}
		if q, ok := input.Config["query"].(string); ok && q != "" {
			query = q
		}
	}

	// 解析样例数据
	var sampleLogs interface{}
	if err := json.Unmarshal([]byte(sampleData), &sampleLogs); err != nil {
		// 如果解析失败，使用默认格式
		sampleLogs = []map[string]interface{}{
			{
				"error": "样例数据解析失败",
				"raw":   sampleData,
			},
		}
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 获取执行时间（用于示例）
	executeTime := time.Now()
	if input.FlowData != nil {
		if executeInfo, exists := input.FlowData["executeInfo"]; exists {
			if executeInfoMap, ok := executeInfo.(map[string]interface{}); ok {
				if cronTrigger, exists := executeInfoMap["cronTrigger"]; exists {
					if cronTriggerMap, ok := cronTrigger.(map[string]interface{}); ok {
						if currentTimeStr, exists := cronTriggerMap["currentTime"]; exists {
							if currentTimeString, ok := currentTimeStr.(string); ok {
								// 加载 Asia/Shanghai 时区
								loc, err := time.LoadLocation("Asia/Shanghai")
								if err != nil {
									loc = time.UTC
								}
								if parsedTime, err := time.ParseInLocation("2006-01-02 15:04:05", currentTimeString, loc); err == nil {
									executeTime = parsedTime
								}
							}
						}
					}
				}
			}
		}
	}

	// 计算记录数量
	recordCount := getRecordCount(sampleLogs)

	// 构造示例输出数据（与实际执行时的格式完全一致）
	// sampleLogs 对应实际执行时的 queryResult，即 data 字段
	outputData := map[string]interface{}{
		"startTime":   executeTime.Add(-1 * time.Hour).Format("2006-01-02 15:04:05"),
		"endTime":     executeTime.Format("2006-01-02 15:04:05"),
		"recordCount": recordCount,
		"data":        sampleLogs, // 用户配置的样例数据对应实际的查询结果
		"project":     project,
		"logstore":    logstore,
		"query":       query,
	}

	// 将查询结果添加到流程数据中 (支持JSON路径表达式)
	if !utils.SetFieldValue(resultFlowData, outputField, outputData) {
		// 如果设置失败，回退到简单字段设置
		resultFlowData[outputField] = outputData
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			NewValue:    outputData,
			Description: fmt.Sprintf("查询阿里云日志 (项目: %s, 日志库: %s)", project, logstore),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("查询阿里云日志，将结果存储到 %s 字段", outputField),
		Changes:     changes,
	}
}

// getRecordCount 获取记录数量
func getRecordCount(data interface{}) int {
	if logs, ok := data.([]interface{}); ok {
		return len(logs)
	}
	return 0
}

// getAliyunSLSClient 获取阿里云SLS客户端
func getAliyunSLSClient() *aliyun.AliyunSLSClient {
	return aliyun.GetAliyunSLSClient()
}
