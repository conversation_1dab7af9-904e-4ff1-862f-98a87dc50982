package prime_decode

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取连锁解码手机号日志节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "prime_decode",
		Name:        "连锁解码手机号日志",
		Description: "扫描Prime CRM用户解码手机号的日志，检测可疑的手机号解码行为",
		Category:    "business",
		NodeType:    "rect",
		Icon:        "search",
		FormFields: []base.FormField{
			{
				Key:          "windowMode",
				Label:        "时间窗口模式",
				Type:         "select",
				Required:     true,
				DefaultValue: "relative",
				Options: []base.FormFieldOption{
					{Label: "相对时长 (如前10分钟/前1小时)", Value: "relative"},
					{Label: "锚点时刻 (如当天0点/当天22点/前一天22点)", Value: "anchored"},
				},
			},
			{
				Key:         "relativeDuration",
				Label:       "相对时长",
				Type:        "input",
				Required:    false,
				Placeholder: "Go时长：10m、1h、2h30m",
			},
			{
				Key:         "anchorHHmm",
				Label:       "锚点时刻(HH:mm)",
				Type:        "input",
				Required:    false,
				Placeholder: "如 00:00、22:00、23:30",
			},
			{
				Key:          "anchorDayShift",
				Label:        "锚点日偏移(整数)",
				Type:         "input",
				Required:     false,
				DefaultValue: 0,
				Placeholder:  "0=今天，-1=昨天，+1=明天",
				Validation: map[string]interface{}{
					"type": "number",
				},
			},
			{
				Key:          "outputField",
				Label:        "输出字段",
				Type:         "input",
				Required:     true,
				DefaultValue: "primeDecodeResult",
				Placeholder:  "支持JSON路径，如: result.scanData, primeData",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 50,
				},
			},
		},
		Style: base.NodeStyle{
			Fill:         "#fff2e8",
			Stroke:       "#fa8c16",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}
}
