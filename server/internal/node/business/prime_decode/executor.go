package prime_decode

import (
	"context"
	"fmt"
	"log"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/service/third_party/aliyun"
	"sec-flow-server/internal/utils"
	"time"
)

// PrimeDecodeNode 连锁解码手机号日志节点
type PrimeDecodeNode struct {
	config *base.NodeConfig
}

// NewPrimeDecodeNode 创建连锁解码手机号日志节点
func NewPrimeDecodeNode() *PrimeDecodeNode {
	return &PrimeDecodeNode{
		config: GetNodeConfig(),
	}
}

// GetID 获取节点ID
func (n *PrimeDecodeNode) GetID() string {
	return n.config.ID
}

// GetConfig 获取节点配置
func (n *PrimeDecodeNode) GetConfig() *base.NodeConfig {
	return n.config
}

// Validate 验证节点参数
func (n *PrimeDecodeNode) Validate(params map[string]interface{}) error {
	var windowMode string
	var ok bool
	// 新配置
	if windowMode, ok = params["windowMode"].(string); !ok || windowMode == "" {
		return fmt.Errorf("windowMode is required")
	}
	if windowMode != "relative" && windowMode != "anchored" {
		return fmt.Errorf("invalid windowMode: %s", windowMode)
	}
	if outputField, ok := params["outputField"]; !ok || outputField == "" {
		return fmt.Errorf("outputField is required")
	}
	if windowMode == "relative" {
		if v, ok := params["relativeDuration"].(string); !ok || v == "" {
			return fmt.Errorf("relativeDuration is required when windowMode=relative")
		}
		if _, err := time.ParseDuration(params["relativeDuration"].(string)); err != nil {
			return fmt.Errorf("invalid relativeDuration: %v", err)
		}
	}
	if windowMode == "anchored" {
		v, ok := params["anchorHHmm"].(string)
		if !ok || v == "" {
			return fmt.Errorf("anchorHHmm is required when windowMode=anchored")
		}
		// 简单格式校验 HH:mm
		if len(v) != 5 || v[2] != ':' {
			return fmt.Errorf("anchorHHmm must be in HH:mm format")
		}
		if _, ok := params["anchorDayShift"]; !ok {
			return fmt.Errorf("anchorDayShift is required when windowMode=anchored")
		}
	}

	return nil
}

// Execute 执行节点逻辑
func (n *PrimeDecodeNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行连锁解码手机号日志节点: %s", input.NodeID)

	// 验证参数
	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 获取参数
	windowMode := input.Params["windowMode"].(string)
	outputField := input.Params["outputField"].(string)
	var startTime, endTime time.Time

	// 获取执行时间，优先使用cron_start节点提供的currentTime
	executeTime := time.Now() // 默认值
	if input.FlowData != nil {
		if executeInfo, exists := input.FlowData["executeInfo"]; exists {
			if executeInfoMap, ok := executeInfo.(map[string]interface{}); ok {
				if cronTrigger, exists := executeInfoMap["cronTrigger"]; exists {
					if cronTriggerMap, ok := cronTrigger.(map[string]interface{}); ok {
						if currentTimeStr, exists := cronTriggerMap["currentTime"]; exists {
							if currentTimeString, ok := currentTimeStr.(string); ok {
								// 加载 Asia/Shanghai 时区
								loc, err := time.LoadLocation("Asia/Shanghai")
								if err != nil {
									loc = time.UTC
									log.Printf("⚠️  无法加载 Asia/Shanghai 时区，使用 UTC: %v", err)
								}

								// 使用 ParseInLocation 正确解析北京时间
								if parsedTime, err := time.ParseInLocation("2006-01-02 15:04:05", currentTimeString, loc); err == nil {
									executeTime = parsedTime
									log.Printf("使用cron_start节点提供的执行时间: %s (Asia/Shanghai)", currentTimeString)
								} else {
									log.Printf("解析cron currentTime失败，使用当前时间: %v", err)
								}
							}
						}
					}
				}
			}
		}
	}

	log.Printf("最终使用的执行时间: %s", executeTime.Format("2006-01-02 15:04:05"))

	// 计算 start/end
	endTime = executeTime
	if windowMode == "relative" {
		durStr := input.Params["relativeDuration"].(string)
		dur, _ := time.ParseDuration(durStr)
		startTime = endTime.Add(-dur)
	} else {
		hhmm := input.Params["anchorHHmm"].(string)
		dayShift := 0
		if v, ok := input.Params["anchorDayShift"].(float64); ok { // 来自表单可能是数字
			dayShift = int(v)
		} else if vStr, ok := input.Params["anchorDayShift"].(string); ok && vStr != "" {
			// 兜底：字符串转数字
			if parsed, err := time.ParseDuration(vStr + "h"); err == nil {
				dayShift = int(parsed.Hours() / 24)
			}
		}
		// 组装当天日期+偏移
		loc, _ := time.LoadLocation("Asia/Shanghai")
		endInLoc := endTime.In(loc)
		baseDate := time.Date(endInLoc.Year(), endInLoc.Month(), endInLoc.Day(), 0, 0, 0, 0, loc)
		// 设置时分
		hour := (int(hhmm[0]-'0')*10 + int(hhmm[1]-'0'))
		minute := (int(hhmm[3]-'0')*10 + int(hhmm[4]-'0'))
		startCandidate := baseDate.AddDate(0, 0, dayShift).Add(time.Duration(hour)*time.Hour + time.Duration(minute)*time.Minute)
		if startCandidate.After(endInLoc) {
			startCandidate = startCandidate.Add(-24 * time.Hour)
		}
		startTime = startCandidate
		endTime = endInLoc
	}

	// 调用 PrimeUserDecodeScan
	log.Printf("参数 - windowMode: %s, start: %s, end: %s, window: %s, outputField: %s", windowMode, startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"), outputField)
	scanResult, err := aliyun.PrimeUserDecodeScan(startTime, endTime)
	if err != nil {
		log.Printf("PrimeUserDecodeScan 执行失败: %v", err)
		// 根据要求，如果方法执行失败，流程没必要继续执行
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("PrimeUserDecodeScan failed: %v", err),
		}, nil
	}

	// 新结构：按 global_user_id 分组，recordCount 取所有分组的记录数之和
	recordCount := 0
	for _, group := range scanResult.Datas {
		if v, ok := group["customerCount"]; ok {
			switch c := v.(type) {
			case int:
				recordCount += c
			case int64:
				recordCount += int(c)
			case float64:
				recordCount += int(c)
			}
			continue
		}
		if recordsAny, ok := group["records"]; ok {
			if arr, ok2 := recordsAny.([]string); ok2 {
				recordCount += len(arr)
			} else if arrAny, ok3 := recordsAny.([]interface{}); ok3 {
				recordCount += len(arrAny)
			}
		}
	}

	log.Printf("扫描完成 - 时间范围: %s 到 %s, 找到 %d 条记录", scanResult.StartTime, scanResult.EndTime, recordCount)

	// 构造输出数据
	outputData := map[string]interface{}{
		"startTime":   scanResult.StartTime,
		"endTime":     scanResult.EndTime,
		"recordCount": recordCount,
		"datas":       scanResult.Datas,
	}

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 将扫描结果添加到流程数据中 (支持JSON路径表达式)
	if !utils.SetFieldValue(updatedFlowData, outputField, outputData) {
		// 如果设置失败，回退到简单字段设置
		updatedFlowData[outputField] = outputData
	}

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true, // 继续执行后续节点
		Data:     outputData,
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("成功扫描Prime解码日志，在时间 %s 到 %s内找到 %d 条记录", scanResult.StartTime, scanResult.EndTime, len(scanResult.Datas)),
		Metadata: map[string]interface{}{
			"nodeType":    "prime_decode",
			"outputField": outputField,
			"recordCount": len(scanResult.Datas),
			"startTime":   scanResult.StartTime,
			"endTime":     scanResult.EndTime,
			"processedAt": time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// Example 生成示例数据
func (n *PrimeDecodeNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置参数
	outputField := "primeDecodeResult"

	if input.Config != nil {
		if of, ok := input.Config["outputField"].(string); ok && of != "" {
			outputField = of
		}
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 获取执行时间，优先使用cron_start节点提供的currentTime
	executeTime := time.Now() // 默认值
	if input.FlowData != nil {
		if executeInfo, exists := input.FlowData["executeInfo"]; exists {
			if executeInfoMap, ok := executeInfo.(map[string]interface{}); ok {
				if cronTrigger, exists := executeInfoMap["cronTrigger"]; exists {
					if cronTriggerMap, ok := cronTrigger.(map[string]interface{}); ok {
						if currentTimeStr, exists := cronTriggerMap["currentTime"]; exists {
							if currentTimeString, ok := currentTimeStr.(string); ok {
								// 加载 Asia/Shanghai 时区
								loc, err := time.LoadLocation("Asia/Shanghai")
								if err != nil {
									loc = time.UTC
								}

								// 使用 ParseInLocation 正确解析北京时间
								if parsedTime, err := time.ParseInLocation("2006-01-02 15:04:05", currentTimeString, loc); err == nil {
									executeTime = parsedTime
								}
							}
						}
					}
				}
			}
		}
	}

	// 生成示例扫描结果（新结构：datas 为多个用户分组，每项包含 customerCount 与 records 文本列表，示例包含加密/哈希字段）
	exampleScanResult := map[string]interface{}{
		"startTime":   executeTime.Add(-1 * time.Hour).Format("2006-01-02 15:04:05"),
		"endTime":     executeTime.Format("2006-01-02 15:04:05"),
		"recordCount": 5,
		"datas": []map[string]interface{}{
			{
				"globalUserId":  "12345",
				"customerCount": 3,
				"ssoUsername":   "zhangsan",
				"userId":        "feishu_abc123",
				"records": []string{
					"手机号：138****1234\n身份证号：11010*****123\n客户归属门店：北京朝阳店。",
					"手机号：139****5678\n身份证号：44030*****567\n客户归属门店：上海黄浦店。",
					"手机号：137****9999\n身份证号：32010*****999\n客户归属门店：深圳南山店。",
				},
				// 与 records 对齐的加密/哈希数组（示例值）
				"mobileAESList": []string{"<enc:138****1234>", "<enc:139****5678>", "<enc:137****9999>"},
				"idCardAESList": []string{"<enc:11010*****123>", "<enc:44030*****567>", "<enc:32010*****999>"},
				"mobileMD5List": []string{"d41d8cd98f00b204e9800998ecf8427e", "d41d8cd98f00b204e9800998ecf8427e", "d41d8cd98f00b204e9800998ecf8427e"},
				"content":       "手机号：138****1234\n身份证号：11010*****123\n客户归属门店：北京朝阳店。\n\n手机号：139****5678\n身份证号：44030*****567\n客户归属门店：上海黄浦店。\n\n手机号：137****9999\n身份证号：32010*****999\n客户归属门店：深圳南山店。",
			},
			{
				"globalUserId":  "67890",
				"customerCount": 2,
				"ssoUsername":   "lisi",
				"userId":        "feishu_def456",
				"records": []string{
					"手机号：136****0000\n身份证号：12010*****000\n客户归属门店：广州天河店。",
					"手机号：135****1111\n身份证号：21010*****111\n客户归属门店：杭州西湖店。",
				},
				"mobileAESList": []string{"<enc:136****0000>", "<enc:135****1111>"},
				"idCardAESList": []string{"<enc:12010*****000>", "<enc:21010*****111>"},
				"mobileMD5List": []string{"d41d8cd98f00b204e9800998ecf8427e", "d41d8cd98f00b204e9800998ecf8427e"},
				"content":       "手机号：136****0000\n身份证号：12010*****000\n客户归属门店：广州天河店。\n\n手机号：135****1111\n身份证号：21010*****111\n客户归属门店：杭州西湖店。",
			},
		},
	}

	// 添加到流程数据中 (支持JSON路径表达式)
	if !utils.SetFieldValue(resultFlowData, outputField, exampleScanResult) {
		// 如果设置失败，回退到简单字段设置
		resultFlowData[outputField] = exampleScanResult
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			NewValue:    exampleScanResult,
			Description: "扫描Prime CRM用户解码手机号日志，检测可疑的解码行为",
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("扫描Prime CRM用户在指定时间窗口内的手机号解码日志，将结果存储到 %s 字段", outputField),
		Changes:     changes,
	}
}
