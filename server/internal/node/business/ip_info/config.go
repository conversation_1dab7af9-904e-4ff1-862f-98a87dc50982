package ip_info

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取IP信息补充节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "ip-info-supplement",
		Name:        "IP信息补充",
		Description: "根据指定字段补充IP信息",
		Category:    "business",
		NodeType:    "rect",
		Icon:        "ip",
		FormFields: []base.FormField{
			{
				Key:          "sourceField",
				Label:        "IP来源字段",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "支持JSON路径，如: ip.id, data.email",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 50,
				},
			},
			{
				Key:          "outputField",
				Label:        "输出字段",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "支持JSON路径，如: ip.info, result.profile",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 50,
				},
			},
		},
		Style: base.NodeStyle{
			Fill:         "#e6f7ff",
			Stroke:       "#1890ff",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}
}
