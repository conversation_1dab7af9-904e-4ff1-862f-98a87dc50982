package ip_info

import (
	"context"
	"fmt"
	"log"
	"reflect"
	"regexp"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/service/third_party/yongan"
	"sec-flow-server/internal/utils"
	"strconv"
	"strings"
	"time"
)

// IPInfoNode IP信息补充节点
type IPInfoNode struct {
	config *base.NodeConfig
}

// NewIPInfoNode 创建IP信息补充节点
func NewIPInfoNode() *IPInfoNode {
	return &IPInfoNode{
		config: GetNodeConfig(),
	}
}

// GetID 获取节点ID
func (n *IPInfoNode) GetID() string {
	return n.config.ID
}

// GetConfig 获取节点配置
func (n *IPInfoNode) GetConfig() *base.NodeConfig {
	return n.config
}

// Validate 验证节点参数
func (n *IPInfoNode) Validate(params map[string]interface{}) error {
	// 验证必填字段
	sourceField, ok := params["sourceField"].(string)
	if !ok || sourceField == "" {
		return fmt.Errorf("sourceField is required")
	}

	outputField, ok := params["outputField"].(string)
	if !ok || outputField == "" {
		return fmt.Errorf("outputField is required")
	}

	return nil
}

// Execute 执行节点逻辑
func (n *IPInfoNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行IP信息补充节点: %s", input.NodeID)

	// 验证参数
	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 获取参数
	sourceField := input.Params["sourceField"].(string)
	outputField := input.Params["outputField"].(string)

	log.Printf("参数 - sourceField: %s, outputField: %s",
		sourceField, outputField)

	// 检查是否是数组遍历操作 (如: data[].ipId)
	if strings.Contains(sourceField, "[]") {
		return n.executeArrayIPInfo(ctx, input, sourceField, outputField)
	}

	// 检查是否是数组索引操作 (如: data[0].ipId)
	if strings.Contains(sourceField, "[") && strings.Contains(sourceField, "]") && !strings.Contains(sourceField, "[]") {
		return n.executeIndexIPInfo(ctx, input, sourceField, outputField)
	}

	// 从输入数据或上一个节点的输出中获取源字段值 (支持JSON路径表达式)
	sourceValue, found := utils.GetFieldValueFromSources(input.PrevOutput, input.FlowData, sourceField)
	if !found {
		return &base.ExecutionOutput{
			Success: false,
			Error:   fmt.Sprintf("source field '%s' not found in input data", sourceField),
		}, nil
	}

	// 模拟IP信息补充逻辑
	ipInfo, err := n.supplementIPInfo(sourceValue)
	if err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 构造输出数据
	outputData := make(map[string]interface{})
	outputData[outputField] = ipInfo

	log.Printf("IP信息补充完成，输出: %v", outputData)

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 将IP信息添加到流程数据中 (支持JSON路径表达式)
	if !utils.SetFieldValue(updatedFlowData, outputField, ipInfo) {
		// 如果设置失败，回退到简单字段设置
		updatedFlowData[outputField] = ipInfo
	}

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true, // 继续执行后续节点
		Data:     outputData,
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("成功补充IP信息到字段 '%s'", outputField),
		Metadata: map[string]interface{}{
			"nodeType":    "ip_info_supplement",
			"sourceField": sourceField,
			"outputField": outputField,
			"processedAt": time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// executeArrayIPInfo 执行数组遍历IP信息补充
func (n *IPInfoNode) executeArrayIPInfo(ctx context.Context, input *base.ExecutionInput, sourceField, outputField string) (*base.ExecutionOutput, error) {
	log.Printf("🔄 执行数组IP信息补充: %s -> %s", sourceField, outputField)

	// 解析输入字段路径：data[].ipId → arrayPath="data", fieldName="ipId"
	parts := strings.Split(sourceField, "[]")
	if len(parts) != 2 {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("invalid array field path: %s", sourceField),
			FlowData: input.FlowData,
		}, nil
	}

	arrayPath := strings.TrimSuffix(parts[0], ".")
	fieldName := strings.TrimPrefix(parts[1], ".")

	// 获取数组对象
	arrayObj, found := utils.GetFieldValueFromSources(input.PrevOutput, input.FlowData, arrayPath)
	if !found {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("array field '%s' not found in input data", arrayPath),
			FlowData: input.FlowData,
		}, nil
	}

	// 确保是数组类型
	arrayValue := reflect.ValueOf(arrayObj)
	if arrayValue.Kind() != reflect.Slice && arrayValue.Kind() != reflect.Array {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("field '%s' is not an array", arrayPath),
			FlowData: input.FlowData,
		}, nil
	}

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	processedCount := 0
	// 遍历数组，为每个元素补充IP信息
	for i := 0; i < arrayValue.Len(); i++ {
		element := arrayValue.Index(i).Interface()

		// 确保元素是map类型
		if elementMap, ok := element.(map[string]interface{}); ok {
			// 从元素中获取输入字段的值
			var sourceValue interface{}
			if fieldName == "" {
				// 如果没有字段名，使用整个元素
				sourceValue = element
			} else {
				// 获取指定字段的值
				if val, exists := elementMap[fieldName]; exists {
					sourceValue = val
				} else {
					log.Printf("⚠️ 第%d个元素中没有找到字段 '%s'", i, fieldName)
					continue
				}
			}

			// 补充IP信息
			ipInfo, err := n.supplementIPInfo(sourceValue)
			if err != nil {
				log.Printf("❌ 补充第%d个元素的IP信息失败: %v", i, err)
				continue
			}

			// 解析输出字段路径并设置值
			outputParts := strings.Split(outputField, "[]")
			if len(outputParts) == 2 {
				outputFieldName := strings.TrimPrefix(outputParts[1], ".")
				if outputFieldName != "" {
					elementMap[outputFieldName] = ipInfo
					processedCount++
				}
			}
		}
	}

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true,
		Data:     map[string]interface{}{"processedCount": processedCount},
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("成功为 %d 个数组元素补充IP信息", processedCount),
		Metadata: map[string]interface{}{
			"nodeType":       "ip_info_supplement",
			"operationType":  "array",
			"sourceField":    sourceField,
			"outputField":    outputField,
			"processedCount": processedCount,
			"processedAt":    time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// executeIndexIPInfo 执行数组索引IP信息补充
func (n *IPInfoNode) executeIndexIPInfo(ctx context.Context, input *base.ExecutionInput, sourceField, outputField string) (*base.ExecutionOutput, error) {
	log.Printf("🔄 执行数组索引IP信息补充: %s -> %s", sourceField, outputField)

	// 解析数组索引路径：data[0].ipId
	re := regexp.MustCompile(`^(.+)\[(\d+)\]\.?(.*)$`)
	matches := re.FindStringSubmatch(sourceField)
	if len(matches) != 4 {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("invalid array index field path: %s", sourceField),
			FlowData: input.FlowData,
		}, nil
	}

	arrayPath := matches[1]
	indexStr := matches[2]
	fieldName := matches[3]

	// 解析索引
	index, err := strconv.Atoi(indexStr)
	if err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("invalid array index: %s", indexStr),
			FlowData: input.FlowData,
		}, nil
	}

	// 获取数组对象
	arrayObj, found := utils.GetFieldValueFromSources(input.PrevOutput, input.FlowData, arrayPath)
	if !found {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("array field '%s' not found in input data", arrayPath),
			FlowData: input.FlowData,
		}, nil
	}

	// 确保是数组类型
	arrayValue := reflect.ValueOf(arrayObj)
	if arrayValue.Kind() != reflect.Slice && arrayValue.Kind() != reflect.Array {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("field '%s' is not an array", arrayPath),
			FlowData: input.FlowData,
		}, nil
	}

	// 检查索引范围
	if index < 0 || index >= arrayValue.Len() {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("array index %d out of range (length: %d)", index, arrayValue.Len()),
			FlowData: input.FlowData,
		}, nil
	}

	// 获取指定索引的元素
	element := arrayValue.Index(index).Interface()

	// 确保元素是map类型
	elementMap, ok := element.(map[string]interface{})
	if !ok {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("array element at index %d is not a map", index),
			FlowData: input.FlowData,
		}, nil
	}

	// 从元素中获取输入字段的值
	var sourceValue interface{}
	if fieldName == "" {
		// 如果没有字段名，使用整个元素
		sourceValue = element
	} else {
		// 获取指定字段的值
		if val, exists := elementMap[fieldName]; exists {
			sourceValue = val
		} else {
			return &base.ExecutionOutput{
				Success:  false,
				Continue: false,
				Error:    fmt.Sprintf("field '%s' not found in array element at index %d", fieldName, index),
				FlowData: input.FlowData,
			}, nil
		}
	}

	// 补充IP信息
	ipInfo, err := n.supplementIPInfo(sourceValue)
	if err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("failed to supplement ip info: %v", err),
			FlowData: input.FlowData,
		}, nil
	}

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 解析输出字段路径并设置值
	outputRe := regexp.MustCompile(`^(.+)\[(\d+)\]\.?(.*)$`)
	outputMatches := outputRe.FindStringSubmatch(outputField)
	if len(outputMatches) == 4 {
		outputArrayPath := outputMatches[1]
		outputIndexStr := outputMatches[2]
		outputFieldName := outputMatches[3]

		outputIndex, err := strconv.Atoi(outputIndexStr)
		if err == nil && outputIndex == index && outputArrayPath == arrayPath {
			// 直接设置到指定索引的元素中
			if outputFieldName != "" {
				elementMap[outputFieldName] = ipInfo
			}
		}
	} else {
		// 如果输出字段不是数组索引格式，使用通用设置方法
		if !utils.SetFieldValue(updatedFlowData, outputField, ipInfo) {
			updatedFlowData[outputField] = ipInfo
		}
	}

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true,
		Data:     map[string]interface{}{"ipInfo": ipInfo},
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("成功为数组索引 %d 补充IP信息", index),
		Metadata: map[string]interface{}{
			"nodeType":      "ip_info_supplement",
			"operationType": "index",
			"sourceField":   sourceField,
			"outputField":   outputField,
			"arrayIndex":    index,
			"processedAt":   time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// supplementIPInfo 模拟IP信息补充逻辑
func (n *IPInfoNode) supplementIPInfo(sourceValue interface{}) (map[string]interface{}, error) {
	client := yongan.GetYonganClient()
	ipDesc, err := client.GetIPDesc(sourceValue)
	return ipDesc, err
}

// Example 生成示例数据
func (n *IPInfoNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置参数
	sourceField := "ip"
	outputField := "ipInfo"

	if input.Config != nil {
		if sf, ok := input.Config["sourceField"].(string); ok && sf != "" {
			sourceField = sf
		}
		if of, ok := input.Config["outputField"].(string); ok && of != "" {
			outputField = of
		}
	}

	// 检查是否是数组操作
	if strings.Contains(sourceField, "[]") {
		return n.generateArrayExample(input, sourceField, outputField)
	}

	if strings.Contains(sourceField, "[") && strings.Contains(sourceField, "]") && !strings.Contains(sourceField, "[]") {
		return n.generateIndexExample(input, sourceField, outputField)
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 生成示例IP信息
	// 生成示例IP信息
	exampleIPInfo := map[string]interface{}{
		"country": "内网",
		"detail":  "内网",
	}

	// 添加到流程数据中 (支持JSON路径表达式)
	if !utils.SetFieldValue(resultFlowData, outputField, exampleIPInfo) {
		// 如果设置失败，回退到简单字段设置
		resultFlowData[outputField] = exampleIPInfo
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			NewValue:    exampleIPInfo,
			Description: fmt.Sprintf("根据 %s 补充IP信息", sourceField),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("从 %s 字段获取IPID，补充详细的IP信息到 %s 字段", sourceField, outputField),
		Changes:     changes,
	}
}

// generateArrayExample 生成数组操作示例
func (n *IPInfoNode) generateArrayExample(input *base.ExampleInput, sourceField, outputField string) *base.ExampleOutput {
	// 解析数组路径
	parts := strings.Split(sourceField, "[]")
	if len(parts) != 2 {
		return n.generateNormalExample(input, sourceField, outputField)
	}

	arrayPath := strings.TrimSuffix(parts[0], ".")
	fieldName := strings.TrimPrefix(parts[1], ".")

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 创建示例数组数据（如果不存在）
	if _, exists := resultFlowData[arrayPath]; !exists {
		exampleArray := []interface{}{
			map[string]interface{}{
				fieldName: "abcdefgh",
				"name":    "IP1",
			},
			map[string]interface{}{
				fieldName: "ijklmnop",
				"name":    "IP2",
			},
		}
		resultFlowData[arrayPath] = exampleArray
	}

	// 为数组中的每个元素添加IP信息
	if arrayData, ok := resultFlowData[arrayPath].([]interface{}); ok {
		for _, item := range arrayData {
			if itemMap, ok := item.(map[string]interface{}); ok {
				// 解析输出字段路径
				outputParts := strings.Split(outputField, "[]")
				if len(outputParts) == 2 {
					outputFieldName := strings.TrimPrefix(outputParts[1], ".")
					if outputFieldName != "" {
						// 生成示例IP信息
						ipInfo := map[string]interface{}{
							"country": "内网",
							"detail":  "内网",
						}
						itemMap[outputFieldName] = ipInfo
					}
				}
			}
		}
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			NewValue:    "IP信息对象",
			Description: fmt.Sprintf("为数组 %s 中的每个元素根据 %s 补充IP信息", arrayPath, fieldName),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("为数组中的每个元素根据 %s 补充IP信息", sourceField),
		Changes:     changes,
	}
}

// generateIndexExample 生成数组索引操作示例
func (n *IPInfoNode) generateIndexExample(input *base.ExampleInput, sourceField, outputField string) *base.ExampleOutput {
	// 解析数组索引路径
	re := regexp.MustCompile(`^(.+)\[(\d+)\]\.?(.*)$`)
	matches := re.FindStringSubmatch(sourceField)
	if len(matches) != 4 {
		return n.generateNormalExample(input, sourceField, outputField)
	}

	arrayPath := matches[1]
	indexStr := matches[2]
	fieldName := matches[3]

	index, err := strconv.Atoi(indexStr)
	if err != nil {
		return n.generateNormalExample(input, sourceField, outputField)
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 创建示例数组数据（如果不存在）
	if _, exists := resultFlowData[arrayPath]; !exists {
		exampleArray := []interface{}{
			map[string]interface{}{
				fieldName: "abcdefgh",
				"name":    "IP1",
			},
			map[string]interface{}{
				fieldName: "ijklmnop",
				"name":    "IP2",
			},
		}
		resultFlowData[arrayPath] = exampleArray
	}

	// 为指定索引的元素添加IP信息
	if arrayData, ok := resultFlowData[arrayPath].([]interface{}); ok && index < len(arrayData) {
		if itemMap, ok := arrayData[index].(map[string]interface{}); ok {
			// 解析输出字段路径
			outputRe := regexp.MustCompile(`^(.+)\[(\d+)\]\.?(.*)$`)
			outputMatches := outputRe.FindStringSubmatch(outputField)
			if len(outputMatches) == 4 {
				outputFieldName := outputMatches[3]
				if outputFieldName != "" {
					// 生成示例IP信息
					ipInfo := map[string]interface{}{
						"country": "内网",
						"detail":  "内网",
					}
					itemMap[outputFieldName] = ipInfo
				}
			}
		}
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			NewValue:    "IP信息对象",
			Description: fmt.Sprintf("为数组索引 %d 的元素根据 %s 补充IP信息", index, fieldName),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("为数组索引 %d 的元素根据 %s 补充IP信息", index, sourceField),
		Changes:     changes,
	}
}

// generateNormalExample 生成普通操作示例
func (n *IPInfoNode) generateNormalExample(input *base.ExampleInput, sourceField, outputField string) *base.ExampleOutput {
	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 生成示例IP信息
	exampleIPInfo := map[string]interface{}{
		"country": "内网",
		"detail":  "内网",
	}

	// 将IP信息添加到流程数据中
	if !utils.SetFieldValue(resultFlowData, outputField, exampleIPInfo) {
		resultFlowData[outputField] = exampleIPInfo
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			NewValue:    exampleIPInfo,
			Description: fmt.Sprintf("根据 %s 补充IP信息", sourceField),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("根据 %s 字段补充IP信息，结果保存到 %s 字段", sourceField, outputField),
		Changes:     changes,
	}
}
