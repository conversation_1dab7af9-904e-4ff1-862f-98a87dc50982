package event_generation

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取事件生成节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "event_generation",
		Name:        "事件生成",
		Description: "从上下文中取数据，创建一条运营事件",
		Category:    "business",
		NodeType:    "rect",
		Icon:        "event",
		FormFields: []base.FormField{
			{
				Key:         "categoryId",
				Label:       "事件分类",
				Type:        "select",
				Required:    true,
				Placeholder: "请选择事件分类",
				Options:     []base.FormFieldOption{}, // 动态加载事件分类选项
				Validation: map[string]interface{}{
					"required":   true,
					"dynamicApi": "/event-categories/options", // 标识需要动态加载选项的API
				},
			},
			{
				Key:         "occurredCondition",
				Label:       "发生条件",
				Type:        "textarea",
				Required:    true,
				Placeholder: "请输入发生条件，支持变量替换如：${user.name}",
				Validation: map[string]interface{}{
					"required":  true,
					"maxLength": 1000,
				},
			},
			{
				Key:         "description",
				Label:       "事件描述",
				Type:        "textarea",
				Required:    false,
				Placeholder: "请输入事件描述，支持变量替换如：${user.email}",
				Validation: map[string]interface{}{
					"maxLength": 2000,
				},
			},
			{
				Key:         "relatedEmployeeEmailField",
				Label:       "关联员工邮箱",
				Type:        "input",
				Required:    false,
				Placeholder: "支持JSON路径，如: user.email, data.employee_email",
				Validation: map[string]interface{}{
					"maxLength": 100,
				},
			},
		},
	}
}
