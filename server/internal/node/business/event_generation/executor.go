package event_generation

import (
	"context"
	"fmt"
	"log"
	"sec-flow-server/internal/constants"
	"sec-flow-server/internal/model"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/service"
	"sec-flow-server/internal/utils"
	"strings"
	"time"

	"github.com/google/uuid"
)

// EventGenerationNode 事件生成节点
type EventGenerationNode struct {
	config                *base.NodeConfig
	eventOperationService *service.EventOperationService
	eventCategoryService  *service.EventCategoryService
}

// NewEventGenerationNode 创建事件生成节点
func NewEventGenerationNode() *EventGenerationNode {
	return &EventGenerationNode{
		config:                GetNodeConfig(),
		eventOperationService: service.NewEventOperationService(),
		eventCategoryService:  service.NewEventCategoryService(),
	}
}

// GetID 获取节点ID
func (n *EventGenerationNode) GetID() string {
	return n.config.ID
}

// GetConfig 获取节点配置
func (n *EventGenerationNode) GetConfig() *base.NodeConfig {
	return n.config
}

// Validate 验证节点参数
func (n *EventGenerationNode) Validate(params map[string]interface{}) error {
	// 验证必填参数
	if categoryId, ok := params["categoryId"]; !ok || categoryId == "" {
		return fmt.Errorf("categoryId is required")
	}

	if occurredCondition, ok := params["occurredCondition"]; !ok || occurredCondition == "" {
		return fmt.Errorf("occurredCondition is required")
	}

	// 验证字符串长度
	if occurredCondition, ok := params["occurredCondition"].(string); ok {
		if len(occurredCondition) > 1000 {
			return fmt.Errorf("occurredCondition length cannot exceed 1000 characters")
		}
	}

	if description, ok := params["description"].(string); ok {
		if len(description) > 2000 {
			return fmt.Errorf("description length cannot exceed 2000 characters")
		}
	}

	if relatedEmployeeEmailField, ok := params["relatedEmployeeEmailField"].(string); ok {
		if len(relatedEmployeeEmailField) > 100 {
			return fmt.Errorf("relatedEmployeeEmailField length cannot exceed 100 characters")
		}
	}

	return nil
}

// Execute 执行节点逻辑
func (n *EventGenerationNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行事件生成节点: %s", input.NodeID)

	// 验证参数
	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 获取参数
	categoryId := input.Params["categoryId"].(string)
	occurredCondition := input.Params["occurredCondition"].(string)
	description := ""
	if desc, ok := input.Params["description"].(string); ok {
		description = desc
	}
	relatedEmployeeEmailField := ""
	if field, ok := input.Params["relatedEmployeeEmailField"].(string); ok {
		relatedEmployeeEmailField = field
	}

	log.Printf("参数 - categoryId: %s, occurredCondition: %s, description: %s, relatedEmployeeEmailField: %s",
		categoryId, occurredCondition, description, relatedEmployeeEmailField)

	// 获取事件分类信息
	category, err := n.eventCategoryService.GetCategory(categoryId)
	if err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   fmt.Sprintf("failed to get event category: %v", err),
		}, nil
	}

	// 使用ReplaceVariablesInText替换发生条件和事件描述中的变量
	processedOccurredCondition := utils.ReplaceVariablesInText(occurredCondition, input.FlowData)
	processedDescription := utils.ReplaceVariablesInText(description, input.FlowData)

	// 处理关联员工邮箱
	var relatedEmployeeIDs []string
	if relatedEmployeeEmailField != "" {
		emailValue, found := utils.GetFieldValueFromSources(input.PrevOutput, input.FlowData, relatedEmployeeEmailField)
		if found && emailValue != nil {
			emailStr := fmt.Sprintf("%v", emailValue)
			if emailStr != "" {
				// 去掉邮箱后缀，获取用户名
				username := strings.TrimSuffix(emailStr, constants.EmailSuffix)
				if username != emailStr { // 确保确实去掉了后缀
					relatedEmployeeIDs = append(relatedEmployeeIDs, username)
				}
			}
		}
	}

	// 创建运营事件请求
	createRequest := &model.EventOperationCreateRequest{
		OccurredAt:         time.Now(),
		OccurredCondition:  processedOccurredCondition,
		CategoryID:         categoryId,
		Description:        processedDescription,
		OwnerID:            category.OwnerID, // 负责人取事件分类中关联的负责人
		RelatedEmployeeIDs: relatedEmployeeIDs,
		ProcessType:        model.EventProcessTypeUnprocessed, // 默认为未处理
		ProcessDescription: "",
		ProcessCompletedAt: nil,
	}

	// 创建运营事件
	operation, err := n.eventOperationService.CreateOperation(createRequest)
	if err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   fmt.Sprintf("failed to create event operation: %v", err),
		}, nil
	}

	log.Printf("事件生成完成，事件ID: %s", operation.ID)

	// 构造输出数据
	outputData := map[string]interface{}{
		"eventId":            operation.ID,
		"occurredAt":         operation.OccurredAt,
		"occurredCondition":  operation.OccurredCondition,
		"categoryId":         operation.CategoryID,
		"categoryName":       category.Name,
		"description":        operation.Description,
		"ownerId":            operation.OwnerID,
		"relatedEmployeeIds": relatedEmployeeIDs,
		"processType":        operation.ProcessType,
		"processDescription": operation.ProcessDescription,
	}

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 将事件信息添加到流程数据中
	updatedFlowData["generatedEvent"] = outputData

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true, // 继续执行后续节点
		Data:     outputData,
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("成功创建运营事件，事件ID: %s", operation.ID),
		Metadata: map[string]interface{}{
			"nodeType":    "event_generation",
			"eventId":     operation.ID,
			"categoryId":  categoryId,
			"processedAt": time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// Example 生成示例数据
func (n *EventGenerationNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 生成示例事件数据
	exampleEventData := map[string]interface{}{
		"eventId":            uuid.New().String(),
		"occurredAt":         time.Now(),
		"occurredCondition":  "检测到用户张三进行了异常操作",
		"categoryId":         "example-category-id",
		"categoryName":       "安全事件",
		"description":        "用户在非工作时间访问了敏感数据",
		"ownerId":            "example-owner-id",
		"relatedEmployeeIds": []string{"zhangsan"},
		"processType":        "unprocessed",
		"processDescription": "",
	}

	// 添加到流程数据中
	resultFlowData["generatedEvent"] = exampleEventData

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       "generatedEvent",
			OldValue:    nil,
			NewValue:    exampleEventData,
			Description: "创建了新的运营事件",
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: "根据配置参数和流程数据创建运营事件，并将事件信息添加到流程数据中",
		Changes:     changes,
	}
}
