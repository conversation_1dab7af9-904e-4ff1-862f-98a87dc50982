package user_info

import (
	"context"
	"fmt"
	"log"
	"reflect"
	"regexp"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/service/third_party/lark"
	"sec-flow-server/internal/utils"
	"strconv"
	"strings"
	"time"
)

// UserInfoNode 用户信息补充节点
type UserInfoNode struct {
	config *base.NodeConfig
}

// NewUserInfoNode 创建用户信息补充节点
func NewUserInfoNode() *UserInfoNode {
	return &UserInfoNode{
		config: GetNodeConfig(),
	}
}

// GetID 获取节点ID
func (n *UserInfoNode) GetID() string {
	return n.config.ID
}

// GetConfig 获取节点配置
func (n *UserInfoNode) GetConfig() *base.NodeConfig {
	return n.config
}

// Validate 验证节点参数
func (n *UserInfoNode) Validate(params map[string]interface{}) error {
	// 验证必填字段
	sourceField, ok := params["sourceField"].(string)
	if !ok || sourceField == "" {
		return fmt.Errorf("sourceField is required")
	}

	sourceFieldType, ok := params["sourceFieldType"].(string)
	if !ok || sourceFieldType == "" {
		return fmt.Errorf("sourceFieldType is required")
	}

	outputField, ok := params["outputField"].(string)
	if !ok || outputField == "" {
		return fmt.Errorf("outputField is required")
	}

	// 验证字段类型
	validTypes := map[string]bool{
		"email":    true,
		"user_id":  true,
		"username": true,
	}
	if !validTypes[sourceFieldType] {
		return fmt.Errorf("invalid sourceFieldType: %s", sourceFieldType)
	}

	return nil
}

// Execute 执行节点逻辑
func (n *UserInfoNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行用户信息补充节点: %s", input.NodeID)

	// 验证参数
	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 获取参数
	sourceField := input.Params["sourceField"].(string)
	sourceFieldType := input.Params["sourceFieldType"].(string)
	outputField := input.Params["outputField"].(string)

	log.Printf("参数 - sourceField: %s, sourceFieldType: %s, outputField: %s",
		sourceField, sourceFieldType, outputField)

	// 检查是否是数组遍历操作 (如: data[].userId)
	if strings.Contains(sourceField, "[]") {
		return n.executeArrayUserInfo(ctx, input, sourceField, sourceFieldType, outputField)
	}

	// 检查是否是数组索引操作 (如: data[0].userId)
	if strings.Contains(sourceField, "[") && strings.Contains(sourceField, "]") && !strings.Contains(sourceField, "[]") {
		return n.executeIndexUserInfo(ctx, input, sourceField, sourceFieldType, outputField)
	}

	// 从输入数据或上一个节点的输出中获取源字段值 (支持JSON路径表达式)
	sourceValue, found := utils.GetFieldValueFromSources(input.PrevOutput, input.FlowData, sourceField)
	if !found {
		return &base.ExecutionOutput{
			Success: false,
			Error:   fmt.Sprintf("source field '%s' not found in input data", sourceField),
		}, nil
	}

	// 模拟用户信息补充逻辑
	userInfo, err := n.supplementUserInfo(sourceValue, sourceFieldType)
	if err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 构造输出数据
	outputData := make(map[string]interface{})
	outputData[outputField] = userInfo

	log.Printf("用户信息补充完成，输出: %v", outputData)

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 将用户信息添加到流程数据中 (支持JSON路径表达式)
	if !utils.SetFieldValue(updatedFlowData, outputField, userInfo) {
		// 如果设置失败，回退到简单字段设置
		updatedFlowData[outputField] = userInfo
	}

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true, // 继续执行后续节点
		Data:     outputData,
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("成功补充用户信息到字段 '%s'", outputField),
		Metadata: map[string]interface{}{
			"nodeType":    "user_info_supplement",
			"sourceField": sourceField,
			"outputField": outputField,
			"processedAt": time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// executeArrayUserInfo 执行数组遍历用户信息补充
func (n *UserInfoNode) executeArrayUserInfo(ctx context.Context, input *base.ExecutionInput, sourceField, sourceFieldType, outputField string) (*base.ExecutionOutput, error) {
	log.Printf("🔄 执行数组用户信息补充: %s -> %s (类型: %s)", sourceField, outputField, sourceFieldType)

	// 解析输入字段路径：data[].userId → arrayPath="data", fieldName="userId"
	parts := strings.Split(sourceField, "[]")
	if len(parts) != 2 {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("invalid array field path: %s", sourceField),
			FlowData: input.FlowData,
		}, nil
	}

	arrayPath := strings.TrimSuffix(parts[0], ".")
	fieldName := strings.TrimPrefix(parts[1], ".")

	// 获取数组对象
	arrayObj, found := utils.GetFieldValueFromSources(input.PrevOutput, input.FlowData, arrayPath)
	if !found {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("array field '%s' not found in input data", arrayPath),
			FlowData: input.FlowData,
		}, nil
	}

	// 确保是数组类型
	arrayValue := reflect.ValueOf(arrayObj)
	if arrayValue.Kind() != reflect.Slice && arrayValue.Kind() != reflect.Array {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("field '%s' is not an array", arrayPath),
			FlowData: input.FlowData,
		}, nil
	}

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	processedCount := 0
	// 遍历数组，为每个元素补充用户信息
	for i := 0; i < arrayValue.Len(); i++ {
		element := arrayValue.Index(i).Interface()

		// 确保元素是map类型
		if elementMap, ok := element.(map[string]interface{}); ok {
			// 从元素中获取输入字段的值
			var sourceValue interface{}
			if fieldName == "" {
				// 如果没有字段名，使用整个元素
				sourceValue = element
			} else {
				// 获取指定字段的值
				if val, exists := elementMap[fieldName]; exists {
					sourceValue = val
				} else {
					log.Printf("⚠️ 第%d个元素中没有找到字段 '%s'", i, fieldName)
					continue
				}
			}

			// 补充用户信息
			userInfo, err := n.supplementUserInfo(sourceValue, sourceFieldType)
			if err != nil {
				log.Printf("❌ 补充第%d个元素的用户信息失败: %v", i, err)
				continue
			}

			// 解析输出字段路径并设置值
			outputParts := strings.Split(outputField, "[]")
			if len(outputParts) == 2 {
				outputFieldName := strings.TrimPrefix(outputParts[1], ".")
				if outputFieldName != "" {
					elementMap[outputFieldName] = userInfo
					processedCount++
				}
			}
		}
	}

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true,
		Data:     map[string]interface{}{"processedCount": processedCount},
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("成功为 %d 个数组元素补充用户信息", processedCount),
		Metadata: map[string]interface{}{
			"nodeType":       "user_info_supplement",
			"operationType":  "array",
			"sourceField":    sourceField,
			"outputField":    outputField,
			"processedCount": processedCount,
			"processedAt":    time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// executeIndexUserInfo 执行数组索引用户信息补充
func (n *UserInfoNode) executeIndexUserInfo(ctx context.Context, input *base.ExecutionInput, sourceField, sourceFieldType, outputField string) (*base.ExecutionOutput, error) {
	log.Printf("🔄 执行数组索引用户信息补充: %s -> %s (类型: %s)", sourceField, outputField, sourceFieldType)

	// 解析数组索引路径：data[0].userId
	re := regexp.MustCompile(`^(.+)\[(\d+)\]\.?(.*)$`)
	matches := re.FindStringSubmatch(sourceField)
	if len(matches) != 4 {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("invalid array index field path: %s", sourceField),
			FlowData: input.FlowData,
		}, nil
	}

	arrayPath := matches[1]
	indexStr := matches[2]
	fieldName := matches[3]

	// 解析索引
	index, err := strconv.Atoi(indexStr)
	if err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("invalid array index: %s", indexStr),
			FlowData: input.FlowData,
		}, nil
	}

	// 获取数组对象
	arrayObj, found := utils.GetFieldValueFromSources(input.PrevOutput, input.FlowData, arrayPath)
	if !found {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("array field '%s' not found in input data", arrayPath),
			FlowData: input.FlowData,
		}, nil
	}

	// 确保是数组类型
	arrayValue := reflect.ValueOf(arrayObj)
	if arrayValue.Kind() != reflect.Slice && arrayValue.Kind() != reflect.Array {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("field '%s' is not an array", arrayPath),
			FlowData: input.FlowData,
		}, nil
	}

	// 检查索引范围
	if index < 0 || index >= arrayValue.Len() {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("array index %d out of range (length: %d)", index, arrayValue.Len()),
			FlowData: input.FlowData,
		}, nil
	}

	// 获取指定索引的元素
	element := arrayValue.Index(index).Interface()

	// 确保元素是map类型
	elementMap, ok := element.(map[string]interface{})
	if !ok {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("array element at index %d is not a map", index),
			FlowData: input.FlowData,
		}, nil
	}

	// 从元素中获取输入字段的值
	var sourceValue interface{}
	if fieldName == "" {
		// 如果没有字段名，使用整个元素
		sourceValue = element
	} else {
		// 获取指定字段的值
		if val, exists := elementMap[fieldName]; exists {
			sourceValue = val
		} else {
			return &base.ExecutionOutput{
				Success:  false,
				Continue: false,
				Error:    fmt.Sprintf("field '%s' not found in array element at index %d", fieldName, index),
				FlowData: input.FlowData,
			}, nil
		}
	}

	// 补充用户信息
	userInfo, err := n.supplementUserInfo(sourceValue, sourceFieldType)
	if err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("failed to supplement user info: %v", err),
			FlowData: input.FlowData,
		}, nil
	}

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 解析输出字段路径并设置值
	outputRe := regexp.MustCompile(`^(.+)\[(\d+)\]\.?(.*)$`)
	outputMatches := outputRe.FindStringSubmatch(outputField)
	if len(outputMatches) == 4 {
		outputArrayPath := outputMatches[1]
		outputIndexStr := outputMatches[2]
		outputFieldName := outputMatches[3]

		outputIndex, err := strconv.Atoi(outputIndexStr)
		if err == nil && outputIndex == index && outputArrayPath == arrayPath {
			// 直接设置到指定索引的元素中
			if outputFieldName != "" {
				elementMap[outputFieldName] = userInfo
			}
		}
	} else {
		// 如果输出字段不是数组索引格式，使用通用设置方法
		if !utils.SetFieldValue(updatedFlowData, outputField, userInfo) {
			updatedFlowData[outputField] = userInfo
		}
	}

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true,
		Data:     map[string]interface{}{"userInfo": userInfo},
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("成功为数组索引 %d 补充用户信息", index),
		Metadata: map[string]interface{}{
			"nodeType":      "user_info_supplement",
			"operationType": "index",
			"sourceField":   sourceField,
			"outputField":   outputField,
			"arrayIndex":    index,
			"processedAt":   time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// supplementUserInfo 模拟用户信息补充逻辑
func (n *UserInfoNode) supplementUserInfo(sourceValue interface{}, sourceFieldType string) (map[string]interface{}, error) {
	sourceStr, ok := sourceValue.(string)
	if !ok {
		return nil, fmt.Errorf("source value must be a string")
	}

	// 模拟根据不同类型补充用户信息
	userInfo := make(map[string]interface{})

	switch sourceFieldType {
	case "email":
		userInfo["email"] = sourceStr
		userInfo["username"] = extractUsernameFromEmail(sourceStr)
		userInfo["domain"] = extractDomainFromEmail(sourceStr)
		userInfo["user_type"] = "email_user"
	case "user_id":
		return lark.FillLarkUserInfo(sourceStr)
	case "username":
		userInfo["username"] = sourceStr
		userInfo["display_name"] = sourceStr
		userInfo["user_type"] = "username_user"
	default:
		return nil, fmt.Errorf("unsupported source field type: %s", sourceFieldType)
	}

	return userInfo, nil
}

// extractUsernameFromEmail 从邮箱中提取用户名
func extractUsernameFromEmail(email string) string {
	for i, char := range email {
		if char == '@' {
			return email[:i]
		}
	}
	return email
}

// extractDomainFromEmail 从邮箱中提取域名
func extractDomainFromEmail(email string) string {
	for i, char := range email {
		if char == '@' && i+1 < len(email) {
			return email[i+1:]
		}
	}
	return ""
}

// Example 生成示例数据
func (n *UserInfoNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置参数
	sourceField := "userId"
	sourceFieldType := "user_id"
	outputField := "userInfo"

	if input.Config != nil {
		if sf, ok := input.Config["sourceField"].(string); ok && sf != "" {
			sourceField = sf
		}
		if sft, ok := input.Config["sourceFieldType"].(string); ok && sft != "" {
			sourceFieldType = sft
		}
		if of, ok := input.Config["outputField"].(string); ok && of != "" {
			outputField = of
		}
	}

	// 检查是否是数组操作
	if strings.Contains(sourceField, "[]") {
		return n.generateArrayExample(input, sourceField, sourceFieldType, outputField)
	}

	if strings.Contains(sourceField, "[") && strings.Contains(sourceField, "]") && !strings.Contains(sourceField, "[]") {
		return n.generateIndexExample(input, sourceField, sourceFieldType, outputField)
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 生成示例用户信息
	// 生成示例用户信息
	exampleUserInfo := map[string]interface{}{
		"user_id":        "abcdefgh",
		"name":           "张三",
		"email":          "<EMAIL>",
		"leader_user_id": "hgfedcba",
		"department_ids": []string{"od_1234567890abcdef"},
		"join_time":      1640995200, // 2022-01-01 00:00:00 UTC
		"status": map[string]interface{}{
			"is_resigned": false,
		},
		"custom_attrs": []map[string]interface{}{
			{
				"id": "C-6832109570425372674",
				"value": map[string]interface{}{
					"text": "研发工程师",
				},
			},
			{
				"id": "C-1234567890123456789",
				"value": map[string]interface{}{
					"text": "技术部",
				},
			},
		},
		"dept": "技术部-研发组-后端团队",
		"leader": map[string]interface{}{
			"leader_id":    "hgfedcba",
			"leader_name":  "李四",
			"leader_email": "<EMAIL>",
		},
	}

	// 添加到流程数据中 (支持JSON路径表达式)
	if !utils.SetFieldValue(resultFlowData, outputField, exampleUserInfo) {
		// 如果设置失败，回退到简单字段设置
		resultFlowData[outputField] = exampleUserInfo
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			NewValue:    exampleUserInfo,
			Description: fmt.Sprintf("根据 %s 补充用户信息", sourceField),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("从 %s 字段获取用户ID，补充详细的用户信息到 %s 字段", sourceField, outputField),
		Changes:     changes,
	}
}

// generateArrayExample 生成数组操作示例
func (n *UserInfoNode) generateArrayExample(input *base.ExampleInput, sourceField, sourceFieldType, outputField string) *base.ExampleOutput {
	// 解析数组路径
	parts := strings.Split(sourceField, "[]")
	if len(parts) != 2 {
		return n.generateNormalExample(input, sourceField, sourceFieldType, outputField)
	}

	arrayPath := strings.TrimSuffix(parts[0], ".")
	fieldName := strings.TrimPrefix(parts[1], ".")

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 创建示例数组数据（如果不存在）
	if _, exists := resultFlowData[arrayPath]; !exists {
		exampleArray := []interface{}{
			map[string]interface{}{
				fieldName: "abcdefgh",
				"name":    "用户1",
			},
			map[string]interface{}{
				fieldName: "ijklmnop",
				"name":    "用户2",
			},
		}
		resultFlowData[arrayPath] = exampleArray
	}

	// 为数组中的每个元素添加用户信息
	if arrayData, ok := resultFlowData[arrayPath].([]interface{}); ok {
		for _, item := range arrayData {
			if itemMap, ok := item.(map[string]interface{}); ok {
				// 解析输出字段路径
				outputParts := strings.Split(outputField, "[]")
				if len(outputParts) == 2 {
					outputFieldName := strings.TrimPrefix(outputParts[1], ".")
					if outputFieldName != "" {
						// 生成示例用户信息
						userInfo := map[string]interface{}{
							"user_id":        itemMap[fieldName],
							"name":           fmt.Sprintf("用户_%v", itemMap[fieldName]),
							"email":          fmt.Sprintf("<EMAIL>", itemMap[fieldName]),
							"leader_user_id": "hgfedcba",
							"department_ids": []string{"od_1234567890abcdef"},
							"join_time":      1640995200,
							"status": map[string]interface{}{
								"is_resigned": false,
							},
							"dept": "技术部-研发组-后端团队",
						}
						itemMap[outputFieldName] = userInfo
					}
				}
			}
		}
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			NewValue:    "用户信息对象",
			Description: fmt.Sprintf("为数组 %s 中的每个元素根据 %s 补充用户信息", arrayPath, fieldName),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("为数组中的每个元素根据 %s 补充用户信息", sourceField),
		Changes:     changes,
	}
}

// generateIndexExample 生成数组索引操作示例
func (n *UserInfoNode) generateIndexExample(input *base.ExampleInput, sourceField, sourceFieldType, outputField string) *base.ExampleOutput {
	// 解析数组索引路径
	re := regexp.MustCompile(`^(.+)\[(\d+)\]\.?(.*)$`)
	matches := re.FindStringSubmatch(sourceField)
	if len(matches) != 4 {
		return n.generateNormalExample(input, sourceField, sourceFieldType, outputField)
	}

	arrayPath := matches[1]
	indexStr := matches[2]
	fieldName := matches[3]

	index, err := strconv.Atoi(indexStr)
	if err != nil {
		return n.generateNormalExample(input, sourceField, sourceFieldType, outputField)
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 创建示例数组数据（如果不存在）
	if _, exists := resultFlowData[arrayPath]; !exists {
		exampleArray := []interface{}{
			map[string]interface{}{
				fieldName: "abcdefgh",
				"name":    "用户1",
			},
			map[string]interface{}{
				fieldName: "ijklmnop",
				"name":    "用户2",
			},
		}
		resultFlowData[arrayPath] = exampleArray
	}

	// 为指定索引的元素添加用户信息
	if arrayData, ok := resultFlowData[arrayPath].([]interface{}); ok && index < len(arrayData) {
		if itemMap, ok := arrayData[index].(map[string]interface{}); ok {
			// 解析输出字段路径
			outputRe := regexp.MustCompile(`^(.+)\[(\d+)\]\.?(.*)$`)
			outputMatches := outputRe.FindStringSubmatch(outputField)
			if len(outputMatches) == 4 {
				outputFieldName := outputMatches[3]
				if outputFieldName != "" {
					// 生成示例用户信息
					userInfo := map[string]interface{}{
						"user_id":        itemMap[fieldName],
						"name":           fmt.Sprintf("用户_%v", itemMap[fieldName]),
						"email":          fmt.Sprintf("<EMAIL>", itemMap[fieldName]),
						"leader_user_id": "hgfedcba",
						"department_ids": []string{"od_1234567890abcdef"},
						"join_time":      1640995200,
						"status": map[string]interface{}{
							"is_resigned": false,
						},
						"dept": "技术部-研发组-后端团队",
					}
					itemMap[outputFieldName] = userInfo
				}
			}
		}
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			NewValue:    "用户信息对象",
			Description: fmt.Sprintf("为数组索引 %d 的元素根据 %s 补充用户信息", index, fieldName),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("为数组索引 %d 的元素根据 %s 补充用户信息", index, sourceField),
		Changes:     changes,
	}
}

// generateNormalExample 生成普通操作示例
func (n *UserInfoNode) generateNormalExample(input *base.ExampleInput, sourceField, sourceFieldType, outputField string) *base.ExampleOutput {
	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 生成示例用户信息
	exampleUserInfo := map[string]interface{}{
		"user_id":        "abcdefgh",
		"name":           "张三",
		"email":          "<EMAIL>",
		"leader_user_id": "hgfedcba",
		"department_ids": []string{"od_1234567890abcdef"},
		"join_time":      1640995200,
		"status": map[string]interface{}{
			"is_resigned": false,
		},
		"custom_attrs": []map[string]interface{}{
			{
				"id": "C-6832109570425372674",
				"value": map[string]interface{}{
					"text": "研发工程师",
				},
			},
		},
		"dept": "技术部-研发组-后端团队",
		"leader": map[string]interface{}{
			"leader_id":    "hgfedcba",
			"leader_name":  "李四",
			"leader_email": "<EMAIL>",
		},
	}

	// 设置示例源数据（如果不存在）
	if _, exists := resultFlowData[sourceField]; !exists {
		switch sourceFieldType {
		case "user_id":
			resultFlowData[sourceField] = "abcdefgh"
		case "email":
			resultFlowData[sourceField] = "<EMAIL>"
		case "phone":
			resultFlowData[sourceField] = "13800138000"
		default:
			resultFlowData[sourceField] = "abcdefgh"
		}
	}

	// 将用户信息添加到流程数据中
	if !utils.SetFieldValue(resultFlowData, outputField, exampleUserInfo) {
		resultFlowData[outputField] = exampleUserInfo
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			NewValue:    exampleUserInfo,
			Description: fmt.Sprintf("根据 %s 补充用户信息", sourceField),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("根据 %s 字段补充用户信息，结果保存到 %s 字段", sourceField, outputField),
		Changes:     changes,
	}
}
