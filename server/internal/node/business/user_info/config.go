package user_info

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取用户信息补充节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "user-info-supplement",
		Name:        "用户信息补充",
		Description: "根据指定字段补充用户信息",
		Category:    "business",
		NodeType:    "rect",
		Icon:        "user",
		FormFields: []base.FormField{
			{
				Key:          "sourceField",
				Label:        "用户来源字段",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "支持JSON路径，如: user.id, data.email",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 50,
				},
			},
			{
				Key:          "sourceFieldType",
				Label:        "用户来源字段类型",
				Type:         "select",
				Required:     true,
				DefaultValue: "email",
				Options: []base.FormFieldOption{
					{Label: "邮箱", Value: "email"},
					{Label: "用户ID", Value: "user_id"},
					{Label: "用户名", Value: "username"},
				},
			},
			{
				Key:          "outputField",
				Label:        "输出字段",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "支持JSON路径，如: user.info, result.profile",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 50,
				},
			},
		},
		Style: base.NodeStyle{
			Fill:         "#e6f7ff",
			Stroke:       "#1890ff",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}
}
