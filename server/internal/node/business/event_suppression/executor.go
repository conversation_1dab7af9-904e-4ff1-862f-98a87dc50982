package event_suppression

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sec-flow-server/internal/model"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/service"
	"time"

	"github.com/google/uuid"
)

// EventSuppressionNode 事件压制节点
type EventSuppressionNode struct {
	config                  *base.NodeConfig
	eventSuppressionService *service.EventSuppressionService
}

// NewEventSuppressionNode 创建事件压制节点
func NewEventSuppressionNode() *EventSuppressionNode {
	return &EventSuppressionNode{
		config:                  GetNodeConfig(),
		eventSuppressionService: service.NewEventSuppressionService(),
	}
}

// GetID 获取节点ID
func (n *EventSuppressionNode) GetID() string {
	return n.config.ID
}

// GetConfig 获取节点配置
func (n *EventSuppressionNode) GetConfig() *base.NodeConfig {
	return n.config
}

// Validate 验证节点参数
func (n *EventSuppressionNode) Validate(params map[string]interface{}) error {
	// 验证必填参数
	if timeWindow, ok := params["timeWindow"]; !ok || timeWindow == "" {
		return fmt.Errorf("timeWindow is required")
	}

	if dedupKeyTemplate, ok := params["dedupKeyTemplate"]; !ok || dedupKeyTemplate == "" {
		return fmt.Errorf("dedupKeyTemplate is required")
	}

	// 验证时间窗口
	timeWindow := params["timeWindow"].(string)
	if timeWindow == "custom" {
		if customTimeWindow, ok := params["customTimeWindow"]; !ok || customTimeWindow == "" {
			return fmt.Errorf("customTimeWindow is required when timeWindow is custom")
		}
	}

	return nil
}

// Execute 执行节点逻辑
func (n *EventSuppressionNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行事件压制节点: %s", input.NodeID)

	// 验证参数
	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 解析配置
	config, err := n.parseConfig(input.Params)
	if err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   fmt.Sprintf("failed to parse config: %v", err),
		}, nil
	}

	log.Printf("压制配置 - 时间窗口: %s, 压制键模板: %s, 突破条件数量: %d",
		config.TimeWindow, config.DedupKeyTemplate, len(config.BreakConditions))

	// 获取事件数据
	eventData := make(map[string]interface{})
	if input.PrevOutput != nil {
		for k, v := range input.PrevOutput {
			eventData[k] = v
		}
	}

	// 如果流程数据中有生成的事件，优先使用
	if generatedEvent, ok := input.FlowData["generatedEvent"].(map[string]interface{}); ok {
		for k, v := range generatedEvent {
			eventData[k] = v
		}
	}

	if len(eventData) == 0 {
		return &base.ExecutionOutput{
			Success: false,
			Error:   "no event data found for suppression",
		}, nil
	}

	log.Printf("处理事件数据: %+v", eventData)

	// 执行去重检查
	result, err := n.eventSuppressionService.CheckAndProcessSuppression(config, eventData, input.FlowData)
	if err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   fmt.Sprintf("failed to process Suppression: %v", err),
		}, nil
	}

	log.Printf("压制处理结果 - 是否新事件: %t, 是否继续: %t, 消息: %s",
		result.IsNew, result.ShouldContinue, result.Message)

	// 构造输出数据
	outputData := map[string]interface{}{
		"isNew":            result.IsNew,
		"shouldContinue":   result.ShouldContinue,
		"dedupRecordId":    result.Record.ID,
		"dedupKey":         result.Record.DedupKey,
		"occurrenceCount":  result.Record.OccurrenceCount,
		"suppressionCount": result.Record.SuppressionCount,
		"message":          result.Message,
		"eventData":        result.UpdatedEventData,
	}

	// 更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 更新事件数据
	if result.UpdatedEventData != nil {
		updatedFlowData["generatedEvent"] = result.UpdatedEventData
	}

	// 添加压制信息
	updatedFlowData["suppressionResult"] = outputData

	return &base.ExecutionOutput{
		Success:  true,
		Continue: result.ShouldContinue,
		Data:     outputData,
		FlowData: updatedFlowData,
		Message:  result.Message,
		Metadata: map[string]interface{}{
			"nodeType":       "event_suppression",
			"dedupRecordId":  result.Record.ID,
			"isNew":          result.IsNew,
			"shouldContinue": result.ShouldContinue,
			"processedAt":    time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// parseConfig 解析配置
func (n *EventSuppressionNode) parseConfig(params map[string]interface{}) (*model.SuppressionConfig, error) {
	config := &model.SuppressionConfig{}

	// 基本配置
	config.TimeWindow = params["timeWindow"].(string)
	config.DedupKeyTemplate = params["dedupKeyTemplate"].(string)

	if customTimeWindow, ok := params["customTimeWindow"].(string); ok {
		config.CustomTimeWindow = customTimeWindow
	}

	// 解析突破条件配置
	if breakConditionsData, ok := params["breakConditions"]; ok {
		breakConditions, err := n.parseBreakConditions(breakConditionsData)
		if err != nil {
			return nil, fmt.Errorf("failed to parse break conditions: %v", err)
		}
		config.BreakConditions = breakConditions
	}

	return config, nil
}

// parseBreakConditions 解析突破条件配置
func (n *EventSuppressionNode) parseBreakConditions(data interface{}) ([]model.BreakCondition, error) {
	var breakConditions []model.BreakCondition

	switch v := data.(type) {
	case string:
		// 如果是字符串，尝试解析为JSON
		if v != "" {
			err := json.Unmarshal([]byte(v), &breakConditions)
			if err != nil {
				return nil, fmt.Errorf("failed to unmarshal break conditions JSON: %v", err)
			}
		}
	case []interface{}:
		// 如果是数组，逐个解析
		for _, item := range v {
			if itemMap, ok := item.(map[string]interface{}); ok {
				condition := model.BreakCondition{}

				if field, ok := itemMap["field"].(string); ok {
					condition.Field = field
				}
				if operator, ok := itemMap["operator"].(string); ok {
					condition.Operator = operator
				}
				if value, ok := itemMap["value"].(string); ok {
					condition.Value = value
				}

				breakConditions = append(breakConditions, condition)
			}
		}
	}

	return breakConditions, nil
}

// Example 生成示例数据
func (n *EventSuppressionNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 生成示例压制结果
	exampleResult := map[string]interface{}{
		"isNew":            false,
		"shouldContinue":   false,
		"dedupRecordId":    uuid.New().String(),
		"dedupKey":         "user123-security_event",
		"occurrenceCount":  3,
		"suppressionCount": 2,
		"message":          "事件被压制，发生次数: 3，压制次数: 2",
		"eventData": map[string]interface{}{
			"eventId":     uuid.New().String(),
			"userId":      "user123",
			"count":       15,
			"description": "用户进行了15次敏感操作",
		},
	}

	// 添加到流程数据中
	resultFlowData["suppressionResult"] = exampleResult

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       "suppressionResult",
			OldValue:    nil,
			NewValue:    exampleResult,
			Description: "执行事件压制检查，发现重复事件并进行压制处理",
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: "根据配置的压制规则检查事件，如果发现重复则进行压制处理，避免重复告警",
		Changes:     changes,
	}
}
