package event_suppression

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取事件压制节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "event_suppression",
		Name:        "事件压制",
		Description: "根据配置的规则对事件进行压制，避免重复告警",
		Category:    "business",
		NodeType:    "rect",
		Icon:        "filter",
		FormFields: []base.FormField{
			{
				Key:         "timeWindow",
				Label:       "压制时间窗口",
				Type:        "select",
				Required:    true,
				Placeholder: "请选择压制时间窗口",
				Options: []base.FormFieldOption{
					{Label: "5分钟", Value: "5m"},
					{Label: "15分钟", Value: "15m"},
					{Label: "1小时", Value: "1h"},
					{Label: "6小时", Value: "6h"},
					{Label: "24小时", Value: "24h"},
					{Label: "自定义", Value: "custom"},
				},
				Validation: map[string]interface{}{
					"required": true,
				},
			},
			{
				Key:         "customTimeWindow",
				Label:       "自定义时间窗口",
				Type:        "input",
				Required:    false,
				Placeholder: "如：2h30m、90m、3600s",
				Validation: map[string]interface{}{
					"maxLength": 20,
					"pattern":   "^\\d+[smhd]$",
				},
			},
			{
				Key:         "dedupKeyTemplate",
				Label:       "压制键模板",
				Type:        "input",
				Required:    true,
				Placeholder: "如：${userId}-${categoryId}、${employeeId}-${operType}",
				Validation: map[string]interface{}{
					"required":  true,
					"maxLength": 200,
				},
			},
			{
				Key:         "breakConditions",
				Label:       "突破压制条件",
				Type:        "condition_array",
				Required:    false,
				Placeholder: "配置突破压制的条件，当任一条件满足时将突破压制继续执行",
				Validation: map[string]interface{}{
					"maxItems": 10,
				},
			},
		},
	}
}
