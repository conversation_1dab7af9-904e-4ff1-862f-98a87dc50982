package lark_notice

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取用户信息补充节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "lark_notice",
		Name:        "飞书消息通知",
		Description: "发送飞书消息通知",
		Category:    "business",
		NodeType:    "rect",
		Icon:        "user",
		FormFields: []base.FormField{
			{
				Key:          "receiverType",
				Label:        "接收通知主体类型",
				Type:         "select",
				Required:     true,
				DefaultValue: "group_id",
				Options: []base.FormFieldOption{
					{Label: "固定群聊ID", Value: "group_id"},
					{Label: "固定用户ID", Value: "user_id"},
					{Label: "群聊ID变量", Value: "group_id_var"},
					{Label: "用户ID变量", Value: "user_id_var"},
					{Label: "安全角色：(待支持)", Value: "sec_role_ss"},
					//{Label: "webhook", Value: "webhook"},
					//{Label: "直接领导", Value: "leader"},
				},
			},
			{
				Key:          "receiverId",
				Label:        "接收通知主体值",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "用户飞书user_id、群聊ID或webhook的ID，$开头则从上下文取变量，领导则填写当事人的id",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 50,
				},
			},
			{
				Key:          "title",
				Label:        "消息标题",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "标题",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 50,
				},
			},
			{
				Key:          "noticeContents",
				Label:        "消息内容配置",
				Type:         "lark_array",
				Required:     true,
				DefaultValue: `[{"contentType":"text","contentText":""}]`,
				Placeholder:  "请配置消息内容",
				Validation: map[string]interface{}{
					"contentTypes": GetLarkMsgOptionConfigs(),
				},
			},
		},
		Style: base.NodeStyle{
			Fill:         "#e6f7ff",
			Stroke:       "#1890ff",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}
}
