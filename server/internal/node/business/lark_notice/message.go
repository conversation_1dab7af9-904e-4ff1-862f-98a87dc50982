package lark_notice

import (
	"encoding/json"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/utils"
)

type larkMsgMap struct {
	Label     string
	Value     string
	InputType string // 前端输入控件类型：textarea, none, select 等
	Handler   func(contentText string, flowData map[string]interface{}) interface{}
}

var larkMsgOptions []larkMsgMap
var larkMsgHandleMap map[string]larkMsgMap

func init() {
	larkMsgOptions = make([]larkMsgMap, 0)
	larkMsgHandleMap = make(map[string]larkMsgMap)

	e1 := larkMsgMap{Label: "Markdown文本", Value: "text", InputType: "textarea", Handler: eRichText}
	e2 := larkMsgMap{Label: "自定义JSON", Value: "custom", InputType: "textarea", Handler: eCustom}
	e3 := larkMsgMap{Label: "分割线", Value: "hr", InputType: "none", Handler: eHr}
	e4 := larkMsgMap{Label: "确认事件风险表单", Value: "eventConfirm", InputType: "none"}
	e5 := larkMsgMap{Label: "和直接领导拉群按钮", Value: "leaderGroup", InputType: "none"}
	e6 := larkMsgMap{Label: "数据外发审批表单", Value: "dataOutApproval", InputType: "none"}
	e7 := larkMsgMap{Label: "卡片表格2.0", Value: "table", InputType: "textarea", Handler: eTable}

	larkMsgOptions = append(larkMsgOptions, e1, e2, e3, e4, e5, e6, e7)
	for _, option := range larkMsgOptions {
		larkMsgHandleMap[option.Value] = option
	}
}

func GetLarkMsgOptionConfigs() []base.FormFieldOption {
	result := make([]base.FormFieldOption, 0)
	for _, option := range larkMsgOptions {
		result = append(result, base.FormFieldOption{
			Label: option.Label,
			Value: option.Value,
			Extra: map[string]interface{}{
				"inputType": option.InputType,
			},
		})
	}
	return result
}

func eRichText(contentText string, flowData map[string]interface{}) interface{} {
	text := utils.ReplaceVariablesInText(contentText, flowData)
	larkFormat := `
{
		"tag": "markdown",
		"content": "",
		"text_align": "left",
		"text_size": "normal_v2",
		"margin": "0px 0px 0px 0px"
}
`
	var element = make(map[string]interface{})
	_ = json.Unmarshal([]byte(larkFormat), &element)
	element["content"] = text
	return element
}

func eHr(contentText string, flowData map[string]interface{}) interface{} {
	larkFormat := `
{
	"tag": "hr",
	"margin": "0px 0px 0px 0px"
}
`
	var element = make(map[string]interface{})
	_ = json.Unmarshal([]byte(larkFormat), &element)
	return element
}

func eCustom(contentText string, flowData map[string]interface{}) interface{} {
	text := utils.ReplaceVariablesInText(contentText, flowData)
	var element = make(map[string]interface{})
	_ = json.Unmarshal([]byte(text), &element)
	return element
}

func eTable(contentText string, flowData map[string]interface{}) interface{} {
	conf := make(map[string]interface{})
	err := json.Unmarshal([]byte(contentText), &conf)
	if err != nil {
		return nil
	}
	field, ok := conf["field"]
	if !ok {
		return nil
	}
	array, b := utils.GetFieldValue(flowData, field.(string))
	if !b {
		return nil
	}
	arr, ok := array.([]interface{})
	if !ok {
		return nil
	}
	rowsConf, ok := conf["rows"]
	if !ok {
		return nil
	}
	rowsConfArray, ok := rowsConf.([]interface{})
	if !ok || len(rowsConfArray) < 1 {
		return nil
	}
	rowsConfRow := rowsConfArray[0]
	rowsConfMap, ok := rowsConfRow.(map[string]interface{})
	if !ok {
		return nil
	}
	rows := make([]interface{}, 0)
	for _, item := range arr {
		copyRow := make(map[string]interface{})
		if rowsConfMap != nil {
			for k, v := range rowsConfMap {
				copyRow[k] = v
			}
		}
		e, ok := item.(map[string]interface{})
		if !ok {
			continue
		}
		rowResult := utils.ReplaceVariablesInObject(copyRow, e)
		rows = append(rows, rowResult)
	}
	delete(conf, "field")
	conf["rows"] = rows
	return conf
}
