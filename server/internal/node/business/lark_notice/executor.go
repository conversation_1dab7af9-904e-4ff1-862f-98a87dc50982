package lark_notice

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/service/third_party/lark"
	"sec-flow-server/internal/utils"
	"strings"
	"time"
)

// LarkNoticeNode 用户信息补充节点
type LarkNoticeNode struct {
	config *base.NodeConfig
}

// NewLarkNoticeNode 创建用户信息补充节点
func NewLarkNoticeNode() *LarkNoticeNode {
	return &LarkNoticeNode{
		config: GetNodeConfig(),
	}
}

// GetID 获取节点ID
func (n *LarkNoticeNode) GetID() string {
	return n.config.ID
}

// GetConfig 获取节点配置
func (n *LarkNoticeNode) GetConfig() *base.NodeConfig {
	return n.config
}

// Validate 验证节点参数
func (n *LarkNoticeNode) Validate(params map[string]interface{}) error {
	// 验证必填字段
	receiverType, ok := params["receiverType"].(string)
	if !ok || receiverType == "" {
		return fmt.Errorf("receiverType is required")
	}

	receiverId, ok := params["receiverId"].(string)
	if !ok || receiverId == "" {
		return fmt.Errorf("receiverId is required")
	}

	title, ok := params["title"].(string)
	if !ok || title == "" {
		return fmt.Errorf("title is required")
	}

	// 解析noticeContents，支持字符串和数组两种格式
	var noticeContentsObjs []noticeContent
	if noticeContentsStr, ok := params["noticeContents"].(string); ok && noticeContentsStr != "" {
		// 字符串格式，需要JSON解析
		if err := json.Unmarshal([]byte(noticeContentsStr), &noticeContentsObjs); err != nil {
			return fmt.Errorf("noticeContents must be valid JSON array: %v", err)
		}
	} else if noticeContentsArray, ok := params["noticeContents"].([]interface{}); ok && len(noticeContentsArray) > 0 {
		// 数组格式，直接转换
		for _, item := range noticeContentsArray {
			if contentMap, ok := item.(map[string]interface{}); ok {
				contentType, _ := contentMap["contentType"].(string)
				contentText, _ := contentMap["contentText"].(string)
				noticeContentsObjs = append(noticeContentsObjs, noticeContent{
					ContentType: contentType,
					ContentText: contentText,
				})
			} else {
				return fmt.Errorf("noticeContents array contains invalid item")
			}
		}
	} else {
		return fmt.Errorf("noticeContents is required and must be a string or array")
	}

	if len(noticeContentsObjs) == 0 {
		return fmt.Errorf("at least one notice content is required")
	}

	for _, noticeContentObj := range noticeContentsObjs {
		t := noticeContentObj.ContentType
		if _, ok := larkMsgHandleMap[t]; !ok {
			return fmt.Errorf("not found notice content type: %s", t)
		}
	}

	// 验证字段类型
	//validTypes := map[string]bool{
	//	"email":    true,
	//	"user_id":  true,
	//	"username": true,
	//}
	//if !validTypes[sourceFieldType] {
	//	return fmt.Errorf("invalid sourceFieldType: %s", sourceFieldType)
	//}

	return nil
}

type noticeContent struct {
	ContentType string `json:"contentType"`
	ContentText string `json:"contentText"`
}

// Execute 执行节点逻辑
func (n *LarkNoticeNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行飞书消息通知节点: %s", input.NodeID)

	// 验证参数
	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 获取参数
	// 验证必填字段
	receiverType := input.Params["receiverType"].(string)
	receiverId := input.Params["receiverId"].(string)
	title := input.Params["title"].(string)
	
	// 解析noticeContents，支持字符串和数组两种格式
	var noticeContentsObjs []noticeContent
	var noticeContentsStr string
	if noticeContentsStr, ok := input.Params["noticeContents"].(string); ok && noticeContentsStr != "" {
		// 字符串格式，需要JSON解析
		if err := json.Unmarshal([]byte(noticeContentsStr), &noticeContentsObjs); err != nil {
			return &base.ExecutionOutput{
				Success: false,
				Error:   fmt.Sprintf("noticeContents must be valid JSON array: %v", err),
			}, nil
		}
	} else if noticeContentsArray, ok := input.Params["noticeContents"].([]interface{}); ok && len(noticeContentsArray) > 0 {
		// 数组格式，直接转换
		for _, item := range noticeContentsArray {
			if contentMap, ok := item.(map[string]interface{}); ok {
				contentType, _ := contentMap["contentType"].(string)
				contentText, _ := contentMap["contentText"].(string)
				noticeContentsObjs = append(noticeContentsObjs, noticeContent{
					ContentType: contentType,
					ContentText: contentText,
				})
			} else {
				return &base.ExecutionOutput{
					Success: false,
					Error:   "noticeContents array contains invalid item",
				}, nil
			}
		}
		// 为了日志显示，将数组转换为字符串
		if bytes, err := json.Marshal(noticeContentsArray); err == nil {
			noticeContentsStr = string(bytes)
		}
	} else {
		return &base.ExecutionOutput{
			Success: false,
			Error:   "noticeContents is required and must be a string or array",
		}, nil
	}

	log.Printf("参数 - receiverType: %s, receiverId: %s, title: %s, noticeContents: %s", receiverType, receiverId, title, noticeContentsStr)

	var receiveIdType string
	if strings.HasPrefix(receiverType, "group") {
		receiveIdType = "chat_id"
	} else {
		receiveIdType = "user_id"
	}
	if strings.HasSuffix(receiverType, "var") {
		receiverValue, found := utils.GetFieldValueFromSources(input.PrevOutput, input.FlowData, receiverId)
		if !found {
			return &base.ExecutionOutput{
				Success: false,
				Error:   fmt.Sprintf("receiverId '%s' not found in input data", receiverId),
			}, nil
		}
		receiverId = receiverValue.(string)
	}

	elements := make([]interface{}, 0)
	for _, noticeContentObj := range noticeContentsObjs {
		handleMap := larkMsgHandleMap[noticeContentObj.ContentType]
		e := handleMap.Handler(noticeContentObj.ContentText, input.FlowData)
		if e != nil {
			elements = append(elements, e)
		}
	}
	cardFormat := `
{
	"schema": "2.0",
	"config": {
		"update_multi": true,
		"style": {
			"text_size": {
				"normal_v2": {
					"default": "normal",
					"pc": "normal",
					"mobile": "heading"
				}
			}
		}
	},
	"body": {
		"direction": "vertical",
		"padding": "12px 12px 12px 12px",
		"elements": []
	},
	"header": {
        "title": {
            "tag": "plain_text",
            "content": ""
        },
        "subtitle": {
            "tag": "plain_text",
            "content": ""
        },
        "template": "blue",
        "padding": "12px 12px 12px 12px"
    }
}
`
	contentMap := make(map[string]interface{})
	_ = json.Unmarshal([]byte(cardFormat), &contentMap)
	contentMap["header"].(map[string]interface{})["title"].(map[string]interface{})["content"] = title
	contentMap["body"].(map[string]interface{})["elements"] = elements

	marshal, _ := json.Marshal(contentMap)
	content := string(marshal)

	var response map[string]interface{}
	err := lark.SendCardMessage(receiveIdType, receiverId, content, &response)
	bytes, _ := json.Marshal(response)
	resp := string(bytes)
	code, ok := response["code"]

	if err != nil || !ok || code.(float64) != 0 {
		return &base.ExecutionOutput{
			Success: false,
			Error:   fmt.Sprintf("lark send message error: %s", resp),
		}, nil
	}

	log.Printf("飞书消息发送完成，输出: %s", resp)

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true, // 继续执行后续节点
		Data:     map[string]interface{}{"request": content, "response": resp},
		FlowData: input.FlowData,
		Message:  fmt.Sprintf("飞书消息发送完成，输出: %s", resp),
		Metadata: map[string]interface{}{
			"nodeType":     "lark_notice",
			"receiverId":   receiverId,
			"receiverType": receiverType,
			"title":        title,
			"content":      content,
			"processedAt":  time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// Example 生成示例数据
func (n *LarkNoticeNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 记录数据变化
	changes := make([]base.DataChange, 0)
	return &base.ExampleOutput{
		FlowData:    input.FlowData,
		Description: fmt.Sprintf("发送消息不修改原数据"),
		Changes:     changes,
	}
}
