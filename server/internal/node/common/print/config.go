package print

import "sec-flow-server/internal/node/base"

// GetNodeConfig 返回print节点的配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "print",
		Name:        "日志打印",
		Category:    "common",
		Description: "在服务端日志中打印当前流程数据，用于调试和查看执行结果",
		Icon:        "📝",
		NodeType:    "rect",
		FormFields: []base.FormField{
			{
				Key:          "label",
				Label:        "打印标签",
				Type:         "input",
				Required:     false,
				Placeholder:  "可选的标签，用于标识这次打印",
				DefaultValue: "流程数据打印",
			},
			{
				Key:      "printFormat",
				Label:    "打印格式",
				Type:     "select",
				Required: true,
				Options: []base.FormFieldOption{
					{Label: "JSON格式", Value: "json"},
					{Label: "简洁格式", Value: "simple"},
					{Label: "详细格式", Value: "detailed"},
				},
				DefaultValue: "json",
			},
			{
				Key:          "includeExecuteInfo",
				Label:        "包含执行信息",
				Type:         "switch",
				Required:     false,
				DefaultValue: false,
			},
		},
		Style: base.NodeStyle{
			Fill:         "#f6ffed",
			Stroke:       "#52c41a",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}
}
