package print

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sec-flow-server/internal/node/base"
	"strings"
	"time"
)

// PrintNode print节点
type PrintNode struct {
	config *base.NodeConfig
}

// NewPrintNode 创建print节点
func NewPrintNode() *PrintNode {
	return &PrintNode{
		config: GetNodeConfig(),
	}
}

// GetID 获取节点ID
func (n *PrintNode) GetID() string {
	return n.config.ID
}

// GetConfig 获取节点配置
func (n *PrintNode) GetConfig() *base.NodeConfig {
	return n.config
}

// Validate 验证节点参数
func (n *PrintNode) Validate(params map[string]interface{}) error {
	// print节点的参数验证比较简单，主要检查格式是否正确
	if printFormat, ok := params["printFormat"]; ok {
		if format, ok := printFormat.(string); ok {
			switch format {
			case "json", "simple", "detailed":
				// 有效格式
			default:
				return fmt.Errorf("打印格式必须是 json、simple 或 detailed 之一")
			}
		}
	}
	return nil
}

// Execute 执行节点逻辑
func (n *PrintNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行日志打印节点: %s", input.NodeID)

	// 验证参数
	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 获取配置
	label := "流程数据打印"
	printFormat := "json"
	includeExecuteInfo := false

	if input.Params != nil {
		if l, ok := input.Params["label"].(string); ok && l != "" {
			label = l
		}
		if f, ok := input.Params["printFormat"].(string); ok {
			printFormat = f
		}
		if inc, ok := input.Params["includeExecuteInfo"].(bool); ok {
			includeExecuteInfo = inc
		}
	}

	log.Printf("参数 - label: %s, printFormat: %s, includeExecuteInfo: %v",
		label, printFormat, includeExecuteInfo)

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 准备要打印的数据
	printData := make(map[string]interface{})
	for k, v := range resultFlowData {
		// 根据配置决定是否包含executeInfo
		if k == "executeInfo" && !includeExecuteInfo {
			continue
		}
		printData[k] = v
	}

	// 生成时间戳
	timestamp := time.Now().Format("2006-01-02 15:04:05")

	// 根据格式打印数据
	switch printFormat {
	case "json":
		n.printJSON(label, timestamp, printData)
	case "simple":
		n.printSimple(label, timestamp, printData)
	case "detailed":
		n.printDetailed(label, timestamp, printData)
	default:
		n.printJSON(label, timestamp, printData)
	}

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true,
		Data:     map[string]interface{}{"message": fmt.Sprintf("已在服务端日志中打印流程数据: %s", label)},
		FlowData: resultFlowData,
		Message:  fmt.Sprintf("已在服务端日志中打印流程数据: %s", label),
	}, nil
}

// printJSON 以JSON格式打印数据
func (n *PrintNode) printJSON(label, timestamp string, data map[string]interface{}) {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		log.Printf("🖨️ [%s] %s - JSON序列化失败: %v", timestamp, label, err)
		return
	}

	log.Printf("🖨️ [%s] %s", timestamp, label)
	log.Printf("📄 JSON格式流程数据:")
	log.Printf("%s", string(jsonData))
	log.Printf("📄 数据打印完成 (%d 字节)", len(jsonData))
}

// printSimple 以简洁格式打印数据
func (n *PrintNode) printSimple(label, timestamp string, data map[string]interface{}) {
	log.Printf("🖨️ [%s] %s", timestamp, label)
	log.Printf("📄 简洁格式流程数据:")

	for key, value := range data {
		if key == "executeInfo" {
			log.Printf("  %s: [执行信息]", key)
			continue
		}

		// 简化值的显示
		var valueStr string
		switch v := value.(type) {
		case string:
			if len(v) > 50 {
				valueStr = v[:50] + "..."
			} else {
				valueStr = v
			}
		case map[string]interface{}:
			valueStr = fmt.Sprintf("[对象包含%d个字段]", len(v))
		case []interface{}:
			valueStr = fmt.Sprintf("[数组包含%d个元素]", len(v))
		default:
			valueStr = fmt.Sprintf("%v", v)
		}
		log.Printf("  %s: %s", key, valueStr)
	}
	log.Printf("📄 数据打印完成")
}

// printDetailed 以详细格式打印数据
func (n *PrintNode) printDetailed(label, timestamp string, data map[string]interface{}) {
	log.Printf("🖨️ [%s] %s", timestamp, label)
	log.Printf("📄 详细格式流程数据:")
	log.Printf("=" + strings.Repeat("=", 60))

	for key, value := range data {
		log.Printf("🔑 字段: %s", key)
		log.Printf("📝 类型: %T", value)

		switch v := value.(type) {
		case string:
			log.Printf("💬 值: %s", v)
		case map[string]interface{}:
			log.Printf("🗂️ 对象内容:")
			for subKey, subValue := range v {
				log.Printf("    %s: %v", subKey, subValue)
			}
		case []interface{}:
			log.Printf("📋 数组内容 (%d个元素):", len(v))
			for i, item := range v {
				log.Printf("    [%d]: %v", i, item)
			}
		default:
			log.Printf("💬 值: %v", v)
		}
		log.Printf("-" + strings.Repeat("-", 40))
	}
	log.Printf("=" + strings.Repeat("=", 60))
	log.Printf("📄 数据打印完成")
}

// Example 生成示例数据
func (n *PrintNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置
	label := "流程数据打印"
	printFormat := "json"

	if input.Config != nil {
		if l, ok := input.Config["label"].(string); ok && l != "" {
			label = l
		}
		if f, ok := input.Config["printFormat"].(string); ok {
			printFormat = f
		}
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       "print_log",
			Action:      "log",
			NewValue:    fmt.Sprintf("将打印流程数据: %s", label),
			Description: fmt.Sprintf("将在服务端日志中打印流程数据，标签: %s，格式: %s", label, printFormat),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Changes:     changes,
		Description: "在服务端日志中打印流程数据，用于调试和查看执行结果",
	}
}
