package cron_start

import (
	"context"
	"fmt"
	"log"
	"sec-flow-server/internal/node/base"
	"time"

	"github.com/robfig/cron/v3"
)

// CronStartNode Cron开始节点
type CronStartNode struct {
	config *base.NodeConfig
}

// NewCronStartNode 创建新的Cron开始节点
func NewCronStartNode() *CronStartNode {
	return &CronStartNode{
		config: GetNodeConfig(),
	}
}

// 实现 base.Node 接口
func (n *CronStartNode) GetConfig() *base.NodeConfig {
	return n.config
}

func (n *CronStartNode) GetID() string {
	return n.config.ID
}

func (n *CronStartNode) Validate(params map[string]interface{}) error {
	cronExpr, ok := params["cronExpression"].(string)
	if !ok || cronExpr == "" {
		return fmt.Errorf("缺少必需的Cron表达式配置")
	}

	return ValidateCronExpression(cronExpr)
}

func (n *CronStartNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	// 解析配置
	cronExpr, ok := input.Params["cronExpression"].(string)
	if !ok || cronExpr == "" {
		return nil, fmt.Errorf("缺少必需的Cron表达式配置")
	}

	description, _ := input.Params["description"].(string)

	// 固定使用中国时区
	timezone := "Asia/Shanghai"
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		return nil, fmt.Errorf("无法加载时区 %s: %v", timezone, err)
	}

	// 创建Cron解析器
	parser := cron.NewParser(cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow)

	// 解析Cron表达式
	schedule, err := parser.Parse(cronExpr)
	if err != nil {
		return nil, fmt.Errorf("无效的Cron表达式: %s, 错误: %v", cronExpr, err)
	}

	// 获取当前时间（优先使用前端传入的currentTime，否则使用系统当前时间）
	var now time.Time
	if input.FlowData != nil {
		if currentTimeStr, exists := input.FlowData["currentTime"]; exists {
			if timeStr, ok := currentTimeStr.(string); ok {
				// 尝试解析前端传入的时间
				if parsedTime, err := time.ParseInLocation("2006-01-02 15:04", timeStr, loc); err == nil {
					now = parsedTime
					log.Printf("使用前端传入的执行时间: %s", timeStr)
				} else {
					log.Printf("解析前端时间失败，使用系统当前时间: %v", err)
					now = time.Now().In(loc)
				}
			} else {
				now = time.Now().In(loc)
			}
		} else {
			now = time.Now().In(loc)
		}
	} else {
		now = time.Now().In(loc)
	}

	// 获取下一次执行时间
	nextTime := schedule.Next(now.Add(-time.Minute)) // 减去1分钟，确保当前时间也能匹配

	// 检查当前时间是否在执行时间窗口内（允许1分钟的误差）
	timeDiff := now.Sub(nextTime).Abs()
	isExecutionTime := timeDiff <= time.Minute

	// 格式化时间
	currentTimeStr := now.Format("2006-01-02 15:04:05")
	nextTimeStr := schedule.Next(now).Format("2006-01-02 15:04:05")
	currentDayStr := now.Format("2006-01-02")
	currentMonthStr := now.Format("2006-01")
	lastDayStr := now.AddDate(0, 0, -1).Format("2006-01-02")
	lastMonthStr := now.AddDate(0, -1, 0).Format("2006-01")

	// 创建节点输出数据（用于调试和日志）
	nodeData := map[string]interface{}{
		"isExecutionTime":   isExecutionTime,
		"currentTime":       currentTimeStr,
		"nextExecutionTime": nextTimeStr,
		"cronExpression":    cronExpr,
		"timezone":          timezone,
		"description":       description,
		"timeDifference":    timeDiff.String(),
	}

	// 初始化或更新流程共享数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		// 复制现有的流程数据
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 确保executeInfo字段存在
	if updatedFlowData["executeInfo"] == nil {
		updatedFlowData["executeInfo"] = make(map[string]interface{})
	}
	executeInfo := updatedFlowData["executeInfo"].(map[string]interface{})

	// 添加 Cron 节点的执行信息到执行信息中，而不是直接添加到流程数据
	executeInfo["cronTrigger"] = map[string]interface{}{
		"triggered":         isExecutionTime,
		"currentTime":       currentTimeStr,
		"nextExecutionTime": nextTimeStr,
		"cronExpression":    cronExpr,
		"timezone":          timezone,
	}

	executeInfo["timeInfo"] = map[string]interface{}{
		"currentTime":  currentTimeStr,
		"currentDay":   currentDayStr,
		"currentMonth": currentMonthStr,
		"lastDay":      lastDayStr,
		"lastMonth":    lastMonthStr,
	}

	var message string
	var shouldContinue bool

	// 如果不是执行时间，返回特殊状态
	if !isExecutionTime {
		message = fmt.Sprintf("当前时间 %s 不是执行时间，下次执行时间: %s", currentTimeStr, nextTimeStr)
		shouldContinue = false // 不是执行时间，不继续执行后续节点
		executeInfo["cronTrigger"].(map[string]interface{})["status"] = "waiting"
	} else {
		message = fmt.Sprintf("定时触发成功！当前时间 %s 匹配Cron表达式 %s", currentTimeStr, cronExpr)
		shouldContinue = true // 是执行时间，继续执行后续节点
		executeInfo["cronTrigger"].(map[string]interface{})["status"] = "triggered"
	}

	return &base.ExecutionOutput{
		Success:  true,
		Continue: shouldContinue,
		Data:     nodeData,
		FlowData: updatedFlowData,
		Message:  message,
		Metadata: map[string]interface{}{
			"nodeType":      "cron_start",
			"executionTime": time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// ValidateCronExpression 验证Cron表达式
func ValidateCronExpression(cronExpr string) error {
	parser := cron.NewParser(cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow)
	_, err := parser.Parse(cronExpr)
	return err
}

// GetNextExecutionTimes 获取接下来的几次执行时间
func GetNextExecutionTimes(cronExpr string, timezone string, count int) ([]string, error) {
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		loc = time.UTC
	}

	parser := cron.NewParser(cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow)
	schedule, err := parser.Parse(cronExpr)
	if err != nil {
		return nil, err
	}

	now := time.Now().In(loc)
	times := make([]string, 0, count)

	nextTime := now
	for i := 0; i < count; i++ {
		nextTime = schedule.Next(nextTime)
		times = append(times, nextTime.Format("2006-01-02 15:04:05"))
	}

	return times, nil
}

// Example 生成示例数据
func (n *CronStartNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置参数
	cronExpr := "0 9 * * *"     // 默认每天9点
	timezone := "Asia/Shanghai" // 固定使用中国时区

	if input.Config != nil {
		if ce, ok := input.Config["cronExpression"].(string); ok && ce != "" {
			cronExpr = ce
		}
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 确保executeInfo字段存在
	if resultFlowData["executeInfo"] == nil {
		resultFlowData["executeInfo"] = make(map[string]interface{})
	}
	executeInfo := resultFlowData["executeInfo"].(map[string]interface{})

	// 生成示例时间信息
	currentTime := "2025-01-04 09:00:00"
	nextTime := "2025-01-05 09:00:00"

	// 创建示例 Cron 触发信息
	cronTrigger := map[string]interface{}{
		"triggered":         true,
		"currentTime":       currentTime,
		"nextExecutionTime": nextTime,
		"cronExpression":    cronExpr,
		"timezone":          timezone,
		"status":            "triggered",
	}

	// 添加到执行信息中，而不是直接添加到流程数据
	executeInfo["cronTrigger"] = cronTrigger
	executeInfo["timeInfo"] = map[string]interface{}{
		"currentTime":  "2025-01-04 09:00:00",
		"currentDay":   "2025-01-04",
		"currentMonth": "2025-01",
		"lastDay":      "2025-01-03",
		"lastMonth":    "2024-12",
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       "executeInfo",
			Action:      "add",
			NewValue:    executeInfo,
			Description: fmt.Sprintf("定时触发器触发，Cron表达式: %s", cronExpr),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("定时触发器 (%s) 在指定时间触发，初始化流程数据", cronExpr),
		Changes:     changes,
	}
}
