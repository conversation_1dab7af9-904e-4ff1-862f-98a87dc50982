package cron_start

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取Cron开始节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "cron_start",
		Name:        "定时开始",
		Description: "基于Cron表达式的定时触发节点，用于启动流程执行",
		Category:    "common",
		NodeType:    "circle",
		Icon:        "clock-circle",
		FormFields: []base.FormField{
			{
				Key:         "cronExpression",
				Label:       "Cron表达式",
				Type:        "input",
				Required:    true,
				Placeholder: "例如: 0 9 * * 1-5 (工作日上午9点)",
				Validation: map[string]interface{}{
					"pattern": `^(\*|([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])|\*\/([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])) (\*|([0-9]|1[0-9]|2[0-3])|\*\/([0-9]|1[0-9]|2[0-3])) (\*|([1-9]|1[0-9]|2[0-9]|3[0-1])|\*\/([1-9]|1[0-9]|2[0-9]|3[0-1])) (\*|([1-9]|1[0-2])|\*\/([1-9]|1[0-2])) (\*|([0-6])|\*\/([0-6]))$`,
					"message": "请输入有效的5位Cron表达式，格式：分 时 日 月 周",
				},
			},

			{
				Key:         "description",
				Label:       "描述",
				Type:        "textarea",
				Required:    false,
				Placeholder: "描述这个定时任务的用途",
			},
		},
		Style: base.NodeStyle{
			Fill:         "#fff2f0",
			Stroke:       "#ff4d4f",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}
}
