package api_call

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取API调用节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "api-call",
		Name:        "API调用",
		Description: "调用外部API接口",
		Category:    "common",
		NodeType:    "rect",
		Icon:        "api",
		FormFields: []base.FormField{
			{
				Key:          "url",
				Label:        "API地址",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "请输入API地址，如: https://api.example.com/users",
			},
			{
				Key:          "method",
				Label:        "请求方法",
				Type:         "select",
				Required:     true,
				DefaultValue: "GET",
				Options: []base.FormFieldOption{
					{Label: "GET", Value: "GET"},
					{Label: "POST", Value: "POST"},
					{Label: "PUT", Value: "PUT"},
					{Label: "DELETE", Value: "DELETE"},
				},
			},
			{
				Key:          "headers",
				Label:        "请求头",
				Type:         "textarea",
				Required:     false,
				DefaultValue: "{}",
				Placeholder:  "请输入JSON格式的请求头，如: {\"Content-Type\": \"application/json\"}",
			},
			{
				Key:          "body",
				Label:        "请求体",
				Type:         "textarea",
				Required:     false,
				DefaultValue: "",
				Placeholder:  "请输入请求体数据（JSON格式）",
			},
			{
				Key:          "timeout",
				Label:        "超时时间(秒)",
				Type:         "input",
				Required:     false,
				DefaultValue: "30",
				Validation: map[string]interface{}{
					"type": "number",
					"min":  1,
					"max":  300,
				},
			},
			{
				Key:          "outputField",
				Label:        "输出字段",
				Type:         "input",
				Required:     true,
				DefaultValue: "apiResponse",
				Placeholder:  "请输入输出字段名",
			},
		},
		Style: base.NodeStyle{
			Fill:         "#f6ffed",
			Stroke:       "#52c41a",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}
}
