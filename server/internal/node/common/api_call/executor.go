package api_call

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"sec-flow-server/internal/node/base"
	"strconv"
	"strings"
	"time"
)

// APICallNode API调用节点
type APICallNode struct {
	config *base.NodeConfig
}

// NewAPICallNode 创建API调用节点
func NewAPICallNode() *APICallNode {
	return &APICallNode{
		config: GetNodeConfig(),
	}
}

// GetID 获取节点ID
func (n *APICallNode) GetID() string {
	return n.config.ID
}

// GetConfig 获取节点配置
func (n *APICallNode) GetConfig() *base.NodeConfig {
	return n.config
}

// Validate 验证节点参数
func (n *APICallNode) Validate(params map[string]interface{}) error {
	url, ok := params["url"].(string)
	if !ok || url == "" {
		return fmt.Errorf("url is required")
	}

	method, ok := params["method"].(string)
	if !ok || method == "" {
		return fmt.Errorf("method is required")
	}

	outputField, ok := params["outputField"].(string)
	if !ok || outputField == "" {
		return fmt.Errorf("outputField is required")
	}

	// 验证HTTP方法
	validMethods := map[string]bool{
		"GET":    true,
		"POST":   true,
		"PUT":    true,
		"DELETE": true,
	}
	if !validMethods[strings.ToUpper(method)] {
		return fmt.Errorf("invalid HTTP method: %s", method)
	}

	// 验证timeout参数（可选）
	if timeoutVal, ok := params["timeout"]; ok && timeoutVal != nil {
		var timeout int
		switch v := timeoutVal.(type) {
		case string:
			if v != "" {
				if t, err := strconv.Atoi(v); err != nil {
					return fmt.Errorf("'timeout' is not a valid number")
				} else {
					timeout = t
				}
			}
		case int:
			timeout = v
		case float64:
			timeout = int(v)
		default:
			return fmt.Errorf("'timeout' is not a valid number")
		}

		// 验证timeout范围
		if timeout < 1 || timeout > 300 {
			return fmt.Errorf("timeout must be between 1 and 300 seconds")
		}
	}

	return nil
}

// Execute 执行节点逻辑
func (n *APICallNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行API调用节点: %s", input.NodeID)

	// 验证参数
	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    err.Error(),
			FlowData: input.FlowData, // 保持流程数据不变
		}, nil
	}

	// 获取参数
	url := input.Params["url"].(string)
	method := strings.ToUpper(input.Params["method"].(string))
	outputField := input.Params["outputField"].(string)

	// 处理可选参数
	headers := make(map[string]string)
	if headersStr, ok := input.Params["headers"].(string); ok && headersStr != "" {
		if err := json.Unmarshal([]byte(headersStr), &headers); err != nil {
			log.Printf("警告: 无法解析请求头: %v", err)
		}
	}

	var body string
	if bodyStr, ok := input.Params["body"].(string); ok {
		body = bodyStr
	}

	timeout := 30
	if timeoutVal, ok := input.Params["timeout"]; ok && timeoutVal != nil {
		switch v := timeoutVal.(type) {
		case string:
			if v != "" {
				if t, err := strconv.Atoi(v); err == nil {
					timeout = t
				}
			}
		case int:
			timeout = v
		case float64:
			timeout = int(v)
		}
	}

	log.Printf("API调用参数 - URL: %s, Method: %s, Timeout: %ds", url, method, timeout)

	// 替换URL中的变量
	url = n.replaceVariables(url, input)

	// 替换请求体中的变量
	if body != "" {
		body = n.replaceVariables(body, input)
	}

	// 执行API调用
	response, err := n.makeAPICall(ctx, method, url, headers, body, timeout)
	if err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    err.Error(),
			FlowData: input.FlowData, // 保持流程数据不变
		}, nil
	}

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 将API响应添加到流程数据中
	updatedFlowData[outputField] = response

	// 构造节点输出数据（用于调试和日志）
	outputData := map[string]interface{}{
		"url":         url,
		"method":      method,
		"statusCode":  response["statusCode"],
		"outputField": outputField,
	}

	log.Printf("API调用成功，状态码: %d", response["statusCode"])

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true, // 继续执行后续节点
		Data:     outputData,
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("API调用成功，结果保存到字段 '%s'", outputField),
		Metadata: map[string]interface{}{
			"nodeType":   "api_call",
			"url":        url,
			"method":     method,
			"statusCode": response["statusCode"],
			"executedAt": time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// replaceVariables 替换字符串中的变量
func (n *APICallNode) replaceVariables(text string, input *base.ExecutionInput) string {
	result := text

	// 替换流程数据中的变量 {{flowData.key}}
	if input.FlowData != nil {
		for key, value := range input.FlowData {
			placeholder := fmt.Sprintf("{{flowData.%s}}", key)
			valueStr := fmt.Sprintf("%v", value)
			result = strings.ReplaceAll(result, placeholder, valueStr)
		}
	}

	// 替换上一个节点输出中的变量 {{prevOutput.key}}
	if input.PrevOutput != nil {
		for key, value := range input.PrevOutput {
			placeholder := fmt.Sprintf("{{prevOutput.%s}}", key)
			valueStr := fmt.Sprintf("%v", value)
			result = strings.ReplaceAll(result, placeholder, valueStr)
		}
	}

	return result
}

// makeAPICall 执行API调用
func (n *APICallNode) makeAPICall(ctx context.Context, method, url string, headers map[string]string,
	body string, timeoutSeconds int) (map[string]interface{}, error) {

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: time.Duration(timeoutSeconds) * time.Second,
	}

	// 创建请求
	var req *http.Request
	var err error

	if body != "" && (method == "POST" || method == "PUT") {
		req, err = http.NewRequestWithContext(ctx, method, url, bytes.NewBufferString(body))
	} else {
		req, err = http.NewRequestWithContext(ctx, method, url, nil)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求头
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// 如果没有设置Content-Type且有请求体，默认设置为application/json
	if body != "" && req.Header.Get("Content-Type") == "" {
		req.Header.Set("Content-Type", "application/json")
	}

	log.Printf("发送HTTP请求: %s %s", method, url)

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	// 构造响应数据
	response := map[string]interface{}{
		"statusCode": resp.StatusCode,
		"status":     resp.Status,
		"headers":    resp.Header,
		"body":       string(responseBody),
	}

	// 尝试解析JSON响应
	var jsonData interface{}
	if err := json.Unmarshal(responseBody, &jsonData); err == nil {
		response["json"] = jsonData
	}

	// 检查HTTP状态码
	if resp.StatusCode >= 400 {
		return response, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, resp.Status)
	}

	return response, nil
}

// Example 生成示例数据
func (n *APICallNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置参数
	url := "https://api.example.com/data"
	method := "GET"
	outputField := "apiResponse"

	if input.Config != nil {
		if u, ok := input.Config["url"].(string); ok && u != "" {
			url = u
		}
		if m, ok := input.Config["method"].(string); ok && m != "" {
			method = m
		}
		if of, ok := input.Config["outputField"].(string); ok && of != "" {
			outputField = of
		}
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 生成示例API响应
	exampleResponse := map[string]interface{}{
		"statusCode": 200,
		"status":     "200 OK",
		"headers": map[string]interface{}{
			"Content-Type": "application/json",
		},
		"body": `{"success": true, "data": [{"user_id": "1", "event": "登录事件", "timestamp": "2025-01-04T17:00:00Z"}]}`,
		"json": map[string]interface{}{
			"success": true,
			"data": []map[string]interface{}{
				{
					"user_id":   "1",
					"event":     "登录事件",
					"timestamp": "2025-01-04T17:00:00Z",
				},
			},
		},
	}

	// 添加到流程数据中
	resultFlowData[outputField] = exampleResponse

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			NewValue:    exampleResponse,
			Description: fmt.Sprintf("调用API %s %s，获取响应数据", method, url),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("调用外部API (%s %s)，将响应结果保存到 %s 字段", method, url, outputField),
		Changes:     changes,
	}
}
