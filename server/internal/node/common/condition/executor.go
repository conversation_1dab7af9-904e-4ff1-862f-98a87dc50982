package condition

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/utils"
	"strconv"
	"time"
)

// ConditionNode 条件判断节点
type ConditionNode struct {
	config *base.NodeConfig
}

// NewConditionNode 创建条件判断节点
func NewConditionNode() *ConditionNode {
	return &ConditionNode{
		config: GetNodeConfig(),
	}
}

// GetID 获取节点ID
func (n *ConditionNode) GetID() string {
	return n.config.ID
}

// GetConfig 获取节点配置
func (n *ConditionNode) GetConfig() *base.NodeConfig {
	return n.config
}

// Validate 验证节点参数
func (n *ConditionNode) Validate(params map[string]interface{}) error {
	// 验证逻辑操作符
	logicOperator, ok := params["logicOperator"].(string)
	if !ok || logicOperator == "" {
		return fmt.Errorf("logicOperator is required")
	}
	if logicOperator != "AND" && logicOperator != "OR" {
		return fmt.Errorf("invalid logicOperator: %s", logicOperator)
	}

	// 验证条件数组
	// 解析conditions，支持字符串和数组两种格式
	var conditionsSlice []map[string]interface{}
	if conditionsStr, ok := params["conditions"].(string); ok && conditionsStr != "" {
		// 字符串格式，需要JSON解析
		if err := json.Unmarshal([]byte(conditionsStr), &conditionsSlice); err != nil {
			return fmt.Errorf("conditions must be valid JSON array: %v", err)
		}
	} else if conditionsArray, ok := params["conditions"].([]interface{}); ok && len(conditionsArray) > 0 {
		// 数组格式，直接转换
		for _, item := range conditionsArray {
			if conditionMap, ok := item.(map[string]interface{}); ok {
				conditionsSlice = append(conditionsSlice, conditionMap)
			} else {
				return fmt.Errorf("conditions array contains invalid item")
			}
		}
	} else {
		return fmt.Errorf("conditions is required and must be a string or array")
	}

	// 这里可以添加更详细的条件验证逻辑
	return nil
}

// Execute 执行节点逻辑
func (n *ConditionNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行条件判断节点: %s", input.NodeID)

	// 验证参数
	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    err.Error(),
			FlowData: input.FlowData,
		}, nil
	}

	// 获取参数
	logicOperator := input.Params["logicOperator"].(string)

	log.Printf("条件判断参数 - logicOperator: %s", logicOperator)

	// 解析条件数组
	// 解析conditions，支持字符串和数组两种格式
	var conditionsSlice []map[string]interface{}
	if conditionsStr, ok := input.Params["conditions"].(string); ok && conditionsStr != "" {
		// 字符串格式，需要JSON解析
		if err := json.Unmarshal([]byte(conditionsStr), &conditionsSlice); err != nil {
			return &base.ExecutionOutput{
				Success:  false,
				Continue: false,
				Error:    fmt.Sprintf("failed to parse conditions JSON: %v", err),
				FlowData: input.FlowData,
			}, nil
		}
	} else if conditionsArray, ok := input.Params["conditions"].([]interface{}); ok && len(conditionsArray) > 0 {
		// 数组格式，直接转换
		for _, item := range conditionsArray {
			if conditionMap, ok := item.(map[string]interface{}); ok {
				conditionsSlice = append(conditionsSlice, conditionMap)
			} else {
				return &base.ExecutionOutput{
					Success:  false,
					Continue: false,
					Error:    "conditions array contains invalid item",
					FlowData: input.FlowData,
				}, nil
			}
		}
	} else {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    "conditions is required and must be a string or array",
			FlowData: input.FlowData,
		}, nil
	}

	if len(conditionsSlice) == 0 {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    "no conditions provided",
			FlowData: input.FlowData,
		}, nil
	}

	// 执行所有条件判断
	conditionResults := make([]map[string]interface{}, 0, len(conditionsSlice))
	var finalResult bool

	for i, condition := range conditionsSlice {
		field, ok := condition["field"].(string)
		if !ok || field == "" {
			return &base.ExecutionOutput{
				Success:  false,
				Continue: false,
				Error:    fmt.Sprintf("condition %d: field is required", i+1),
				FlowData: input.FlowData,
			}, nil
		}

		operator, ok := condition["operator"].(string)
		if !ok || operator == "" {
			return &base.ExecutionOutput{
				Success:  false,
				Continue: false,
				Error:    fmt.Sprintf("condition %d: operator is required", i+1),
				FlowData: input.FlowData,
			}, nil
		}

		compareValue := ""
		if val, ok := condition["value"].(string); ok {
			compareValue = val
		}

		// 执行单个条件判断
		conditionResult, err := utils.EvaluateCondition(input.FlowData, field, operator, compareValue)
		if err != nil {
			return &base.ExecutionOutput{
				Success:  false,
				Continue: false,
				Error:    fmt.Sprintf("condition %d: %v", i+1, err),
				FlowData: input.FlowData,
			}, nil
		}

		// 记录条件结果
		var fieldValue interface{}
		if val, ok := utils.GetFieldValue(input.FlowData, field); ok {
			fieldValue = val
		}
		conditionInfo := map[string]interface{}{
			"field":        field,
			"fieldValue":   fieldValue,
			"operator":     operator,
			"compareValue": compareValue,
			"result":       conditionResult,
		}
		conditionResults = append(conditionResults, conditionInfo)

		log.Printf("条件 %d 判断结果: %t (字段: %s, 值: %v %s %s)",
			i+1, conditionResult, field, fieldValue, operator, compareValue)

		// 根据逻辑操作符计算最终结果
		if i == 0 {
			finalResult = conditionResult
		} else {
			if logicOperator == "AND" {
				finalResult = finalResult && conditionResult
			} else { // OR
				finalResult = finalResult || conditionResult
			}
		}

		// 如果是AND逻辑且当前条件为false，可以提前结束
		if logicOperator == "AND" && !conditionResult {
			break
		}
		// 如果是OR逻辑且当前条件为true，可以提前结束
		if logicOperator == "OR" && conditionResult {
			break
		}
	}

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 将条件判断结果添加到流程数据中
	conditionSummary := map[string]interface{}{
		"finalResult":      finalResult,
		"logicOperator":    logicOperator,
		"conditionResults": conditionResults,
		"evaluatedAt":      time.Now().Format("2006-01-02 15:04:05"),
	}

	updatedFlowData["lastCondition"] = conditionSummary

	// 构造节点输出数据
	outputData := map[string]interface{}{
		"finalResult":      finalResult,
		"logicOperator":    logicOperator,
		"conditionResults": conditionResults,
	}

	log.Printf("条件判断完成，最终结果: %t (逻辑: %s, 条件数: %d)",
		finalResult, logicOperator, len(conditionResults))

	return &base.ExecutionOutput{
		Success:  true,
		Continue: finalResult, // 只有当条件为真时才继续执行
		Data:     outputData,
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("条件判断完成，结果: %t", finalResult),
		Metadata: map[string]interface{}{
			"nodeType":     "condition",
			"conditionMet": finalResult,
			"executedAt":   time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// Example 生成示例数据
func (n *ConditionNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置参数
	logicOperator := "AND"
	conditionsStr := `[{"field": "status", "operator": "equals", "value": "active"}]`

	if input.Config != nil {
		if lo, ok := input.Config["logicOperator"].(string); ok && lo != "" {
			logicOperator = lo
		}
		if cs, ok := input.Config["conditions"].(string); ok && cs != "" {
			conditionsStr = cs
		}
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 解析条件数组
	var conditions []map[string]interface{}
	if err := json.Unmarshal([]byte(conditionsStr), &conditions); err != nil {
		// 如果解析失败，使用默认条件
		conditions = []map[string]interface{}{
			{"field": "status", "operator": "equals", "value": "active"},
		}
	}

	// 为每个条件生成示例数据并执行判断
	conditionResults := make([]map[string]interface{}, 0, len(conditions))
	var finalResult bool

	for i, condition := range conditions {
		field, _ := condition["field"].(string)
		operator, _ := condition["operator"].(string)
		compareValue, _ := condition["value"].(string)

		// 生成示例字段值（如果不存在）
		var fieldValue interface{}
		if val, exists := resultFlowData[field]; exists {
			fieldValue = val
		} else {
			// 根据操作符生成合适的示例值
			switch operator {
			case "equals":
				fieldValue = compareValue // 让条件为真
			case "not_equals":
				fieldValue = "different_value"
			case "greater_than":
				if num, err := strconv.ParseFloat(compareValue, 64); err == nil {
					fieldValue = num + 10
				} else {
					fieldValue = "z" // 字符串比较
				}
			case "less_than":
				if num, err := strconv.ParseFloat(compareValue, 64); err == nil {
					fieldValue = num - 10
				} else {
					fieldValue = "a" // 字符串比较
				}
			case "contains":
				fieldValue = fmt.Sprintf("prefix_%s_suffix", compareValue)
			case "is_empty":
				fieldValue = ""
			case "is_not_empty":
				fieldValue = "some_value"
			default:
				fieldValue = compareValue
			}
			resultFlowData[field] = fieldValue
		}

		// 执行示例条件判断
		conditionResult, _ := utils.EvaluateCondition(resultFlowData, field, operator, compareValue)

		// 记录条件结果
		conditionInfo := map[string]interface{}{
			"field":        field,
			"fieldValue":   fieldValue,
			"operator":     operator,
			"compareValue": compareValue,
			"result":       conditionResult,
		}
		conditionResults = append(conditionResults, conditionInfo)

		// 根据逻辑操作符计算最终结果
		if i == 0 {
			finalResult = conditionResult
		} else {
			if logicOperator == "AND" {
				finalResult = finalResult && conditionResult
			} else { // OR
				finalResult = finalResult || conditionResult
			}
		}
	}

	// 添加条件判断结果
	conditionSummary := map[string]interface{}{
		"finalResult":      finalResult,
		"logicOperator":    logicOperator,
		"conditionResults": conditionResults,
		"evaluatedAt":      "2025-01-04T17:00:00Z",
	}

	// 添加条件判断详情
	resultFlowData["lastCondition"] = conditionSummary

	var changes []base.DataChange
	changes = append(changes, base.DataChange{
		Field:       "lastCondition",
		Action:      "add",
		NewValue:    conditionSummary,
		Description: fmt.Sprintf("执行条件判断，逻辑: %s，最终结果: %t", logicOperator, finalResult),
	})

	// 构建描述信息
	description := fmt.Sprintf("执行 %d 个条件的 %s 逻辑判断，最终结果: %t",
		len(conditionResults), logicOperator, finalResult)

	if len(conditionResults) > 0 {
		description += "。条件详情: "
		for i, condResult := range conditionResults {
			if i > 0 {
				description += ", "
			}
			description += fmt.Sprintf("%s(%v) %s %s = %t",
				condResult["field"], condResult["fieldValue"],
				condResult["operator"], condResult["compareValue"],
				condResult["result"])
		}
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: description,
		Changes:     changes,
	}
}
