package condition

import (
	"sec-flow-server/internal/constants"
	"sec-flow-server/internal/node/base"
)

// GetNodeConfig 获取条件判断节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "condition",
		Name:        "条件判断",
		Description: "根据多个条件进行逻辑判断，决定是否继续执行后续节点",
		Category:    "common",
		NodeType:    "diamond",
		Icon:        "question-circle",
		FormFields: []base.FormField{
			{
				Key:          "logicOperator",
				Label:        "条件逻辑关系",
				Type:         "select",
				Required:     true,
				DefaultValue: "AND",
				Options: []base.FormFieldOption{
					{Label: "所有条件都满足 (AND)", Value: "AND"},
					{Label: "任一条件满足 (OR)", Value: "OR"},
				},
			},
			{
				Key:          "conditions",
				Label:        "判断条件",
				Type:         "condition_array",
				Required:     true,
				DefaultValue: `[{"field": "status", "operator": "equals", "value": "active"}]`,
				Placeholder:  "请配置判断条件",
				Validation: map[string]interface{}{
					"operators": constants.GetAllOperators(),
				},
			},
		},
		Style: base.NodeStyle{
			Fill:        "#fff2f0",
			Stroke:      "#ff4d4f",
			StrokeWidth: 2,
			FontSize:    12,
			FontColor:   "#333",
		},
	}
}
