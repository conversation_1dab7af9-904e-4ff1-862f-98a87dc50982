package http_start

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sec-flow-server/internal/node/base"
	"time"
)

// HttpStartNode HTTP开始节点
type HttpStartNode struct {
	config *base.NodeConfig
}

// NewHttpStartNode 创建新的HTTP开始节点
func NewHttpStartNode() *HttpStartNode {
	return &HttpStartNode{
		config: GetNodeConfig(),
	}
}

// 实现 base.Node 接口
func (n *HttpStartNode) GetConfig() *base.NodeConfig {
	return n.config
}

func (n *HttpStartNode) GetID() string {
	return n.config.ID
}

func (n *HttpStartNode) Validate(params map[string]interface{}) error {
	key, ok := params["key"].(string)
	if !ok || key == "" {
		return fmt.Errorf("缺少必需的触发Key配置")
	}

	dataStructure, ok := params["dataStructure"].(string)
	if !ok || dataStructure == "" {
		return fmt.Errorf("缺少必需的数据结构配置")
	}

	// 验证数据结构是否为有效的JSON
	var jsonData interface{}
	if err := json.Unmarshal([]byte(dataStructure), &jsonData); err != nil {
		return fmt.Errorf("数据结构不是有效的JSON格式: %v", err)
	}

	return nil
}

func (n *HttpStartNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	// 解析配置
	key, ok := input.Params["key"].(string)
	if !ok || key == "" {
		return nil, fmt.Errorf("缺少必需的触发Key配置")
	}

	dataStructure, ok := input.Params["dataStructure"].(string)
	if !ok || dataStructure == "" {
		return nil, fmt.Errorf("缺少必需的数据结构配置")
	}

	description, _ := input.Params["description"].(string)

	// 验证数据结构
	var structureData interface{}
	if err := json.Unmarshal([]byte(dataStructure), &structureData); err != nil {
		return nil, fmt.Errorf("数据结构不是有效的JSON格式: %v", err)
	}

	// 获取HTTP请求数据（优先级：前端传入的requestData > FlowData中的httpRequest > 配置的数据结构）
	var requestData interface{}

	// 1. 优先使用前端传入的requestData（执行时的参数）
	if input.FlowData != nil {
		if frontendData, exists := input.FlowData["requestData"]; exists {
			requestData = frontendData
			log.Printf("使用前端传入的requestData: %v", requestData)
		} else if httpData, exists := input.FlowData["httpRequest"]; exists {
			requestData = httpData
			log.Printf("使用FlowData中的httpRequest: %v", requestData)
		}
	}

	// 2. 如果没有请求数据，使用配置的数据结构作为示例
	if requestData == nil {
		requestData = structureData
		log.Printf("使用配置的数据结构作为示例: %v", requestData)
	}

	// 格式化时间
	now := time.Now()
	currentTimeStr := now.Format("2006-01-02 15:04:05")
	currentDayStr := now.Format("2006-01-02")
	currentMonthStr := now.Format("2006-01")
	lastDayStr := now.AddDate(0, 0, -1).Format("2006-01-02")
	lastMonthStr := now.AddDate(0, -1, 0).Format("2006-01")

	// 创建节点输出数据
	nodeData := map[string]interface{}{
		"key":           key,
		"dataStructure": structureData,
		"requestData":   requestData,
		"description":   description,
		"triggeredAt":   currentTimeStr,
		"triggerType":   "http",
	}

	// 初始化或更新流程共享数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		// 复制现有的流程数据
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 确保executeInfo字段存在
	if updatedFlowData["executeInfo"] == nil {
		updatedFlowData["executeInfo"] = make(map[string]interface{})
	}
	executeInfo := updatedFlowData["executeInfo"].(map[string]interface{})

	// 添加 HTTP 触发信息到执行信息中，而不是直接添加到流程数据
	executeInfo["httpTrigger"] = map[string]interface{}{
		"key":         key,
		"triggeredAt": currentTimeStr,
		"requestData": requestData,
		"status":      "triggered",
	}

	executeInfo["timeInfo"] = map[string]interface{}{
		"currentTime":  currentTimeStr,
		"currentDay":   currentDayStr,
		"currentMonth": currentMonthStr,
		"lastDay":      lastDayStr,
		"lastMonth":    lastMonthStr,
	}

	// 将请求数据合并到流程数据中（这是业务数据）
	if requestDataMap, ok := requestData.(map[string]interface{}); ok {
		for k, v := range requestDataMap {
			updatedFlowData[k] = v
		}
	}

	message := fmt.Sprintf("HTTP触发成功！Key: %s, 触发时间: %s", key, currentTimeStr)

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true, // HTTP触发总是继续执行后续节点
		Data:     nodeData,
		FlowData: updatedFlowData,
		Message:  message,
		Metadata: map[string]interface{}{
			"nodeType":      "http_start",
			"executionTime": currentTimeStr,
			"triggerKey":    key,
		},
	}, nil
}

// ValidateKey 验证触发Key格式
func ValidateKey(key string) error {
	if key == "" {
		return fmt.Errorf("触发Key不能为空")
	}

	// 简单的格式验证：只允许字母、数字、下划线和短横线
	for _, char := range key {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '_' || char == '-') {
			return fmt.Errorf("触发Key只能包含字母、数字、下划线和短横线")
		}
	}

	return nil
}

// ValidateDataStructure 验证数据结构JSON格式
func ValidateDataStructure(dataStructure string) error {
	var jsonData interface{}
	return json.Unmarshal([]byte(dataStructure), &jsonData)
}

// Example 生成示例数据
func (n *HttpStartNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置参数
	key := "example-trigger"
	dataStructure := `{"userId": 123, "action": "process", "data": {"name": "示例用户", "email": "<EMAIL>"}}`

	if input.Config != nil {
		if k, ok := input.Config["key"].(string); ok && k != "" {
			key = k
		}
		if ds, ok := input.Config["dataStructure"].(string); ok && ds != "" {
			dataStructure = ds
		}
	}

	// 解析数据结构
	var structureData interface{}
	if err := json.Unmarshal([]byte(dataStructure), &structureData); err != nil {
		// 如果解析失败，使用默认结构
		structureData = map[string]interface{}{
			"error": "数据结构解析失败",
			"raw":   dataStructure,
		}
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 确保executeInfo字段存在
	if resultFlowData["executeInfo"] == nil {
		resultFlowData["executeInfo"] = make(map[string]interface{})
	}
	executeInfo := resultFlowData["executeInfo"].(map[string]interface{})

	// 生成示例时间信息
	currentTime := "2025-01-04 10:30:00"

	// 创建示例 HTTP 触发信息
	httpTrigger := map[string]interface{}{
		"key":         key,
		"triggeredAt": currentTime,
		"requestData": structureData,
		"status":      "triggered",
	}

	// 添加到执行信息中，而不是直接添加到流程数据
	executeInfo["httpTrigger"] = httpTrigger

	executeInfo["timeInfo"] = map[string]interface{}{
		"currentTime":  "2025-01-04 10:30:00",
		"currentDay":   "2025-01-04",
		"currentMonth": "2025-01",
		"lastDay":      "2025-01-03",
		"lastMonth":    "2024-12",
	}

	// 将请求数据合并到流程数据中（这是业务数据）
	if requestDataMap, ok := structureData.(map[string]interface{}); ok {
		for k, v := range requestDataMap {
			resultFlowData[k] = v
		}
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       "executeInfo",
			Action:      "add",
			NewValue:    executeInfo,
			Description: fmt.Sprintf("HTTP触发器触发，Key: %s", key),
		},
	}

	// 为数据结构中的每个字段记录变化
	if requestDataMap, ok := structureData.(map[string]interface{}); ok {
		for k, v := range requestDataMap {
			changes = append(changes, base.DataChange{
				Field:       k,
				Action:      "add",
				NewValue:    v,
				Description: fmt.Sprintf("从HTTP请求数据中添加字段: %s", k),
			})
		}
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("HTTP触发器 (%s) 接收到请求，初始化流程数据", key),
		Changes:     changes,
	}
}
