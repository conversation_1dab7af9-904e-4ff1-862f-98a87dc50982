package http_start

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取HTTP开始节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "http_start",
		Name:        "HTTP开始",
		Description: "基于HTTP请求触发的开始节点，通过POST请求启动流程执行",
		Category:    "common",
		NodeType:    "circle",
		Icon:        "api",
		FormFields: []base.FormField{
			{
				Key:         "key",
				Label:       "触发Key",
				Type:        "input",
				Required:    true,
				Placeholder: "例如: user-registration, data-sync",
				Validation: map[string]interface{}{
					"pattern": `^[a-zA-Z0-9_-]+$`,
					"message": "Key只能包含字母、数字、下划线和短横线",
				},
			},
			{
				Key:         "dataStructure",
				Label:       "数据结构",
				Type:        "textarea",
				Required:    true,
				Placeholder: `例如: {"userId": 123, "email": "<EMAIL>", "action": "register"}`,
				Validation: map[string]interface{}{
					"message": "请输入有效的JSON格式数据结构",
				},
			},
			{
				Key:         "description",
				Label:       "描述",
				Type:        "textarea",
				Required:    false,
				Placeholder: "描述这个HTTP触发器的用途和预期的请求数据",
			},
		},
		Style: base.NodeStyle{
			Fill:         "#f0f9ff",
			Stroke:       "#1890ff",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}
}
