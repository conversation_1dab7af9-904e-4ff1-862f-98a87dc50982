package array_filter

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sec-flow-server/internal/constants"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/utils"
	"strings"
	"time"
)

// ArrayFilterNode 数组过滤节点
type ArrayFilterNode struct {
	config *base.NodeConfig
}

// NewArrayFilterNode 创建数组过滤节点
func NewArrayFilterNode() *ArrayFilterNode {
	return &ArrayFilterNode{
		config: GetNodeConfig(),
	}
}

// GetID 获取节点ID
func (n *ArrayFilterNode) GetID() string {
	return n.config.ID
}

// GetConfig 获取节点配置
func (n *ArrayFilterNode) GetConfig() *base.NodeConfig {
	return n.config
}

// FilterCondition 过滤条件
type FilterCondition struct {
	Field    string `json:"field"`
	Operator string `json:"operator"`
	Value    string `json:"value"`
}

// Validate 验证节点参数
func (n *ArrayFilterNode) Validate(params map[string]interface{}) error {
	arrayField, ok := params["arrayField"].(string)
	if !ok || arrayField == "" {
		return fmt.Errorf("arrayField is required")
	}

	logicOperator, ok := params["logicOperator"].(string)
	if !ok || logicOperator == "" {
		return fmt.Errorf("logicOperator is required")
	}

	if logicOperator != "AND" && logicOperator != "OR" {
		return fmt.Errorf("logicOperator must be 'AND' or 'OR'")
	}

	// 解析conditions，支持字符串和数组两种格式
	var conditionsSlice []map[string]interface{}
	if conditionsStr, ok := params["conditions"].(string); ok && conditionsStr != "" {
		// 字符串格式，需要JSON解析
		if err := json.Unmarshal([]byte(conditionsStr), &conditionsSlice); err != nil {
			return fmt.Errorf("conditions must be valid JSON array: %v", err)
		}
	} else if conditionsArray, ok := params["conditions"].([]interface{}); ok && len(conditionsArray) > 0 {
		// 数组格式，直接转换
		for _, item := range conditionsArray {
			if conditionMap, ok := item.(map[string]interface{}); ok {
				conditionsSlice = append(conditionsSlice, conditionMap)
			} else {
				return fmt.Errorf("conditions array contains invalid item")
			}
		}
	} else {
		return fmt.Errorf("conditions is required and must be a string or array")
	}

	if len(conditionsSlice) == 0 {
		return fmt.Errorf("at least one condition is required")
	}

	// 验证每个条件
	for i, condition := range conditionsSlice {
		field, ok := condition["field"].(string)
		if !ok || field == "" {
			return fmt.Errorf("condition %d: field is required", i)
		}

		operator, ok := condition["operator"].(string)
		if !ok || operator == "" {
			return fmt.Errorf("condition %d: operator is required", i)
		}

		// 验证操作符（使用统一的操作符验证）
		if !constants.IsValidOperator(operator) {
			return fmt.Errorf("condition %d: invalid operator: %s", i, operator)
		}
	}

	return nil
}

// Execute 执行节点逻辑
func (n *ArrayFilterNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行数组过滤节点: %s", input.NodeID)

	// 验证参数
	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    err.Error(),
			FlowData: input.FlowData,
		}, nil
	}

	// 获取参数
	arrayField := input.Params["arrayField"].(string)
	logicOperator := input.Params["logicOperator"].(string)

	// 解析conditions，支持字符串和数组两种格式
	var conditionsSlice []map[string]interface{}
	if conditionsStr, ok := input.Params["conditions"].(string); ok && conditionsStr != "" {
		// 字符串格式，需要JSON解析
		if err := json.Unmarshal([]byte(conditionsStr), &conditionsSlice); err != nil {
			return &base.ExecutionOutput{
				Success:  false,
				Continue: false,
				Error:    fmt.Sprintf("failed to parse conditions JSON: %v", err),
				FlowData: input.FlowData,
			}, nil
		}
	} else if conditionsArray, ok := input.Params["conditions"].([]interface{}); ok && len(conditionsArray) > 0 {
		// 数组格式，直接转换
		for _, item := range conditionsArray {
			if conditionMap, ok := item.(map[string]interface{}); ok {
				conditionsSlice = append(conditionsSlice, conditionMap)
			} else {
				return &base.ExecutionOutput{
					Success:  false,
					Continue: false,
					Error:    "conditions array contains invalid item",
					FlowData: input.FlowData,
				}, nil
			}
		}
	} else {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    "conditions is required and must be a string or array",
			FlowData: input.FlowData,
		}, nil
	}

	// 转换为FilterCondition结构
	var conditions []FilterCondition
	for _, condition := range conditionsSlice {
		filterCondition := FilterCondition{
			Field:    condition["field"].(string),
			Operator: condition["operator"].(string),
		}
		if value, ok := condition["value"].(string); ok {
			filterCondition.Value = value
		}
		conditions = append(conditions, filterCondition)
	}

	log.Printf("数组过滤参数 - arrayField: %s, logicOperator: %s, conditions: %+v", arrayField, logicOperator, conditions)

	// 获取数组数据（支持JSON路径）
	var arrayData []interface{}
	var found bool

	// 从流程数据中查找数组，支持JSON路径表达式
	if input.FlowData != nil {
		if value, exists := utils.GetFieldValueFromSources(input.PrevOutput, input.FlowData, arrayField); exists {
			// 尝试多种数组类型转换
			if arr, ok := value.([]interface{}); ok {
				arrayData = arr
				found = true
			} else if arr, ok := value.([]map[string]interface{}); ok {
				// 转换 []map[string]interface{} 为 []interface{}
				arrayData = make([]interface{}, len(arr))
				for i, v := range arr {
					arrayData[i] = v
				}
				found = true
			} else {
				return &base.ExecutionOutput{
					Success:  false,
					Continue: false,
					Error:    fmt.Sprintf("field '%s' is not a supported array type, got %T", arrayField, value),
					FlowData: input.FlowData,
				}, nil
			}
		}
	}

	if !found {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("array field '%s' not found in flow data", arrayField),
			FlowData: input.FlowData,
		}, nil
	}

	// 执行过滤
	filteredData, err := n.filterArray(arrayData, conditions, logicOperator)
	if err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    err.Error(),
			FlowData: input.FlowData,
		}, nil
	}

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 更新数组字段（支持JSON路径）
	if !utils.SetFieldValue(updatedFlowData, arrayField, filteredData) {
		// 如果设置失败，回退到简单字段设置
		updatedFlowData[arrayField] = filteredData
	}

	// 构造节点输出数据（用于调试和日志）
	outputData := map[string]interface{}{
		"arrayField":    arrayField,
		"logicOperator": logicOperator,
		"conditions":    conditions,
		"originalCount": len(arrayData),
		"filteredCount": len(filteredData),
		"removedCount":  len(arrayData) - len(filteredData),
	}

	log.Printf("数组过滤完成，原始数量: %d，过滤后数量: %d", len(arrayData), len(filteredData))

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true,
		Data:     outputData,
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("数组过滤完成，从 %d 个元素过滤为 %d 个元素", len(arrayData), len(filteredData)),
		Metadata: map[string]interface{}{
			"nodeType":      "array_filter",
			"arrayField":    arrayField,
			"logicOperator": logicOperator,
			"originalCount": len(arrayData),
			"filteredCount": len(filteredData),
			"executedAt":    time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// filterArray 过滤数组
func (n *ArrayFilterNode) filterArray(arrayData []interface{}, conditions []FilterCondition, logicOperator string) ([]interface{}, error) {
	var filteredData []interface{}

	for _, item := range arrayData {
		// 检查每个数组元素是否满足条件
		match, err := n.evaluateConditions(item, conditions, logicOperator)
		if err != nil {
			return nil, err
		}

		if match {
			filteredData = append(filteredData, item)
		}
	}

	return filteredData, nil
}

// evaluateConditions 评估条件
func (n *ArrayFilterNode) evaluateConditions(item interface{}, conditions []FilterCondition, logicOperator string) (bool, error) {
	if len(conditions) == 0 {
		return true, nil
	}

	results := make([]bool, len(conditions))

	// 评估每个条件
	for i, condition := range conditions {
		result, err := utils.EvaluateCondition(item, condition.Field, condition.Operator, condition.Value)
		if err != nil {
			return false, err
		}
		results[i] = result
	}

	// 根据逻辑操作符组合结果
	if logicOperator == "AND" {
		for _, result := range results {
			if !result {
				return false, nil
			}
		}
		return true, nil
	} else { // OR
		for _, result := range results {
			if result {
				return true, nil
			}
		}
		return false, nil
	}
}

// Example 生成示例数据
func (n *ArrayFilterNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置参数
	arrayField := "items"
	logicOperator := "AND"
	conditions := []FilterCondition{
		{Field: "status", Operator: "equals", Value: "active"},
		{Field: "score", Operator: "greater_than", Value: "80"},
	}

	if input.Config != nil {
		if af, ok := input.Config["arrayField"].(string); ok && af != "" {
			arrayField = af
		}
		if lo, ok := input.Config["logicOperator"].(string); ok && lo != "" {
			logicOperator = lo
		}
		// 解析conditions，支持字符串和数组两种格式
		var conditionsSlice []map[string]interface{}
		if conditionsStr, ok := input.Config["conditions"].(string); ok && conditionsStr != "" {
			// 字符串格式，需要JSON解析
			if err := json.Unmarshal([]byte(conditionsStr), &conditionsSlice); err == nil {
				conditions = nil
				for _, condition := range conditionsSlice {
					filterCondition := FilterCondition{
						Field:    condition["field"].(string),
						Operator: condition["operator"].(string),
					}
					if value, ok := condition["value"].(string); ok {
						filterCondition.Value = value
					}
					conditions = append(conditions, filterCondition)
				}
			}
		} else if conditionsArray, ok := input.Config["conditions"].([]interface{}); ok && len(conditionsArray) > 0 {
			// 数组格式，直接转换
			conditions = nil
			for _, item := range conditionsArray {
				if conditionMap, ok := item.(map[string]interface{}); ok {
					filterCondition := FilterCondition{
						Field:    conditionMap["field"].(string),
						Operator: conditionMap["operator"].(string),
					}
					if value, ok := conditionMap["value"].(string); ok {
						filterCondition.Value = value
					}
					conditions = append(conditions, filterCondition)
				}
			}
		}
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 获取数组数据（优先使用传入的数据，支持JSON路径）
	var originalArray []interface{}
	if value, found := utils.GetFieldValueFromSources(nil, resultFlowData, arrayField); found {
		// 尝试多种数组类型转换
		if arr, ok := value.([]interface{}); ok {
			originalArray = arr
		} else if arr, ok := value.([]map[string]interface{}); ok {
			// 转换 []map[string]interface{} 为 []interface{}
			originalArray = make([]interface{}, len(arr))
			for i, v := range arr {
				originalArray[i] = v
			}
		}
	}

	// 如果没有找到指定的数组字段，生成示例数据
	if originalArray == nil {
		// 生成示例数据
		originalArray = []interface{}{
			map[string]interface{}{"id": 1, "name": "张三", "status": "active", "score": 85, "department": "技术部"},
			map[string]interface{}{"id": 2, "name": "李四", "status": "inactive", "score": 75, "department": "销售部"},
			map[string]interface{}{"id": 3, "name": "王五", "status": "active", "score": 92, "department": "技术部"},
			map[string]interface{}{"id": 4, "name": "赵六", "status": "active", "score": 78, "department": "市场部"},
		}
		resultFlowData[arrayField] = originalArray
	}

	// 执行示例过滤
	filteredArray, _ := n.filterArray(originalArray, conditions, logicOperator)

	// 更新数组字段（支持JSON路径）
	if !utils.SetFieldValue(resultFlowData, arrayField, filteredArray) {
		// 如果设置失败，回退到简单字段设置
		resultFlowData[arrayField] = filteredArray
	}

	// 构建变化描述
	var changes []base.DataChange
	changes = append(changes, base.DataChange{
		Field:       arrayField,
		Action:      "update",
		OldValue:    originalArray,
		NewValue:    filteredArray,
		Description: fmt.Sprintf("根据 %d 个条件（%s逻辑）过滤数组，从 %d 个元素过滤为 %d 个元素", len(conditions), logicOperator, len(originalArray), len(filteredArray)),
	})

	// 构建条件描述
	var conditionDescs []string
	for _, condition := range conditions {
		operatorDesc := map[string]string{
			"equals":        "等于",
			"not_equals":    "不等于",
			"greater_than":  "大于",
			"less_than":     "小于",
			"greater_equal": "大于等于",
			"less_equal":    "小于等于",
			"contains":      "包含",
			"not_contains":  "不包含",
			"is_empty":      "为空",
			"is_not_empty":  "不为空",
		}
		desc := fmt.Sprintf("%s %s %s", condition.Field, operatorDesc[condition.Operator], condition.Value)
		if condition.Operator == "is_empty" || condition.Operator == "is_not_empty" {
			desc = fmt.Sprintf("%s %s", condition.Field, operatorDesc[condition.Operator])
		}
		conditionDescs = append(conditionDescs, desc)
	}

	logicDesc := "且"
	if logicOperator == "OR" {
		logicDesc = "或"
	}

	description := fmt.Sprintf("过滤数组 %s，条件：%s（%s关系），从 %d 个元素过滤为 %d 个元素",
		arrayField, strings.Join(conditionDescs, logicDesc), logicOperator, len(originalArray), len(filteredArray))

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: description,
		Changes:     changes,
	}
}
