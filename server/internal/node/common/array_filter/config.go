package array_filter

import (
	"sec-flow-server/internal/constants"
	"sec-flow-server/internal/node/base"
)

// GetNodeConfig 获取数组过滤节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "array_filter",
		Name:        "数组过滤",
		Description: "根据条件过滤数组中的元素",
		Category:    "common",
		NodeType:    "rect",
		Icon:        "filter",
		FormFields: []base.FormField{
			{
				Key:          "arrayField",
				Label:        "数组字段名",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "请输入要过滤的数组字段名",
			},
			{
				Key:          "logicOperator",
				Label:        "条件逻辑关系",
				Type:         "select",
				Required:     true,
				DefaultValue: "AND",
				Options: []base.FormFieldOption{
					{Label: "所有条件都满足 (AND)", Value: "AND"},
					{Label: "任一条件满足 (OR)", Value: "OR"},
				},
			},
			{
				Key:          "conditions",
				Label:        "过滤条件",
				Type:         "condition_array",
				Required:     true,
				DefaultValue: `[{"field": "status", "operator": "equals", "value": "active"}]`,
				Placeholder:  "请配置过滤条件",
				Validation: map[string]interface{}{
					"operators": constants.GetAllOperators(),
				},
			},
		},
		Style: base.NodeStyle{
			Fill:         "#f0f9ff",
			Stroke:       "#0ea5e9",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}
}
