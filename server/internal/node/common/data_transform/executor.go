package data_transform

import (
	"context"
	"fmt"
	"log"
	"reflect"
	"regexp"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/utils"
	"strconv"
	"strings"
	"time"
)

// DataTransformNode 数据转换节点
type DataTransformNode struct {
	config *base.NodeConfig
}

// NewDataTransformNode 创建数据转换节点
func NewDataTransformNode() *DataTransformNode {
	return &DataTransformNode{
		config: GetNodeConfig(),
	}
}

// GetID 获取节点ID
func (n *DataTransformNode) GetID() string {
	return n.config.ID
}

// GetConfig 获取节点配置
func (n *DataTransformNode) GetConfig() *base.NodeConfig {
	return n.config
}

// Validate 验证节点参数
func (n *DataTransformNode) Validate(params map[string]interface{}) error {
	inputField, ok := params["inputField"].(string)
	if !ok || inputField == "" {
		return fmt.Errorf("inputField is required")
	}

	transformType, ok := params["transformType"].(string)
	if !ok || transformType == "" {
		return fmt.Errorf("transformType is required")
	}

	outputField, ok := params["outputField"].(string)
	if !ok || outputField == "" {
		return fmt.Errorf("outputField is required")
	}

	// 验证转换类型
	validTypes := map[string]bool{
		"string":    true,
		"number":    true,
		"date":      true,
		"boolean":   true,
		"uppercase": true,
		"lowercase": true,
		"join":      true,
		"format":    true,
	}
	if !validTypes[transformType] {
		return fmt.Errorf("invalid transformType: %s", transformType)
	}
	if transformType == "join" || transformType == "format" {
		transformSupport, ok := params["transformSupport"].(string)
		if !ok || transformSupport == "" {
			return fmt.Errorf("transformSupport is required")
		}
	}

	return nil
}

// Execute 执行节点逻辑
func (n *DataTransformNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行数据转换节点: %s", input.NodeID)

	// 验证参数
	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    err.Error(),
			FlowData: input.FlowData,
		}, nil
	}

	// 获取参数
	inputField := input.Params["inputField"].(string)
	transformType := input.Params["transformType"].(string)
	outputField := input.Params["outputField"].(string)
	var transformSupport string
	transformSupportParam, ok := input.Params["transformSupport"]
	if ok {
		transformSupport = transformSupportParam.(string)
		unquote, err := strconv.Unquote("\"" + transformSupport + "\"")
		if err == nil {
			transformSupport = unquote
		}
	}

	log.Printf("参数 - inputField: %s, transformType: %s, outputField: %s",
		inputField, transformType, outputField)

	// 检查是否是数组遍历操作
	if strings.Contains(inputField, "[]") {
		return n.executeArrayTransform(input, inputField, transformType, outputField, transformSupport)
	}

	// 检查是否是数组索引操作 (如: datas[0].age)
	if strings.Contains(inputField, "[") && strings.Contains(inputField, "]") && !strings.Contains(inputField, "[]") {
		return n.executeIndexTransform(input, inputField, transformType, outputField, transformSupport)
	}

	// 获取输入值 (支持JSON路径表达式)
	inputValue, found := utils.GetFieldValueFromSources(input.PrevOutput, input.FlowData, inputField)
	if !found {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("input field '%s' not found in input data", inputField),
			FlowData: input.FlowData,
		}, nil
	}

	// 执行数据转换
	transformedValue, err := n.transformData(inputValue, transformType, transformSupport)
	if err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    err.Error(),
			FlowData: input.FlowData,
		}, nil
	}

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 将转换后的数据添加到流程数据中 (支持JSON路径表达式)
	if !utils.SetFieldValue(updatedFlowData, outputField, transformedValue) {
		// 如果设置失败，回退到简单字段设置
		updatedFlowData[outputField] = transformedValue
	}

	// 构造节点输出数据（用于调试和日志）
	outputData := map[string]interface{}{
		"inputField":       inputField,
		"transformType":    transformType,
		"outputField":      outputField,
		"originalValue":    inputValue,
		"transformedValue": transformedValue,
	}

	log.Printf("数据转换完成，输入: %v -> 输出: %v", inputValue, transformedValue)

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true,
		Data:     outputData,
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("成功将 '%s' 转换为 %s 类型并输出到 '%s'", inputField, transformType, outputField),
		Metadata: map[string]interface{}{
			"nodeType":      "data_transform",
			"transformType": transformType,
			"executedAt":    time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// transformData 执行数据转换
func (n *DataTransformNode) transformData(value interface{}, transformType string, transformSupport string) (interface{}, error) {
	switch transformType {
	case "string":
		return fmt.Sprintf("%v", value), nil

	case "number":
		switch v := value.(type) {
		case string:
			if num, err := strconv.ParseFloat(v, 64); err == nil {
				return num, nil
			}
			return nil, fmt.Errorf("cannot convert '%s' to number", v)
		case int:
			return float64(v), nil
		case int64:
			return float64(v), nil
		case float32:
			return float64(v), nil
		case float64:
			return v, nil
		default:
			return nil, fmt.Errorf("cannot convert %T to number", value)
		}

	case "date":
		switch v := value.(type) {
		case string:
			// 尝试解析常见的日期格式
			formats := []string{
				"2006-01-02",
				"2006-01-02 15:04:05",
				"2006/01/02",
				"01/02/2006",
				"2006-01-02T15:04:05Z",
			}
			for _, format := range formats {
				if t, err := time.Parse(format, v); err == nil {
					return t.Format("2006-01-02T15:04:05Z"), nil
				}
			}
			return nil, fmt.Errorf("cannot parse date from '%s'", v)
		default:
			return nil, fmt.Errorf("cannot convert %T to date", value)
		}

	case "boolean":
		switch v := value.(type) {
		case bool:
			return v, nil
		case string:
			lower := strings.ToLower(v)
			if lower == "true" || lower == "1" || lower == "yes" || lower == "on" {
				return true, nil
			}
			if lower == "false" || lower == "0" || lower == "no" || lower == "off" {
				return false, nil
			}
			return nil, fmt.Errorf("cannot convert '%s' to boolean", v)
		case int, int64:
			return v != 0, nil
		case float32, float64:
			return v != 0, nil
		default:
			return nil, fmt.Errorf("cannot convert %T to boolean", value)
		}

	case "uppercase":
		str := fmt.Sprintf("%v", value)
		return strings.ToUpper(str), nil

	case "lowercase":
		str := fmt.Sprintf("%v", value)
		return strings.ToLower(str), nil

	case "join":
		return n.joinArray(value, transformSupport)

	case "format":
		formatValue, ok := value.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("cannot convert %T to map[string]interface{}", value)
		}
		return utils.ReplaceVariablesInText(transformSupport, formatValue), nil

	default:
		return nil, fmt.Errorf("unsupported transform type: %s", transformType)
	}
}

// Example 生成示例数据
func (n *DataTransformNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置参数
	inputField := "data"
	outputField := "transformedData"
	transformType := "string"

	if input.Config != nil {
		if inf, ok := input.Config["inputField"].(string); ok && inf != "" {
			inputField = inf
		}
		if of, ok := input.Config["outputField"].(string); ok && of != "" {
			outputField = of
		}
		if tt, ok := input.Config["transformType"].(string); ok && tt != "" {
			transformType = tt
		}
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 生成示例输入值和转换结果
	var exampleInput interface{}
	var exampleOutput interface{}
	var description string

	// 根据转换类型生成不同的示例
	switch transformType {
	case "string":
		exampleInput = 12345
		exampleOutput = "12345"
		description = "将数字转换为字符串"
	case "number":
		exampleInput = "123.45"
		exampleOutput = 123.45
		description = "将字符串转换为数字"
	case "date":
		exampleInput = "2025-01-04"
		exampleOutput = "2025-01-04T00:00:00Z"
		description = "将日期字符串转换为标准格式"
	case "boolean":
		exampleInput = "true"
		exampleOutput = true
		description = "将字符串转换为布尔值"
	case "uppercase":
		exampleInput = "hello world"
		exampleOutput = "HELLO WORLD"
		description = "将字符串转换为大写"
	case "lowercase":
		exampleInput = "HELLO WORLD"
		exampleOutput = "hello world"
		description = "将字符串转换为小写"
	case "join":
		exampleInput = []interface{}{"apple", "banana", "orange"}
		exampleOutput = "apple,banana,orange"
		description = "将数组元素拼接为字符串"
	case "format":
		exampleInput = map[string]interface{}{"name": "张三", "age": 18}
		exampleOutput = "张三的年龄是18岁"
		description = "将对象数据格式化为字符串"
	default:
		exampleInput = "sample data"
		exampleOutput = "sample data"
		description = "数据转换"
	}

	// 检查是否是数组遍历操作
	if strings.Contains(inputField, "[]") {
		return n.generateArrayExample(input, inputField, outputField, transformType, description, resultFlowData)
	}

	// 设置示例输入值到流程数据中（如果不存在）
	if _, found := utils.GetFieldValue(resultFlowData, inputField); !found {
		if !utils.SetFieldValue(resultFlowData, inputField, exampleInput) {
			// 如果设置失败，回退到简单字段设置
			resultFlowData[inputField] = exampleInput
		}
	}

	// 添加转换后的数据 (支持JSON路径表达式)
	if !utils.SetFieldValue(resultFlowData, outputField, exampleOutput) {
		// 如果设置失败，回退到简单字段设置
		resultFlowData[outputField] = exampleOutput
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			OldValue:    resultFlowData[inputField],
			NewValue:    exampleOutput,
			Description: fmt.Sprintf("将 %s 字段的值从 %v 转换为 %s 类型: %v", inputField, resultFlowData[inputField], transformType, exampleOutput),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("从 %s 字段读取数据，%s，结果保存到 %s 字段", inputField, description, outputField),
		Changes:     changes,
	}
}

// executeArrayTransform 执行数组遍历转换
func (n *DataTransformNode) executeArrayTransform(input *base.ExecutionInput, inputField, transformType, outputField, transformSupport string) (*base.ExecutionOutput, error) {
	log.Printf("🔄 执行数组转换: %s -> %s (类型: %s)", inputField, outputField, transformType)

	// 解析输入字段路径：datas[].age → arrayPath="datas", fieldName="age"
	parts := strings.Split(inputField, "[]")
	if len(parts) != 2 {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("invalid array field path: %s", inputField),
			FlowData: input.FlowData,
		}, nil
	}

	arrayPath := strings.TrimSuffix(parts[0], ".")
	fieldName := strings.TrimPrefix(parts[1], ".")

	// 获取数组对象
	arrayObj, found := utils.GetFieldValueFromSources(input.PrevOutput, input.FlowData, arrayPath)
	if !found {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("array field '%s' not found in input data", arrayPath),
			FlowData: input.FlowData,
		}, nil
	}

	// 确保是数组类型
	arrayValue := reflect.ValueOf(arrayObj)
	if arrayValue.Kind() != reflect.Slice && arrayValue.Kind() != reflect.Array {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("field '%s' is not an array", arrayPath),
			FlowData: input.FlowData,
		}, nil
	}

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 遍历数组，为每个元素进行转换
	for i := 0; i < arrayValue.Len(); i++ {
		element := arrayValue.Index(i).Interface()

		// 确保元素是map类型
		if elementMap, ok := element.(map[string]interface{}); ok {
			// 从元素中获取输入字段的值
			var inputValue interface{}
			if fieldName == "" {
				// 如果没有字段名，使用整个元素
				inputValue = element
			} else {
				// 获取指定字段的值
				if val, exists := elementMap[fieldName]; exists {
					inputValue = val
				} else {
					log.Printf("⚠️ 第%d个元素中没有找到字段 '%s'", i, fieldName)
					continue
				}
			}

			// 执行数据转换
			transformedValue, err := n.transformData(inputValue, transformType, transformSupport)
			if err != nil {
				log.Printf("❌ 转换第%d个元素失败: %v", i, err)
				continue
			}

			// 解析输出字段路径并设置值
			outputParts := strings.Split(outputField, "[]")
			if len(outputParts) == 2 {
				outputFieldName := strings.TrimPrefix(outputParts[1], ".")
				if outputFieldName != "" {
					elementMap[outputFieldName] = transformedValue
				}
			}

			log.Printf("✅ 第%d个元素转换完成: %v -> %v", i, inputValue, transformedValue)
		}
	}

	log.Printf("✅ 数组转换完成: %s -> %s", inputField, outputField)

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true,
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("成功对数组 %s 中每个元素的 %s 字段进行 %s 转换", arrayPath, fieldName, transformType),
	}, nil
}

// executeIndexTransform 执行数组索引转换 (如: datas[0].age -> datas[0].ageStr)
func (n *DataTransformNode) executeIndexTransform(input *base.ExecutionInput, inputField, transformType, outputField, transformSupport string) (*base.ExecutionOutput, error) {
	log.Printf("🔄 执行数组索引转换: %s -> %s (类型: %s)", inputField, outputField, transformType)

	// 获取输入值
	inputValue, found := utils.GetFieldValueFromSources(input.PrevOutput, input.FlowData, inputField)
	if !found {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("input field '%s' not found in input data", inputField),
			FlowData: input.FlowData,
		}, nil
	}

	// 执行数据转换
	transformedValue, err := n.transformData(inputValue, transformType, transformSupport)
	if err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    err.Error(),
			FlowData: input.FlowData,
		}, nil
	}

	// 复制并更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}

	// 设置输出值到指定的数组索引位置
	if !n.setIndexFieldValue(updatedFlowData, outputField, transformedValue) {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("failed to set indexed field '%s'", outputField),
			FlowData: input.FlowData,
		}, nil
	}

	log.Printf("✅ 数组索引转换完成: %v -> %v", inputValue, transformedValue)

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true,
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("成功将 '%s' 转换为 %s 类型并输出到 '%s'", inputField, transformType, outputField),
	}, nil
}

// setIndexFieldValue 设置数组索引字段的值 (如: datas[0].ageStr)
func (n *DataTransformNode) setIndexFieldValue(obj map[string]interface{}, fieldPath string, value interface{}) bool {
	// 使用正则表达式解析路径：datas[0].ageStr
	re := regexp.MustCompile(`^([^[]+)\[(\d+)\]\.(.+)$`)
	matches := re.FindStringSubmatch(fieldPath)
	if len(matches) != 4 {
		// 如果不匹配索引模式，尝试使用普通设值
		return utils.SetFieldValue(obj, fieldPath, value)
	}

	arrayPath := matches[1]
	indexStr := matches[2]
	fieldName := matches[3]

	// 解析索引
	index, err := strconv.Atoi(indexStr)
	if err != nil {
		return false
	}

	// 获取数组对象
	arrayObj, found := utils.GetFieldValue(obj, arrayPath)
	if !found {
		return false
	}

	// 确保是数组类型
	arrayValue := reflect.ValueOf(arrayObj)
	if arrayValue.Kind() != reflect.Slice && arrayValue.Kind() != reflect.Array {
		return false
	}

	// 检查索引范围
	if index < 0 || index >= arrayValue.Len() {
		return false
	}

	// 获取数组元素
	element := arrayValue.Index(index).Interface()

	// 确保元素是map类型
	if elementMap, ok := element.(map[string]interface{}); ok {
		// 设置字段值
		elementMap[fieldName] = value
		return true
	}

	return false
}

// generateArrayExample 生成数组遍历转换的示例
func (n *DataTransformNode) generateArrayExample(input *base.ExampleInput, inputField, outputField, transformType, description string, resultFlowData map[string]interface{}) *base.ExampleOutput {
	// 解析数组路径：datas[].age → arrayPath="datas", fieldName="age"
	parts := strings.Split(inputField, "[]")
	if len(parts) != 2 {
		// 如果解析失败，回退到普通示例
		return n.generateNormalExample(input, inputField, outputField, transformType, description, resultFlowData)
	}

	arrayPath := strings.TrimSuffix(parts[0], ".")
	fieldName := strings.TrimPrefix(parts[1], ".")

	// 生成示例数组数据
	var exampleArray []interface{}
	var exampleTransformedArray []interface{}

	// 根据转换类型生成不同的示例数组
	switch transformType {
	case "string":
		exampleArray = []interface{}{
			map[string]interface{}{fieldName: 11, "name": "Alice"},
			map[string]interface{}{fieldName: 12, "name": "Bob"},
		}
		exampleTransformedArray = []interface{}{
			map[string]interface{}{fieldName: 11, "name": "Alice", strings.TrimPrefix(outputField, arrayPath+"[]."): "11"},
			map[string]interface{}{fieldName: 12, "name": "Bob", strings.TrimPrefix(outputField, arrayPath+"[]."): "12"},
		}
	case "number":
		exampleArray = []interface{}{
			map[string]interface{}{fieldName: "123", "name": "Alice"},
			map[string]interface{}{fieldName: "456", "name": "Bob"},
		}
		exampleTransformedArray = []interface{}{
			map[string]interface{}{fieldName: "123", "name": "Alice", strings.TrimPrefix(outputField, arrayPath+"[]."): 123},
			map[string]interface{}{fieldName: "456", "name": "Bob", strings.TrimPrefix(outputField, arrayPath+"[]."): 456},
		}
	case "uppercase":
		exampleArray = []interface{}{
			map[string]interface{}{fieldName: "hello", "name": "Alice"},
			map[string]interface{}{fieldName: "world", "name": "Bob"},
		}
		exampleTransformedArray = []interface{}{
			map[string]interface{}{fieldName: "hello", "name": "Alice", strings.TrimPrefix(outputField, arrayPath+"[]."): "HELLO"},
			map[string]interface{}{fieldName: "world", "name": "Bob", strings.TrimPrefix(outputField, arrayPath+"[]."): "WORLD"},
		}
	default:
		exampleArray = []interface{}{
			map[string]interface{}{fieldName: "data1", "name": "Alice"},
			map[string]interface{}{fieldName: "data2", "name": "Bob"},
		}
		exampleTransformedArray = []interface{}{
			map[string]interface{}{fieldName: "data1", "name": "Alice", strings.TrimPrefix(outputField, arrayPath+"[]."): "data1"},
			map[string]interface{}{fieldName: "data2", "name": "Bob", strings.TrimPrefix(outputField, arrayPath+"[]."): "data2"},
		}
	}

	// 设置示例数组到流程数据中（如果不存在）
	if _, found := utils.GetFieldValue(resultFlowData, arrayPath); !found {
		if !utils.SetFieldValue(resultFlowData, arrayPath, exampleArray) {
			resultFlowData[arrayPath] = exampleArray
		}
	}

	// 设置转换后的数组数据
	if !utils.SetFieldValue(resultFlowData, arrayPath, exampleTransformedArray) {
		resultFlowData[arrayPath] = exampleTransformedArray
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			OldValue:    nil,
			NewValue:    "批量转换结果",
			Description: fmt.Sprintf("对数组 %s 中每个元素的 %s 字段进行 %s 转换，结果保存到 %s", arrayPath, fieldName, transformType, strings.TrimPrefix(outputField, arrayPath+"[].")),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("批量处理：从数组 %s 中每个元素读取 %s 字段，%s，结果保存到每个元素的 %s 字段", arrayPath, fieldName, description, strings.TrimPrefix(outputField, arrayPath+"[].")),
		Changes:     changes,
	}
}

// generateNormalExample 生成普通转换的示例（从原Example方法提取）
func (n *DataTransformNode) generateNormalExample(input *base.ExampleInput, inputField, outputField, transformType, description string, resultFlowData map[string]interface{}) *base.ExampleOutput {
	// 生成示例输入值和转换结果
	var exampleInput interface{}
	var exampleOutput interface{}

	// 根据转换类型生成不同的示例
	switch transformType {
	case "string":
		exampleInput = 12345
		exampleOutput = "12345"
	case "number":
		exampleInput = "123.45"
		exampleOutput = 123.45
	case "date":
		exampleInput = "2025-01-04"
		exampleOutput = "2025-01-04T00:00:00Z"
	case "boolean":
		exampleInput = "true"
		exampleOutput = true
	case "uppercase":
		exampleInput = "hello world"
		exampleOutput = "HELLO WORLD"
	case "lowercase":
		exampleInput = "HELLO WORLD"
		exampleOutput = "hello world"
	case "join":
		exampleInput = []interface{}{"apple", "banana", "orange"}
		exampleOutput = "apple,banana,orange"
	case "format":
		exampleInput = []interface{}{"apple", "banana", "orange"}
		exampleOutput = "apple,banana,orange"
	default:
		exampleInput = "sample data"
		exampleOutput = "sample data"
	}

	// 设置示例输入值到流程数据中（如果不存在）
	if _, found := utils.GetFieldValue(resultFlowData, inputField); !found {
		if !utils.SetFieldValue(resultFlowData, inputField, exampleInput) {
			resultFlowData[inputField] = exampleInput
		}
	}

	// 添加转换后的数据
	if !utils.SetFieldValue(resultFlowData, outputField, exampleOutput) {
		resultFlowData[outputField] = exampleOutput
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			OldValue:    resultFlowData[inputField],
			NewValue:    exampleOutput,
			Description: fmt.Sprintf("将 %s 字段的值从 %v 转换为 %s 类型: %v", inputField, resultFlowData[inputField], transformType, exampleOutput),
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: fmt.Sprintf("从 %s 字段读取数据，%s，结果保存到 %s 字段", inputField, description, outputField),
		Changes:     changes,
	}
}

// joinArray 将数组元素转换为字符串并用指定分隔符拼接
func (n *DataTransformNode) joinArray(value interface{}, separator string) (interface{}, error) {
	// 检查输入是否为数组类型
	switch v := value.(type) {
	case []interface{}:
		// 将每个元素转换为字符串
		var strElements []string
		for _, element := range v {
			strElements = append(strElements, fmt.Sprintf("%v", element))
		}
		return strings.Join(strElements, separator), nil

	case []string:
		// 如果已经是字符串数组，直接拼接
		return strings.Join(v, separator), nil

	case []int:
		// 处理整数数组
		var strElements []string
		for _, element := range v {
			strElements = append(strElements, fmt.Sprintf("%d", element))
		}
		return strings.Join(strElements, separator), nil

	case []float64:
		// 处理浮点数数组
		var strElements []string
		for _, element := range v {
			strElements = append(strElements, fmt.Sprintf("%g", element))
		}
		return strings.Join(strElements, separator), nil

	default:
		// 如果不是数组类型，尝试转换为字符串
		return fmt.Sprintf("%v", value), nil
	}
}
