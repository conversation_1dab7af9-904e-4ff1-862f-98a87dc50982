package data_transform

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取数据转换节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "data-transform",
		Name:        "数据转换",
		Description: "对数据进行格式转换和处理",
		Category:    "common",
		NodeType:    "rect",
		Icon:        "swap",
		FormFields: []base.FormField{
			{
				Key:          "inputField",
				Label:        "输入字段",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "支持JSON路径，如: data.user.name, arr[0].id",
			},
			{
				Key:          "transformType",
				Label:        "转换类型",
				Type:         "select",
				Required:     true,
				DefaultValue: "string",
				Options: []base.FormFieldOption{
					{Label: "转为字符串", Value: "string"},
					{Label: "转为数字", Value: "number"},
					{Label: "转为日期", Value: "date"},
					{Label: "转为布尔值", Value: "boolean"},
					{Label: "转为大写", Value: "uppercase"},
					{Label: "转为小写", Value: "lowercase"},
					{Label: "拼接数组", Value: "join"},
					{Label: "格式化字符串", Value: "format"},
				},
			},
			{
				Key:          "transformSupport",
				Label:        "字符串模板/拼接符",
				Type:         "input",
				Required:     false,
				DefaultValue: "",
				Placeholder:  "如: ${name}的年龄是${age}岁/,",
			},
			{
				Key:          "outputField",
				Label:        "输出字段",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "支持JSON路径，如: result.data, output.value",
			},
		},
		Style: base.NodeStyle{
			Fill:         "#fff7e6",
			Stroke:       "#fa8c16",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}
}
