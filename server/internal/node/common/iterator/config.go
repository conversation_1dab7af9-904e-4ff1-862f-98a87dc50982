package iterator

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取迭代器节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "iterator",
		Name:        "迭代器",
		Description: "对指定数组的每个元素依次执行后续节点流程",
		Category:    "common",
		NodeType:    "rect",
		Icon:        "🔄",
		FormFields: []base.FormField{
			{
				Key:          "arrayField",
				Label:        "数组字段名",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "请输入要迭代的数组字段名，如：datas",
			},
			{
				Key:          "outputField",
				Label:        "输出字段名",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "请输入将数组元素输出到的字段名，如：data",
			},
			{
				Key:          "errorHandling",
				Label:        "错误处理策略",
				Type:         "select",
				Required:     true,
				DefaultValue: "continue",
				Options: []base.FormFieldOption{
					{Label: "继续执行其他元素", Value: "continue"},
					{Label: "停止整个迭代", Value: "stop"},
				},
			},
		},
		Style: base.NodeStyle{
			Fill:         "#fff7ed",
			Stroke:       "#f97316",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}
}
