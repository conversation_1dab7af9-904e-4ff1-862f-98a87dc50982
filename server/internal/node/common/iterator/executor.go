package iterator

import (
	"context"
	"fmt"
	"log"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/utils"
	"time"
)

// IteratorNode 迭代器节点
type IteratorNode struct {
	config *base.NodeConfig
}

// NewIteratorNode 创建迭代器节点
func NewIteratorNode() *IteratorNode {
	return &IteratorNode{
		config: GetNodeConfig(),
	}
}

// GetID 获取节点ID
func (n *IteratorNode) GetID() string {
	return n.config.ID
}

// GetConfig 获取节点配置
func (n *IteratorNode) GetConfig() *base.NodeConfig {
	return n.config
}

// Validate 验证节点参数
func (n *IteratorNode) Validate(params map[string]interface{}) error {
	arrayField, ok := params["arrayField"].(string)
	if !ok || arrayField == "" {
		return fmt.Errorf("arrayField is required")
	}

	outputField, ok := params["outputField"].(string)
	if !ok || outputField == "" {
		return fmt.Errorf("outputField is required")
	}

	errorHandling, ok := params["errorHandling"].(string)
	if !ok || errorHandling == "" {
		return fmt.Errorf("errorHandling is required")
	}

	if errorHandling != "continue" && errorHandling != "stop" {
		return fmt.Errorf("errorHandling must be 'continue' or 'stop'")
	}

	return nil
}

// Execute 执行节点逻辑
func (n *IteratorNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行迭代器节点: %s", input.NodeID)

	// 验证参数
	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    err.Error(),
			FlowData: input.FlowData,
		}, nil
	}

	// 获取参数
	arrayField := input.Params["arrayField"].(string)
	outputField := input.Params["outputField"].(string)
	errorHandling := input.Params["errorHandling"].(string)

	log.Printf("迭代器参数 - arrayField: %s, outputField: %s, errorHandling: %s",
		arrayField, outputField, errorHandling)

	// 获取数组数据
	var arrayData []interface{}
	var found bool

	// 从流程数据中查找数组（支持JSON路径）
	if input.FlowData != nil {
		if data, exists := utils.GetFieldValueFromSources(input.PrevOutput, input.FlowData, arrayField); exists {
			if arr, ok := data.([]interface{}); ok {
				arrayData = arr
				found = true
			} else if arr, ok := data.([]map[string]interface{}); ok {
				// 处理 []map[string]interface{} 类型
				arrayData = make([]interface{}, len(arr))
				for i, v := range arr {
					arrayData[i] = v
				}
				found = true
			} else {
				return &base.ExecutionOutput{
					Success:  false,
					Continue: false,
					Error:    fmt.Sprintf("field '%s' is not a supported array type, got %T", arrayField, data),
					FlowData: input.FlowData,
				}, nil
			}
		}
	}

	if !found {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("array field '%s' not found in flow data", arrayField),
			FlowData: input.FlowData,
		}, nil
	}

	if len(arrayData) == 0 {
		log.Printf("数组 %s 为空，跳过迭代", arrayField)
		return &base.ExecutionOutput{
			Success:  true,
			Continue: true,
			Data: map[string]interface{}{
				"arrayField":    arrayField,
				"outputField":   outputField,
				"errorHandling": errorHandling,
				"arrayLength":   0,
				"message":       "数组为空，跳过迭代",
			},
			FlowData: input.FlowData,
			Message:  fmt.Sprintf("数组 %s 为空，跳过迭代", arrayField),
			Metadata: map[string]interface{}{
				"nodeType":      "iterator",
				"arrayField":    arrayField,
				"outputField":   outputField,
				"errorHandling": errorHandling,
				"arrayLength":   0,
				"executedAt":    time.Now().Format("2006-01-02 15:04:05"),
			},
		}, nil
	}

	// 构造节点输出数据（用于调试和日志）
	outputData := map[string]interface{}{
		"arrayField":    arrayField,
		"outputField":   outputField,
		"errorHandling": errorHandling,
		"arrayLength":   len(arrayData),
		"message":       fmt.Sprintf("准备迭代数组 %s，共 %d 个元素", arrayField, len(arrayData)),
	}

	log.Printf("迭代器准备完成，数组长度: %d", len(arrayData))

	// 注意：实际的迭代逻辑将在流程执行器中处理
	// 这里只是标记这是一个迭代器节点，并返回必要的信息
	return &base.ExecutionOutput{
		Success:  true,
		Continue: true,
		Data:     outputData,
		FlowData: input.FlowData,
		Message:  fmt.Sprintf("迭代器节点准备完成，将对数组 %s 的 %d 个元素进行迭代", arrayField, len(arrayData)),
		Metadata: map[string]interface{}{
			"nodeType":      "iterator",
			"arrayField":    arrayField,
			"outputField":   outputField,
			"errorHandling": errorHandling,
			"arrayLength":   len(arrayData),
			"arrayData":     arrayData, // 传递数组数据给执行器
			"executedAt":    time.Now().Format("2006-01-02 15:04:05"),
		},
	}, nil
}

// Example 生成示例数据
func (n *IteratorNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置参数
	arrayField := "datas"
	outputField := "data"
	errorHandling := "continue"

	if input.Config != nil {
		if af, ok := input.Config["arrayField"].(string); ok && af != "" {
			arrayField = af
		}
		if of, ok := input.Config["outputField"].(string); ok && of != "" {
			outputField = of
		}
		if eh, ok := input.Config["errorHandling"].(string); ok && eh != "" {
			errorHandling = eh
		}
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 获取数组数据（优先使用传入的数据，支持JSON路径）
	var originalArray []interface{}
	if val, exists := utils.GetFieldValueFromSources(nil, resultFlowData, arrayField); exists {
		if arr, ok := val.([]interface{}); ok {
			originalArray = arr
		} else if arr, ok := val.([]map[string]interface{}); ok {
			// 处理 []map[string]interface{} 类型
			originalArray = make([]interface{}, len(arr))
			for i, v := range arr {
				originalArray[i] = v
			}
		}
	}

	// 如果没有找到指定的数组字段，生成示例数据
	if originalArray == nil {
		// 生成示例数据
		originalArray = []interface{}{
			map[string]interface{}{"user": "zhangsan", "files": []string{"f1", "f2"}, "file_len": 2},
			map[string]interface{}{"user": "lisi", "files": []string{"f2", "f3", "f5"}, "file_len": 3},
		}
		resultFlowData[arrayField] = originalArray
		resultFlowData["time"] = "2025-07-17"
	}

	// 模拟第一次迭代的结果（示例）
	if len(originalArray) > 0 {
		firstElement := originalArray[0]
		resultFlowData[outputField] = firstElement

		// 移除原数组字段，因为迭代器会将其拆分（支持JSON路径）
		/*TODO if !utils.DeleteFieldValue(resultFlowData, arrayField) {
			// 如果JSON路径删除失败，尝试直接删除（兼容性）
			delete(resultFlowData, arrayField)
		}*/
	}

	// 构建变化描述
	var changes []base.DataChange

	if len(originalArray) > 0 {
		changes = append(changes, base.DataChange{
			Field:       arrayField,
			Action:      "delete",
			OldValue:    originalArray,
			NewValue:    nil,
			Description: fmt.Sprintf("移除原数组字段 %s", arrayField),
		})

		changes = append(changes, base.DataChange{
			Field:       outputField,
			Action:      "add",
			OldValue:    nil,
			NewValue:    originalArray[0],
			Description: fmt.Sprintf("添加迭代元素到字段 %s（示例显示第一个元素）", outputField),
		})
	}

	errorHandlingDesc := "继续执行其他元素"
	if errorHandling == "stop" {
		errorHandlingDesc = "停止整个迭代"
	}

	description := fmt.Sprintf("迭代数组 %s（共 %d 个元素），将每个元素放入字段 %s，错误处理策略：%s",
		arrayField, len(originalArray), outputField, errorHandlingDesc)

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: description,
		Changes:     changes,
	}
}
