package aggregate

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"reflect"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/utils"
	"sort"
	"strconv"
	"strings"
)

// Execute 执行聚合操作
func (n *AggregateNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("🔄 开始执行聚合节点")

	// 获取配置参数
	config := input.Params
	arrayField, ok := config["arrayField"].(string)
	if !ok || arrayField == "" {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    "arrayField parameter is required",
			FlowData: input.FlowData,
		}, nil
	}

	outputField, ok := config["outputField"].(string)
	if !ok || outputField == "" {
		outputField = "aggregatedData"
	}

	// 获取分组字段配置
	groupByFields := n.parseGroupByFields(config["groupByFields"])

	// 获取聚合字段配置
	aggregateFields, err := n.parseAggregateFields(config["aggregateFields"])
	if err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("invalid aggregateFields configuration: %v", err),
			FlowData: input.FlowData,
		}, nil
	}

	log.Printf("📊 聚合配置: 数组=%s, 分组字段=%v, 聚合字段=%d个, 输出=%s",
		arrayField, groupByFields, len(aggregateFields), outputField)

	// 获取要聚合的数组数据
	arrayData, found := utils.GetFieldValueFromSources(input.PrevOutput, input.FlowData, arrayField)
	if !found {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("array field '%s' not found in input data", arrayField),
			FlowData: input.FlowData,
		}, nil
	}

	// 确保是数组类型
	arrayValue := reflect.ValueOf(arrayData)
	if arrayValue.Kind() != reflect.Slice && arrayValue.Kind() != reflect.Array {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("field '%s' is not an array", arrayField),
			FlowData: input.FlowData,
		}, nil
	}

	// 执行聚合操作
	result, err := n.performAggregation(arrayValue, groupByFields, aggregateFields)
	if err != nil {
		return &base.ExecutionOutput{
			Success:  false,
			Continue: false,
			Error:    fmt.Sprintf("aggregation failed: %v", err),
			FlowData: input.FlowData,
		}, nil
	}

	// 更新流程数据
	updatedFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			updatedFlowData[k] = v
		}
	}
	rs := make([]interface{}, 0)
	for _, v := range result {
		rs = append(rs, v)
	}

	updatedFlowData[outputField] = rs

	log.Printf("✅ 聚合完成: 输入%d条记录，输出%d个分组", arrayValue.Len(), len(rs))

	return &base.ExecutionOutput{
		Success:  true,
		Continue: true,
		FlowData: updatedFlowData,
		Message:  fmt.Sprintf("成功聚合 %d 条记录为 %d 个分组", arrayValue.Len(), len(rs)),
	}, nil
}

// parseGroupByFields 解析分组字段配置
func (n *AggregateNode) parseGroupByFields(groupByConfig interface{}) []string {
	var groupByFields []string

	if groupByStr, ok := groupByConfig.(string); ok && groupByStr != "" {
		// 按逗号分割字段名
		fields := strings.Split(groupByStr, ",")
		for _, field := range fields {
			field = strings.TrimSpace(field)
			if field != "" {
				groupByFields = append(groupByFields, field)
			}
		}
	}

	return groupByFields
}

// AggregateFieldConfig 聚合字段配置
type AggregateFieldConfig struct {
	FieldName     string `json:"fieldName"`
	AggregateFunc string `json:"aggregateFunc"`
	OutputName    string `json:"outputName"`
}

// parseAggregateFields 解析聚合字段配置
func (n *AggregateNode) parseAggregateFields(aggregateConfig interface{}) ([]AggregateFieldConfig, error) {
	var aggregateFields []AggregateFieldConfig

	// 如果是字符串，尝试解析JSON
	if aggregateStr, ok := aggregateConfig.(string); ok {
		var aggregateArray []map[string]interface{}
		if err := json.Unmarshal([]byte(aggregateStr), &aggregateArray); err != nil {
			return nil, fmt.Errorf("failed to parse aggregateFields JSON: %v", err)
		}

		for i, item := range aggregateArray {
			config, err := n.parseAggregateFieldItem(item, i)
			if err != nil {
				return nil, err
			}
			aggregateFields = append(aggregateFields, config)
		}

		return aggregateFields, nil
	}

	// 如果是数组，直接处理
	aggregateArray, ok := aggregateConfig.([]interface{})
	if !ok {
		return nil, fmt.Errorf("aggregateFields must be a JSON string or array")
	}

	for i, item := range aggregateArray {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("aggregateFields[%d] must be an object", i)
		}

		config, err := n.parseAggregateFieldItem(itemMap, i)
		if err != nil {
			return nil, err
		}
		aggregateFields = append(aggregateFields, config)
	}

	if len(aggregateFields) == 0 {
		return nil, fmt.Errorf("at least one aggregate field is required")
	}

	return aggregateFields, nil
}

// parseAggregateFieldItem 解析单个聚合字段配置项
func (n *AggregateNode) parseAggregateFieldItem(itemMap map[string]interface{}, index int) (AggregateFieldConfig, error) {
	config := AggregateFieldConfig{}

	// fieldName 对于count函数可以为空
	if fieldName, ok := itemMap["fieldName"].(string); ok {
		config.FieldName = fieldName
	}

	// aggregateFunc 必须
	aggregateFunc, ok := itemMap["aggregateFunc"].(string)
	if !ok || aggregateFunc == "" {
		return config, fmt.Errorf("aggregateFields[%d].aggregateFunc is required", index)
	}
	config.AggregateFunc = aggregateFunc

	// outputName 必须
	outputName, ok := itemMap["outputName"].(string)
	if !ok || outputName == "" {
		return config, fmt.Errorf("aggregateFields[%d].outputName is required", index)
	}
	config.OutputName = outputName

	// 验证聚合函数
	if !n.isValidAggregateFunc(aggregateFunc) {
		return config, fmt.Errorf("aggregateFields[%d].aggregateFunc '%s' is not supported", index, aggregateFunc)
	}

	// count函数不需要fieldName，其他函数需要
	if aggregateFunc != "count" && config.FieldName == "" {
		return config, fmt.Errorf("aggregateFields[%d].fieldName is required for function '%s'", index, aggregateFunc)
	}

	return config, nil
}

// Example 生成示例数据
func (n *AggregateNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置参数
	arrayField := "orders"
	groupByFields := []string{"category", "type"}
	aggregateFields := []AggregateFieldConfig{
		{FieldName: "amount", AggregateFunc: "sum", OutputName: "total_amount"},
		{FieldName: "quantity", AggregateFunc: "sum", OutputName: "total_quantity"},
		{FieldName: "", AggregateFunc: "count", OutputName: "order_count"},
		{FieldName: "amount", AggregateFunc: "avg", OutputName: "avg_amount"},
	}
	outputField := "aggregatedData"

	if input.Config != nil {
		if af, ok := input.Config["arrayField"].(string); ok && af != "" {
			arrayField = af
		}
		if of, ok := input.Config["outputField"].(string); ok && of != "" {
			outputField = of
		}
		if gbf, ok := input.Config["groupByFields"].(string); ok && gbf != "" {
			groupByFields = n.parseGroupByFields(gbf)
		}
		// 解析聚合字段配置（与Execute方法保持一致）
		if aggregateConfig, exists := input.Config["aggregateFields"]; exists {
			if parsedFields, err := n.parseAggregateFields(aggregateConfig); err == nil {
				aggregateFields = parsedFields
			}
		}
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 获取数组数据（使用与Execute方法相同的逻辑）
	var originalArray []interface{}

	// 使用与Execute方法相同的数据源查找逻辑
	// 注意：在Example方法中，上一个节点的数据已经合并到了resultFlowData中
	arrayData, found := utils.GetFieldValueFromSources(nil, resultFlowData, arrayField)

	if found {
		// 确保是数组类型
		arrayValue := reflect.ValueOf(arrayData)
		if arrayValue.Kind() == reflect.Slice || arrayValue.Kind() == reflect.Array {
			// 转换为 []interface{} 类型
			originalArray = make([]interface{}, arrayValue.Len())
			for i := 0; i < arrayValue.Len(); i++ {
				originalArray[i] = arrayValue.Index(i).Interface()
			}
		}
	}

	// 如果没有找到指定的数组字段，生成示例数据
	if originalArray == nil {
		// 生成示例订单数据
		originalArray = []interface{}{
			map[string]interface{}{"category": "电子产品", "type": "手机", "amount": 1000, "quantity": 2, "brand": "Apple"},
			map[string]interface{}{"category": "电子产品", "type": "电脑", "amount": 5000, "quantity": 1, "brand": "Dell"},
			map[string]interface{}{"category": "电子产品", "type": "手机", "amount": 1200, "quantity": 1, "brand": "Samsung"},
			map[string]interface{}{"category": "服装", "type": "上衣", "amount": 200, "quantity": 3, "brand": "Nike"},
			map[string]interface{}{"category": "服装", "type": "裤子", "amount": 300, "quantity": 2, "brand": "Adidas"},
			map[string]interface{}{"category": "服装", "type": "上衣", "amount": 250, "quantity": 1, "brand": "Uniqlo"},
		}
		resultFlowData[arrayField] = originalArray
	}

	// 执行示例聚合
	arrayValue := reflect.ValueOf(originalArray)
	aggregatedResult, _ := n.performAggregation(arrayValue, groupByFields, aggregateFields)
	rs := make([]interface{}, 0)
	for _, v := range aggregatedResult {
		rs = append(rs, v)
	}
	// 更新输出字段
	resultFlowData[outputField] = rs

	// 构建变化描述
	var changes []base.DataChange
	changes = append(changes, base.DataChange{
		Field:       outputField,
		Action:      "add",
		NewValue:    rs,
		Description: fmt.Sprintf("对数组 %s 进行聚合，生成 %d 个分组结果", arrayField, len(rs)),
	})

	// 构建聚合字段描述
	var aggDescs []string
	for _, aggField := range aggregateFields {
		funcDesc := map[string]string{
			"count":            "计数",
			"sum":              "求和",
			"avg":              "平均值",
			"min":              "最小值",
			"max":              "最大值",
			"distinct_count":   "去重计数",
			"first":            "第一个值",
			"last":             "最后一个值",
			"collect":          "集合",
			"distinct_collect": "去重集合",
		}
		if aggField.FieldName == "" {
			aggDescs = append(aggDescs, fmt.Sprintf("%s → %s", funcDesc[aggField.AggregateFunc], aggField.OutputName))
		} else {
			aggDescs = append(aggDescs, fmt.Sprintf("%s(%s) → %s", funcDesc[aggField.AggregateFunc], aggField.FieldName, aggField.OutputName))
		}
	}

	groupDesc := "全部数据"
	if len(groupByFields) > 0 {
		groupDesc = fmt.Sprintf("按 %s 分组", strings.Join(groupByFields, ", "))
	}

	description := fmt.Sprintf("聚合数组 %s，%s，聚合函数：%s，从 %d 个元素聚合为 %d 个分组",
		arrayField, groupDesc, strings.Join(aggDescs, "、"), len(originalArray), len(aggregatedResult))

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Description: description,
		Changes:     changes,
	}
}

// isValidAggregateFunc 检查聚合函数是否有效
func (n *AggregateNode) isValidAggregateFunc(funcName string) bool {
	validFuncs := map[string]bool{
		"count":            true,
		"sum":              true,
		"avg":              true,
		"min":              true,
		"max":              true,
		"distinct_count":   true,
		"first":            true,
		"last":             true,
		"collect":          true,
		"distinct_collect": true,
	}
	return validFuncs[funcName]
}

// performAggregation 执行聚合操作
func (n *AggregateNode) performAggregation(arrayValue reflect.Value, groupByFields []string, aggregateFields []AggregateFieldConfig) ([]map[string]interface{}, error) {
	// 创建分组映射
	groups := make(map[string][]map[string]interface{})

	// 遍历数组元素进行分组
	for i := 0; i < arrayValue.Len(); i++ {
		element := arrayValue.Index(i).Interface()

		// 确保元素是map类型
		elementMap, ok := element.(map[string]interface{})
		if !ok {
			log.Printf("⚠️ 跳过非map类型的数组元素: %v", element)
			continue
		}

		// 生成分组键
		groupKey := n.generateGroupKey(elementMap, groupByFields)

		// 添加到对应分组
		if groups[groupKey] == nil {
			groups[groupKey] = make([]map[string]interface{}, 0)
		}
		groups[groupKey] = append(groups[groupKey], elementMap)
	}

	log.Printf("📋 分组完成: 共 %d 个分组", len(groups))

	// 对每个分组执行聚合计算
	var result []map[string]interface{}
	for groupKey, groupItems := range groups {
		aggregatedItem, err := n.aggregateGroup(groupKey, groupItems, groupByFields, aggregateFields)
		if err != nil {
			return nil, fmt.Errorf("failed to aggregate group '%s': %v", groupKey, err)
		}
		result = append(result, aggregatedItem)
	}

	// 对结果进行排序，确保输出稳定
	sort.Slice(result, func(i, j int) bool {
		// 按分组字段排序
		for _, field := range groupByFields {
			valI := fmt.Sprintf("%v", result[i][field])
			valJ := fmt.Sprintf("%v", result[j][field])
			if valI != valJ {
				return valI < valJ
			}
		}
		return false
	})

	return result, nil
}

// generateGroupKey 生成分组键
func (n *AggregateNode) generateGroupKey(item map[string]interface{}, groupByFields []string) string {
	if len(groupByFields) == 0 {
		return "all" // 没有分组字段时，所有记录归为一组
	}

	var keyParts []string
	for _, field := range groupByFields {
		// 支持嵌套字段路径 (如: user.profile.name)
		value, found := utils.GetFieldValue(item, field)
		if !found {
			value = nil
		}
		keyParts = append(keyParts, fmt.Sprintf("%v", value))
	}

	return fmt.Sprintf("[%s]", fmt.Sprintf("%v", keyParts))
}

// aggregateGroup 对单个分组执行聚合计算
func (n *AggregateNode) aggregateGroup(groupKey string, items []map[string]interface{}, groupByFields []string, aggregateFields []AggregateFieldConfig) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 添加分组字段到结果中
	if len(groupByFields) > 0 && len(items) > 0 {
		firstItem := items[0]
		for _, field := range groupByFields {
			// 支持嵌套字段路径 (如: user.profile.name)
			value, found := utils.GetFieldValue(firstItem, field)
			if found {
				// 使用字段路径的最后一部分作为结果字段名
				// 例如: user.profile.name -> name
				fieldParts := strings.Split(field, ".")
				resultFieldName := fieldParts[len(fieldParts)-1]
				result[resultFieldName] = value
			} else {
				// 如果字段不存在，使用原字段名并设置为nil
				fieldParts := strings.Split(field, ".")
				resultFieldName := fieldParts[len(fieldParts)-1]
				result[resultFieldName] = nil
			}
		}
	}

	// 执行每个聚合字段的计算
	for _, aggField := range aggregateFields {
		value, err := n.calculateAggregateValue(items, aggField)
		if err != nil {
			return nil, fmt.Errorf("failed to calculate %s for field %s: %v", aggField.AggregateFunc, aggField.FieldName, err)
		}
		result[aggField.OutputName] = value
	}

	return result, nil
}

// calculateAggregateValue 计算聚合值
func (n *AggregateNode) calculateAggregateValue(items []map[string]interface{}, aggField AggregateFieldConfig) (interface{}, error) {
	switch aggField.AggregateFunc {
	case "count":
		return len(items), nil

	case "sum":
		return n.calculateSum(items, aggField.FieldName)

	case "avg":
		sum, err := n.calculateSum(items, aggField.FieldName)
		if err != nil {
			return nil, err
		}
		if len(items) == 0 {
			return 0, nil
		}
		if sumFloat, ok := sum.(float64); ok {
			return sumFloat / float64(len(items)), nil
		}
		return 0, fmt.Errorf("cannot calculate average of non-numeric values")

	case "min":
		return n.calculateMin(items, aggField.FieldName)

	case "max":
		return n.calculateMax(items, aggField.FieldName)

	case "distinct_count":
		return n.calculateDistinctCount(items, aggField.FieldName)

	case "first":
		if len(items) > 0 {
			// 支持嵌套字段路径 (如: user.profile.name)
			value, found := utils.GetFieldValue(items[0], aggField.FieldName)
			if found {
				return value, nil
			}
		}
		return nil, nil

	case "last":
		if len(items) > 0 {
			// 支持嵌套字段路径 (如: user.profile.name)
			value, found := utils.GetFieldValue(items[len(items)-1], aggField.FieldName)
			if found {
				return value, nil
			}
		}
		return nil, nil

	case "collect":
		return n.calculateCollect(items, aggField.FieldName)

	case "distinct_collect":
		return n.calculateDistinctCollect(items, aggField.FieldName)

	default:
		return nil, fmt.Errorf("unsupported aggregate function: %s", aggField.AggregateFunc)
	}
}

// calculateCollect 计算收集
func (n *AggregateNode) calculateCollect(items []map[string]interface{}, fieldName string) (interface{}, error) {
	var collect = make([]interface{}, 0)
	for _, item := range items {
		// 支持嵌套字段路径 (如: user.profile.tags)
		value, found := utils.GetFieldValue(item, fieldName)
		if !found || value == nil {
			continue
		}
		collect = append(collect, value)
	}
	return collect, nil
}

// calculateDistinctCollect 计算去重收集
func (n *AggregateNode) calculateDistinctCollect(items []map[string]interface{}, fieldName string) (interface{}, error) {
	var collectMap = make(map[interface{}]bool)
	var collect = make([]interface{}, 0)
	for _, item := range items {
		// 支持嵌套字段路径 (如: user.profile.tags)
		value, found := utils.GetFieldValue(item, fieldName)
		if !found || value == nil {
			continue
		}
		collectMap[value] = true
	}
	for key := range collectMap {
		collect = append(collect, key)
	}
	return collect, nil
}

// calculateSum 计算求和
func (n *AggregateNode) calculateSum(items []map[string]interface{}, fieldName string) (interface{}, error) {
	var sum float64
	validCount := 0

	for _, item := range items {
		// 支持嵌套字段路径 (如: order.amount)
		value, found := utils.GetFieldValue(item, fieldName)
		if !found || value == nil {
			continue
		}

		numValue, err := n.convertToNumber(value)
		if err != nil {
			log.Printf("⚠️ 跳过非数值字段 %s: %v", fieldName, value)
			continue
		}

		sum += numValue
		validCount++
	}

	if validCount == 0 {
		return 0, nil
	}

	return sum, nil
}

// calculateMin 计算最小值
func (n *AggregateNode) calculateMin(items []map[string]interface{}, fieldName string) (interface{}, error) {
	var min float64
	hasValue := false

	for _, item := range items {
		// 支持嵌套字段路径 (如: order.amount)
		value, found := utils.GetFieldValue(item, fieldName)
		if !found || value == nil {
			continue
		}

		numValue, err := n.convertToNumber(value)
		if err != nil {
			continue
		}

		if !hasValue || numValue < min {
			min = numValue
			hasValue = true
		}
	}

	if !hasValue {
		return nil, nil
	}

	return min, nil
}

// calculateMax 计算最大值
func (n *AggregateNode) calculateMax(items []map[string]interface{}, fieldName string) (interface{}, error) {
	var max float64
	hasValue := false

	for _, item := range items {
		// 支持嵌套字段路径 (如: order.amount)
		value, found := utils.GetFieldValue(item, fieldName)
		if !found || value == nil {
			continue
		}

		numValue, err := n.convertToNumber(value)
		if err != nil {
			continue
		}

		if !hasValue || numValue > max {
			max = numValue
			hasValue = true
		}
	}

	if !hasValue {
		return nil, nil
	}

	return max, nil
}

// calculateDistinctCount 计算去重计数
func (n *AggregateNode) calculateDistinctCount(items []map[string]interface{}, fieldName string) (interface{}, error) {
	distinctValues := make(map[string]bool)

	for _, item := range items {
		// 支持嵌套字段路径 (如: user.profile.name)
		value, found := utils.GetFieldValue(item, fieldName)
		if found && value != nil {
			key := fmt.Sprintf("%v", value)
			distinctValues[key] = true
		}
	}

	return len(distinctValues), nil
}

// convertToNumber 将值转换为数字
func (n *AggregateNode) convertToNumber(value interface{}) (float64, error) {
	switch v := value.(type) {
	case int:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case float32:
		return float64(v), nil
	case float64:
		return v, nil
	case string:
		if num, err := strconv.ParseFloat(v, 64); err == nil {
			return num, nil
		}
		return 0, fmt.Errorf("cannot convert string '%s' to number", v)
	default:
		return 0, fmt.Errorf("cannot convert %T to number", value)
	}
}
