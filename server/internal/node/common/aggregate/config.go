package aggregate

import (
	"sec-flow-server/internal/node/base"
)

// AggregateNode 聚合节点
type AggregateNode struct {
	id string
}

// NewAggregateNode 创建聚合节点实例
func NewAggregateNode() *AggregateNode {
	return &AggregateNode{
		id: "aggregate",
	}
}

// GetID 获取节点ID
func (n *AggregateNode) GetID() string {
	return n.id
}

// Validate 验证节点参数
func (n *AggregateNode) Validate(params map[string]interface{}) error {
	return nil
}

// GetConfig 获取节点配置
func (n *AggregateNode) GetConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "aggregate",
		Name:        "数据聚合",
		Description: "对数组数据进行分组聚合操作，支持count、sum、avg、min、max、distinct_count等聚合函数",
		Category:    "common",
		NodeType:    "aggregate",
		Icon:        "📊",
		FormFields: []base.FormField{
			{
				Key:          "arrayField",
				Label:        "数组字段名",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "请输入要聚合的数组字段名，如: datas",
				Validation: map[string]interface{}{
					"required": true,
				},
			},
			{
				Key:          "groupByFields",
				Label:        "分组字段",
				Type:         "textarea",
				Required:     false,
				DefaultValue: "",
				Placeholder:  "请输入分组字段名，多个字段用逗号分隔，如: category,type",
				Validation: map[string]interface{}{
					"maxLength": 500,
				},
			},
			{
				Key:          "aggregateFields",
				Label:        "聚合字段配置",
				Type:         "aggregate_array",
				Required:     true,
				DefaultValue: `[{"fieldName":"amount","aggregateFunc":"sum","outputName":"total_amount"},{"fieldName":"","aggregateFunc":"count","outputName":"record_count"}]`,
				Placeholder:  "请配置聚合字段和函数",
				Validation: map[string]interface{}{
					"aggregateFunctions": []base.FormFieldOption{
						{Label: "计数 (count)", Value: "count"},
						{Label: "求和 (sum)", Value: "sum"},
						{Label: "平均值 (avg)", Value: "avg"},
						{Label: "最小值 (min)", Value: "min"},
						{Label: "最大值 (max)", Value: "max"},
						{Label: "去重计数 (distinct_count)", Value: "distinct_count"},
						{Label: "第一个值 (first)", Value: "first"},
						{Label: "最后一个值 (last)", Value: "last"},
						{Label: "集合 (collect)", Value: "collect"},
						{Label: "去重集合 (distinct_collect)", Value: "distinct_collect"},
					},
				},
			},
			{
				Key:          "outputField",
				Label:        "输出字段名",
				Type:         "input",
				Required:     true,
				DefaultValue: "aggregatedData",
				Placeholder:  "请输入聚合结果的输出字段名",
				Validation: map[string]interface{}{
					"required": true,
				},
			},
		},
		Style: base.NodeStyle{
			Fill:         "#e6f7ff",
			Stroke:       "#1890ff",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#1890ff",
			BorderRadius: 8,
		},
	}
}
