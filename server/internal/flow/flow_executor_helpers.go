package flow

import (
	"sec-flow-server/internal/utils"
)

// deepCopyElement 深拷贝元素
func (e *FlowExecutor) deepCopyElement(element interface{}) interface{} {
	return deepCopy(element)
}

// setFieldValue 设置字段值，支持JSON路径
func (e *FlowExecutor) setFieldValue(data map[string]interface{}, fieldPath string, value interface{}) bool {
	return utils.SetFieldValue(data, fieldPath, value)
}

// deleteFieldValue 删除字段值，支持JSON路径
func (e *FlowExecutor) deleteFieldValue(data map[string]interface{}, fieldPath string) bool {
	return utils.DeleteFieldValue(data, fieldPath)
}

