package flow

import (
	"encoding/json"
	"fmt"
	"log"
	"sec-flow-server/internal/node"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/service"
)

// ExampleService 示例数据服务
type ExampleService struct {
	flowService  *service.FlowService
	nodeRegistry *node.Registry
}

// NewExampleService 创建示例数据服务
func NewExampleService(flowService *service.FlowService) *ExampleService {
	return &ExampleService{
		flowService:  flowService,
		nodeRegistry: node.GetGlobalRegistry(),
	}
}

// ExampleResult 示例执行结果
type ExampleResult struct {
	FlowID         string                        `json:"flowId"`
	Success        bool                          `json:"success"`
	Message        string                        `json:"message"`
	ExecutionOrder []string                      `json:"executionOrder"`
	NodeResults    map[string]*NodeExampleResult `json:"nodeResults"`
	FlowData       map[string]interface{}        `json:"flowData"`
}

// NodeExampleResult 节点示例结果
type NodeExampleResult struct {
	NodeID       string                 `json:"nodeId"`
	NodeType     string                 `json:"nodeType"`
	PresetNodeId string                 `json:"presetNodeId,omitempty"`
	Description  string                 `json:"description"`
	FlowData     map[string]interface{} `json:"flowData"`
	Changes      []base.DataChange      `json:"changes"`
}

// GenerateFlowExample 生成流程示例数据
func (s *ExampleService) GenerateFlowExample(flowID string) (*ExampleResult, error) {
	// 获取流程定义
	flowResp, err := s.flowService.GetFlow(flowID)
	if err != nil {
		return nil, fmt.Errorf("failed to get flow: %v", err)
	}

	// 将 model.FlowData 转换为 base.FlowDefinition
	flowDef := base.FlowDefinition{
		ID:    flowResp.ID,
		Name:  flowResp.Name,
		Nodes: make([]base.FlowNode, len(flowResp.Data.Nodes)),
		Edges: make([]base.FlowEdge, len(flowResp.Data.Edges)),
	}

	// 转换节点
	for i, node := range flowResp.Data.Nodes {
		flowDef.Nodes[i] = base.FlowNode{
			ID:         node.ID,
			Type:       node.Type,
			X:          node.X,
			Y:          node.Y,
			Text:       node.Text,
			Properties: node.Properties,
		}
	}

	// 转换边
	for i, edge := range flowResp.Data.Edges {
		flowDef.Edges[i] = base.FlowEdge{
			ID:     edge.ID,
			Source: edge.SourceNodeId,
			Target: edge.TargetNodeId,
			Type:   edge.Type,
		}
	}

	return s.generateExampleFromDefinition(&flowDef, flowID)
}

// generateExampleFromDefinitionWithInitialData 从流程定义生成示例数据（带初始数据）
func (s *ExampleService) generateExampleFromDefinitionWithInitialData(flowDef *base.FlowDefinition, flowID string, initialFlowData map[string]interface{}) (*ExampleResult, error) {
	// 构建节点执行顺序
	executionOrder, err := s.buildExecutionOrder(flowDef)
	if err != nil {
		return nil, fmt.Errorf("failed to build execution order: %v", err)
	}

	// 初始化流程数据（使用传入的初始数据）
	currentFlowData := make(map[string]interface{})
	if initialFlowData != nil {
		for k, v := range initialFlowData {
			currentFlowData[k] = v
		}
	}
	nodeResults := make(map[string]*NodeExampleResult)

	// 按顺序执行每个节点的示例方法
	for _, nodeID := range executionOrder {
		nodeInfo := s.findNodeByID(flowDef, nodeID)
		if nodeInfo == nil {
			log.Printf("Node %s not found in flow definition", nodeID)
			continue
		}

		// 获取节点配置
		config := make(map[string]interface{})
		if nodeInfo.Properties != nil {
			// 优先从 formData 中获取配置（前端存储的实际配置）
			if formData, ok := nodeInfo.Properties["formData"]; ok {
				if configMap, ok := formData.(map[string]interface{}); ok {
					config = configMap
				}
			} else if nodeConfig, ok := nodeInfo.Properties["config"]; ok {
				// 兼容旧的 config 字段
				if configMap, ok := nodeConfig.(map[string]interface{}); ok {
					config = configMap
				}
			}
		}

		// 执行节点示例
		result, err := s.executeNodeExample(nodeInfo, config, currentFlowData)
		if err != nil {
			log.Printf("Failed to execute example for node %s: %v", nodeID, err)
			continue
		}

		// 保存节点执行时的数据快照（深拷贝）
		nodeFlowDataSnapshot := make(map[string]interface{})
		if result.FlowData != nil {
			for k, v := range result.FlowData {
				nodeFlowDataSnapshot[k] = deepCopy(v)
			}
		}

		// 更新流程数据
		currentFlowData = result.FlowData

		// 记录节点结果，使用数据快照
		nodeResults[nodeID] = &NodeExampleResult{
			NodeID:       nodeID,
			NodeType:     nodeInfo.Type,
			PresetNodeId: s.getPresetNodeId(nodeInfo),
			Description:  result.Description,
			FlowData:     nodeFlowDataSnapshot,
			Changes:      result.Changes,
		}
	}

	return &ExampleResult{
		FlowID:         flowID,
		Success:        true,
		Message:        "示例数据生成成功",
		ExecutionOrder: executionOrder,
		NodeResults:    nodeResults,
		FlowData:       currentFlowData,
	}, nil
}

// generateExampleFromDefinition 从流程定义生成示例数据
func (s *ExampleService) generateExampleFromDefinition(flowDef *base.FlowDefinition, flowID string) (*ExampleResult, error) {
	// 构建节点执行顺序
	executionOrder, err := s.buildExecutionOrder(flowDef)
	if err != nil {
		return nil, fmt.Errorf("failed to build execution order: %v", err)
	}

	// 初始化流程数据
	currentFlowData := make(map[string]interface{})
	nodeResults := make(map[string]*NodeExampleResult)

	// 按顺序执行每个节点的示例方法
	for _, nodeID := range executionOrder {
		nodeInfo := s.findNodeByID(flowDef, nodeID)
		if nodeInfo == nil {
			log.Printf("Node %s not found in flow definition", nodeID)
			continue
		}

		// 获取节点配置
		config := make(map[string]interface{})
		if nodeInfo.Properties != nil {
			// 优先从 formData 中获取配置（前端存储的实际配置）
			if formData, ok := nodeInfo.Properties["formData"]; ok {
				if configMap, ok := formData.(map[string]interface{}); ok {
					config = configMap
				}
			} else if nodeConfig, ok := nodeInfo.Properties["config"]; ok {
				// 兼容旧的 config 字段
				if configMap, ok := nodeConfig.(map[string]interface{}); ok {
					config = configMap
				}
			}
		}

		// 执行节点示例
		result, err := s.executeNodeExample(nodeInfo, config, currentFlowData)
		if err != nil {
			log.Printf("Failed to execute example for node %s: %v", nodeID, err)
			continue
		}

		// 保存节点执行时的数据快照（深拷贝）
		nodeFlowDataSnapshot := make(map[string]interface{})
		if result.FlowData != nil {
			for k, v := range result.FlowData {
				nodeFlowDataSnapshot[k] = deepCopy(v)
			}
		}

		// 更新流程数据
		currentFlowData = result.FlowData

		// 记录节点结果，使用数据快照
		nodeResults[nodeID] = &NodeExampleResult{
			NodeID:       nodeID,
			NodeType:     nodeInfo.Type,
			PresetNodeId: s.getPresetNodeId(nodeInfo),
			Description:  result.Description,
			FlowData:     nodeFlowDataSnapshot,
			Changes:      result.Changes,
		}
	}

	return &ExampleResult{
		FlowID:         flowID,
		Success:        true,
		Message:        "示例数据生成成功",
		ExecutionOrder: executionOrder,
		NodeResults:    nodeResults,
		FlowData:       currentFlowData,
	}, nil
}

// executeNodeExample 执行节点示例
func (s *ExampleService) executeNodeExample(nodeInfo *base.FlowNode, config map[string]interface{}, flowData map[string]interface{}) (*base.ExampleOutput, error) {
	// 获取预置节点ID
	presetNodeId := s.getPresetNodeId(nodeInfo)
	if presetNodeId == "" {
		return nil, fmt.Errorf("no preset node id found for node %s", nodeInfo.ID)
	}

	// 从注册表获取节点实例
	nodeInstance, err := s.nodeRegistry.GetNode(presetNodeId)
	if err != nil {
		return nil, fmt.Errorf("node %s not found in registry: %v", presetNodeId, err)
	}

	// 准备示例输入
	exampleInput := &base.ExampleInput{
		FlowData: flowData,
		Config:   config,
	}

	// 执行示例方法
	return nodeInstance.Example(exampleInput), nil
}

// getPresetNodeId 获取预置节点ID
func (s *ExampleService) getPresetNodeId(nodeInfo *base.FlowNode) string {
	if nodeInfo.Properties == nil {
		return ""
	}

	if presetNodeId, ok := nodeInfo.Properties["presetNodeId"].(string); ok {
		return presetNodeId
	}

	return ""
}

// findNodeByID 根据ID查找节点
func (s *ExampleService) findNodeByID(flowDef *base.FlowDefinition, nodeID string) *base.FlowNode {
	for i := range flowDef.Nodes {
		if flowDef.Nodes[i].ID == nodeID {
			return &flowDef.Nodes[i]
		}
	}
	return nil
}

// buildExecutionOrder 构建节点执行顺序
func (s *ExampleService) buildExecutionOrder(flowDef *base.FlowDefinition) ([]string, error) {
	// 构建邻接表
	graph := make(map[string][]string)
	inDegree := make(map[string]int)

	// 初始化所有节点
	for _, node := range flowDef.Nodes {
		graph[node.ID] = []string{}
		inDegree[node.ID] = 0
	}

	// 构建图
	for _, edge := range flowDef.Edges {
		graph[edge.Source] = append(graph[edge.Source], edge.Target)
		inDegree[edge.Target]++
	}

	// 拓扑排序
	var queue []string
	var result []string

	// 找到所有入度为0的节点（起始节点）
	for nodeID, degree := range inDegree {
		if degree == 0 {
			queue = append(queue, nodeID)
		}
	}

	// 执行拓扑排序
	for len(queue) > 0 {
		current := queue[0]
		queue = queue[1:]
		result = append(result, current)

		// 处理当前节点的所有邻居
		for _, neighbor := range graph[current] {
			inDegree[neighbor]--
			if inDegree[neighbor] == 0 {
				queue = append(queue, neighbor)
			}
		}
	}

	// 检查是否有环
	if len(result) != len(flowDef.Nodes) {
		return nil, fmt.Errorf("flow contains cycles")
	}

	return result, nil
}

// GenerateFlowPreview 生成流程预览数据（基于传入的流程数据）
func (s *ExampleService) GenerateFlowPreview(flowData interface{}) (*ExampleResult, error) {
	// 将 interface{} 转换为 map[string]interface{}
	flowDataMap, ok := flowData.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid flow data format")
	}

	// 解析节点数据
	nodesData, ok := flowDataMap["nodes"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid nodes data format")
	}

	// 解析边数据
	edgesData, ok := flowDataMap["edges"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid edges data format")
	}

	// 解析初始流程数据
	initialFlowData := make(map[string]interface{})
	if flowDataField, exists := flowDataMap["flowData"]; exists {
		if flowDataFieldMap, ok := flowDataField.(map[string]interface{}); ok {
			initialFlowData = flowDataFieldMap
		}
	}

	// 构建 FlowDefinition
	flowDef := base.FlowDefinition{
		ID:    "preview",
		Name:  "Flow Preview",
		Nodes: make([]base.FlowNode, len(nodesData)),
		Edges: make([]base.FlowEdge, len(edgesData)),
	}

	// 转换节点
	for i, nodeData := range nodesData {
		nodeMap, ok := nodeData.(map[string]interface{})
		if !ok {
			continue
		}

		flowDef.Nodes[i] = base.FlowNode{
			ID:         getString(nodeMap, "id"),
			Type:       getString(nodeMap, "type"),
			X:          getFloat64(nodeMap, "x"),
			Y:          getFloat64(nodeMap, "y"),
			Text:       getString(nodeMap, "text"),
			Properties: getMap(nodeMap, "properties"),
		}
	}

	// 转换边
	for i, edgeData := range edgesData {
		edgeMap, ok := edgeData.(map[string]interface{})
		if !ok {
			continue
		}

		flowDef.Edges[i] = base.FlowEdge{
			ID:     getString(edgeMap, "id"),
			Source: getString(edgeMap, "sourceNodeId"),
			Target: getString(edgeMap, "targetNodeId"),
			Type:   getString(edgeMap, "type"),
		}
	}

	// 使用现有的逻辑生成示例数据，传入初始流程数据
	return s.generateExampleFromDefinitionWithInitialData(&flowDef, "preview", initialFlowData)
}

// 辅助函数：从 map 中获取字符串值
func getString(m map[string]interface{}, key string) string {
	if val, ok := m[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

// 辅助函数：从 map 中获取 float64 值
func getFloat64(m map[string]interface{}, key string) float64 {
	if val, ok := m[key]; ok {
		switch v := val.(type) {
		case float64:
			return v
		case int:
			return float64(v)
		}
	}
	return 0
}

// 辅助函数：从 map 中获取 map 值
func getMap(m map[string]interface{}, key string) map[string]interface{} {
	if val, ok := m[key]; ok {
		if mapVal, ok := val.(map[string]interface{}); ok {
			return mapVal
		}
	}
	return nil
}

// deepCopy 深拷贝任意对象
func deepCopy(src interface{}) interface{} {
	// 使用JSON序列化和反序列化进行深拷贝
	data, err := json.Marshal(src)
	if err != nil {
		return src
	}
	
	var dst interface{}
	if err := json.Unmarshal(data, &dst); err != nil {
		return src
	}
	
	return dst
}
