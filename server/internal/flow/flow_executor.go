package flow

import (
	"context"
	"fmt"
	"log"
	"sec-flow-server/internal/constants"
	"sec-flow-server/internal/model"
	"sec-flow-server/internal/node"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/service"
	"strings"
	"time"

	"github.com/google/uuid"
)

// FlowExecutor 流程执行器
type FlowExecutor struct {
	flowService      *service.FlowService
	nodeRegistry     *node.Registry
	executionService *service.FlowExecutionService
}

// NewFlowExecutor 创建流程执行器
func NewFlowExecutor(flowService *service.FlowService) *FlowExecutor {
	return &FlowExecutor{
		flowService:      flowService,
		nodeRegistry:     node.GetGlobalRegistry(),
		executionService: service.NewFlowExecutionService(),
	}
}

// ExecutionRequest 执行请求
type ExecutionRequest struct {
	FlowID      string                 `json:"flowId" binding:"required"`
	StartNodeID string                 `json:"startNodeId,omitempty"`
	InputData   map[string]interface{} `json:"inputData,omitempty"`
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	ExecutionID string                           `json:"executionId"`
	FlowID      string                           `json:"flowId"`
	Status      string                           `json:"status"` // running, completed, failed
	StartTime   int64                            `json:"startTime"`
	EndTime     int64                            `json:"endTime,omitempty"`
	Steps       []ExecutionStep                  `json:"steps"`
	FinalOutput map[string]interface{}           `json:"finalOutput,omitempty"`
	Error       string                           `json:"error,omitempty"`
	NodeResults map[string]*base.ExecutionOutput `json:"nodeResults"`
}

// ExecutionStep 执行步骤
type ExecutionStep struct {
	StepID    string                 `json:"stepId"`
	NodeID    string                 `json:"nodeId"`
	NodeType  string                 `json:"nodeType"`
	NodeName  string                 `json:"nodeName"`
	StartTime int64                  `json:"startTime"`
	EndTime   int64                  `json:"endTime"`
	Status    string                 `json:"status"` // running, completed, failed, skipped
	Input     map[string]interface{} `json:"input,omitempty"`
	Output    map[string]interface{} `json:"output,omitempty"`
	Error     string                 `json:"error,omitempty"`
	Duration  int64                  `json:"duration"` // 毫秒
}

// ExecuteFlow 执行流程
func (e *FlowExecutor) ExecuteFlow(ctx context.Context, req *ExecutionRequest) (*ExecutionResult, error) {
	log.Printf("开始执行流程: %s, StartNodeID: %s", req.FlowID, req.StartNodeID)

	// 获取流程定义
	flow, err := e.flowService.GetFlow(req.FlowID)
	if err != nil {
		return nil, fmt.Errorf("failed to get flow: %v", err)
	}

	// 解析流程数据
	flowDef, err := e.parseFlowDefinition(flow)
	if err != nil {
		return nil, fmt.Errorf("failed to parse flow definition: %v", err)
	}

	// 创建执行结果
	result := &ExecutionResult{
		ExecutionID: uuid.New().String(),
		FlowID:      req.FlowID,
		Status:      "running",
		StartTime:   time.Now().UnixMilli(),
		Steps:       make([]ExecutionStep, 0),
		NodeResults: make(map[string]*base.ExecutionOutput),
	}

	log.Printf("执行ID: %s, 节点数量: %d, 连线数量: %d",
		result.ExecutionID, len(flowDef.Nodes), len(flowDef.Edges))

	// 执行流程
	err = e.executeFlowNodes(ctx, flowDef, req, result)
	if err != nil {
		result.Status = "failed"
		result.Error = err.Error()
		log.Printf("流程执行失败: %v", err)
	} else {
		result.Status = "completed"
		log.Printf("流程执行完成")
	}

	result.EndTime = time.Now().UnixMilli()
	return result, nil
}

// ExecuteFlowWithRecord 执行流程并保存执行记录
func (e *FlowExecutor) ExecuteFlowWithRecord(ctx context.Context, req *ExecutionRequest,
	triggerType constants.TriggerType, triggerSource string) (result *ExecutionResult, finalErr error) {

	log.Printf("开始执行流程并记录: %s, 触发类型: %s", req.FlowID, triggerType)

	// 获取流程定义
	flow, err := e.flowService.GetFlow(req.FlowID)
	if err != nil {
		return nil, fmt.Errorf("failed to get flow: %v", err)
	}

	// 创建执行记录
	execution, err := e.executionService.CreateExecution(
		req.FlowID,
		flow.Name,
		&flow.Data,
		triggerType,
		triggerSource,
		req.InputData,
	)
	if err != nil {
		log.Printf("创建执行记录失败: %v", err)
		// 继续执行，但记录错误
	}

	// 添加panic恢复机制
	defer func() {
		if r := recover(); r != nil {
			log.Printf("🚨 流程执行发生panic: %v", r)

			// 记录详细的panic信息
			panicMsg := fmt.Sprintf("流程执行发生panic: %v", r)
			log.Printf("🚨 Panic详情: %s", panicMsg)

			// 更新执行记录为失败状态
			if execution != nil {
				updateErr := e.executionService.UpdateExecution(
					execution.ID,
					constants.ExecutionStatusFailed,
					nil,
					nil,
					panicMsg,
				)
				if updateErr != nil {
					log.Printf("❌ 更新panic执行记录失败: %v", updateErr)
				} else {
					log.Printf("✅ 已将panic执行记录标记为失败")
				}
			}

			// 创建失败的执行结果
			result = &ExecutionResult{
				ExecutionID: execution.ID,
				FlowID:      req.FlowID,
				Status:      "failed",
				Error:       panicMsg,
				StartTime:   time.Now().UnixMilli(),
				EndTime:     time.Now().UnixMilli(),
				Steps:       make([]ExecutionStep, 0),
				NodeResults: make(map[string]*base.ExecutionOutput),
			}

			finalErr = fmt.Errorf("流程执行panic: %v", r)
		}
	}()

	// 执行流程
	result, err = e.ExecuteFlow(ctx, req)
	if err != nil {
		log.Printf("❌ 流程执行失败: %v", err)
		// 更新执行记录为失败状态
		if execution != nil {
			updateErr := e.executionService.UpdateExecution(
				execution.ID,
				constants.ExecutionStatusFailed,
				nil,
				nil,
				err.Error(),
			)
			if updateErr != nil {
				log.Printf("❌ 更新失败执行记录失败: %v", updateErr)
			}
		}
		return nil, err
	}

	// 转换执行结果为执行日志
	executionLog := e.convertToExecutionLog(result)

	// 确定最终状态
	var finalStatus constants.ExecutionStatus
	switch result.Status {
	case "completed":
		finalStatus = constants.ExecutionStatusCompleted
	case "failed":
		finalStatus = constants.ExecutionStatusFailed
	default:
		finalStatus = constants.ExecutionStatusCompleted
	}

	// 更新执行记录
	if execution != nil {
		err = e.executionService.UpdateExecution(
			execution.ID,
			finalStatus,
			e.extractOutputData(result),
			executionLog,
			result.Error,
		)
		if err != nil {
			log.Printf("❌ 更新执行记录失败: %v", err)
		} else {
			log.Printf("✅ 执行记录更新成功，状态: %s", finalStatus)
		}
	}

	return result, nil
}

// convertToExecutionLog 转换执行结果为执行日志
func (e *FlowExecutor) convertToExecutionLog(result *ExecutionResult) *model.FlowExecutionLog {
	var steps []model.FlowExecutionStep

	for _, step := range result.Steps {
		executionStep := model.FlowExecutionStep{
			StepID:       step.StepID,
			NodeID:       step.NodeID,
			NodeType:     step.NodeType,
			NodeName:     step.NodeName,
			Status:       step.Status,
			StartTime:    step.StartTime,
			EndTime:      step.EndTime,
			Duration:     step.Duration,
			InputData:    step.Input,
			OutputData:   step.Output,
			ErrorMessage: step.Error,
		}

		// 从NodeResults中获取FlowData
		if nodeResult, exists := result.NodeResults[step.NodeID]; exists {
			executionStep.FlowData = nodeResult.FlowData
		}

		steps = append(steps, executionStep)
	}

	// 计算执行摘要
	summary := model.FlowExecutionSummary{
		TotalSteps:   len(steps),
		SuccessSteps: 0,
		FailedSteps:  0,
		SkippedSteps: 0,
	}

	var executionPath []string
	for _, step := range steps {
		executionPath = append(executionPath, step.NodeName)
		switch step.Status {
		case "completed":
			summary.SuccessSteps++
		case "failed":
			summary.FailedSteps++
		case "skipped":
			summary.SkippedSteps++
		}
	}
	summary.ExecutionPath = strings.Join(executionPath, " -> ")

	return &model.FlowExecutionLog{
		ExecutionID: result.ExecutionID,
		Steps:       steps,
		NodeResults: e.convertNodeResults(result.NodeResults),
		Summary:     summary,
	}
}

// convertNodeResults 转换节点结果
func (e *FlowExecutor) convertNodeResults(nodeResults map[string]*base.ExecutionOutput) map[string]interface{} {
	converted := make(map[string]interface{})
	for nodeID, output := range nodeResults {
		converted[nodeID] = map[string]interface{}{
			"success":  output.Success,
			"continue": output.Continue,
			"data":     output.Data,
			"flowData": output.FlowData,
			"error":    output.Error,
		}
	}
	return converted
}

// extractOutputData 提取输出数据
func (e *FlowExecutor) extractOutputData(result *ExecutionResult) map[string]interface{} {
	outputData := make(map[string]interface{})

	// 从最后一个成功的节点结果中提取FlowData作为输出
	for _, nodeResult := range result.NodeResults {
		if nodeResult.Success && nodeResult.FlowData != nil {
			for k, v := range nodeResult.FlowData {
				outputData[k] = v
			}
		}
	}

	return outputData
}

// parseFlowDefinition 解析流程定义
func (e *FlowExecutor) parseFlowDefinition(flow *model.FlowResponse) (*base.FlowDefinition, error) {
	// 转换节点数据
	nodes := make([]base.FlowNode, len(flow.Data.Nodes))
	for i, node := range flow.Data.Nodes {
		nodes[i] = base.FlowNode{
			ID:         node.ID,
			Type:       node.Type,
			X:          node.X,
			Y:          node.Y,
			Text:       node.Text,
			Properties: node.Properties,
		}
	}

	// 转换连线数据
	edges := make([]base.FlowEdge, len(flow.Data.Edges))
	for i, edge := range flow.Data.Edges {
		edges[i] = base.FlowEdge{
			ID:     edge.ID,
			Source: edge.SourceNodeId,
			Target: edge.TargetNodeId,
			Type:   edge.Type,
		}
	}

	return &base.FlowDefinition{
		ID:    flow.ID,
		Name:  flow.Name,
		Nodes: nodes,
		Edges: edges,
	}, nil
}

// executeFlowNodes 执行流程节点
func (e *FlowExecutor) executeFlowNodes(ctx context.Context, flowDef *base.FlowDefinition, req *ExecutionRequest, result *ExecutionResult) error {
	// 构建节点图
	nodeMap := make(map[string]*base.FlowNode)
	for i := range flowDef.Nodes {
		nodeMap[flowDef.Nodes[i].ID] = &flowDef.Nodes[i]
	}

	// 构建连接关系
	nextNodes := make(map[string][]string)
	for _, edge := range flowDef.Edges {
		nextNodes[edge.Source] = append(nextNodes[edge.Source], edge.Target)
	}

	// 找到开始节点
	startNodeID := req.StartNodeID
	if startNodeID == "" {
		startNodeID = e.findStartNode(flowDef)
	}

	if startNodeID == "" {
		return fmt.Errorf("no start node found")
	}

	log.Printf("从节点开始执行: %s", startNodeID)

	// 初始化流程共享数据
	sharedFlowData := make(map[string]interface{})
	if req.InputData != nil {
		// 将输入数据复制到共享数据中
		for k, v := range req.InputData {
			sharedFlowData[k] = v
		}
	}

	// 执行上下文
	execCtx := &base.ExecutionContext{
		FlowID:      flowDef.ID,
		ExecutionID: result.ExecutionID,
		NodeResults: result.NodeResults,
		FlowData:    sharedFlowData,
		StartTime:   result.StartTime,
		CurrentStep: 0,
	}

	// 从开始节点开始执行
	return e.executeNodeChain(ctx, startNodeID, nodeMap, nextNodes, execCtx, result)
}

// findStartNode 查找开始节点
func (e *FlowExecutor) findStartNode(flowDef *base.FlowDefinition) string {
	log.Printf("查找开始节点，总节点数: %d", len(flowDef.Nodes))

	// 打印所有节点信息
	for i, node := range flowDef.Nodes {
		log.Printf("节点 %d: ID=%s, Type=%s, Text=%s", i, node.ID, node.Type, node.Text)
		if node.Properties != nil {
			if presetNodeId, ok := node.Properties["presetNodeId"].(string); ok {
				log.Printf("  预置节点ID: %s", presetNodeId)
			}
		}
	}

	// 打印所有连线信息
	log.Printf("总连线数: %d", len(flowDef.Edges))
	for i, edge := range flowDef.Edges {
		log.Printf("连线 %d: %s -> %s", i, edge.Source, edge.Target)
	}

	// 查找没有入边的节点作为开始节点
	hasIncoming := make(map[string]bool)
	for _, edge := range flowDef.Edges {
		hasIncoming[edge.Target] = true
	}

	for _, node := range flowDef.Nodes {
		if !hasIncoming[node.ID] {
			log.Printf("找到开始节点: %s", node.ID)
			return node.ID
		}
	}

	// 如果没找到，返回第一个节点
	if len(flowDef.Nodes) > 0 {
		log.Printf("没有找到无入边节点，使用第一个节点: %s", flowDef.Nodes[0].ID)
		return flowDef.Nodes[0].ID
	}

	log.Printf("没有找到任何节点")
	return ""
}

// executeNodeChain 执行节点链（支持并行分支）
func (e *FlowExecutor) executeNodeChain(ctx context.Context, nodeID string, nodeMap map[string]*base.FlowNode,
	nextNodes map[string][]string, execCtx *base.ExecutionContext, result *ExecutionResult) error {

	visited := make(map[string]bool)

	// 使用递归方式执行，支持并行分支
	return e.executeNodeRecursive(ctx, nodeID, nodeMap, nextNodes, execCtx, result, visited)
}

// executeNodeRecursive 递归执行节点（支持并行分支）
func (e *FlowExecutor) executeNodeRecursive(ctx context.Context, nodeID string, nodeMap map[string]*base.FlowNode,
	nextNodes map[string][]string, execCtx *base.ExecutionContext, result *ExecutionResult, visited map[string]bool) error {

	// 防止循环
	if visited[nodeID] {
		log.Printf("节点 %s 已访问，跳过", nodeID)
		return nil
	}
	visited[nodeID] = true

	// 执行当前节点
	shouldContinue, currentFlowData, err := e.executeNodeWithDataReturn(ctx, nodeID, nodeMap, execCtx, result)
	if err != nil {
		return fmt.Errorf("failed to execute node %s: %v", nodeID, err)
	}

	// 如果节点返回不继续执行，则停止流程
	if !shouldContinue {
		log.Printf("节点 %s 返回停止执行，流程终止", nodeID)
		return nil
	}

	// 检查是否是迭代器节点
	if e.isIteratorNode(nodeID, nodeMap, result) {
		log.Printf("节点 %s 是迭代器节点，开始迭代处理", nodeID)
		return e.handleIteratorNode(ctx, nodeID, nodeMap, nextNodes, execCtx, result, visited)
	}

	// 获取下一个节点列表
	nextNodeIDs, exists := nextNodes[nodeID]
	if !exists || len(nextNodeIDs) == 0 {
		log.Printf("节点 %s 没有后续节点，分支结束", nodeID)
		return nil
	}

	// 如果只有一个后续节点，直接传递数据
	if len(nextNodeIDs) == 1 {
		// 更新执行上下文的流程数据
		execCtx.FlowData = currentFlowData
		return e.executeNodeRecursive(ctx, nextNodeIDs[0], nodeMap, nextNodes, execCtx, result, visited)
	}

	// 如果有多个后续节点，为每个分支创建独立的数据副本
	log.Printf("节点 %s 有 %d 个后续节点，创建并行分支", nodeID, len(nextNodeIDs))

	for i, nextNodeID := range nextNodeIDs {
		log.Printf("执行分支 %d: %s -> %s", i+1, nodeID, nextNodeID)

		// 为每个分支创建独立的执行上下文
		branchExecCtx := &base.ExecutionContext{
			FlowID:      execCtx.FlowID,
			ExecutionID: execCtx.ExecutionID,
			NodeResults: execCtx.NodeResults,
			FlowData:    e.deepCopyFlowData(currentFlowData), // 深拷贝数据
			StartTime:   execCtx.StartTime,
			CurrentStep: execCtx.CurrentStep,
		}

		// 为每个分支创建独立的visited映射
		branchVisited := make(map[string]bool)
		for k, v := range visited {
			branchVisited[k] = v
		}

		// 递归执行分支
		err := e.executeNodeRecursive(ctx, nextNodeID, nodeMap, nextNodes, branchExecCtx, result, branchVisited)
		if err != nil {
			log.Printf("分支 %d 执行失败: %v", i+1, err)
			return err
		}
	}

	return nil
}

// deepCopyFlowData 深拷贝流程数据
func (e *FlowExecutor) deepCopyFlowData(original map[string]interface{}) map[string]interface{} {
	if original == nil {
		return nil
	}

	copy := make(map[string]interface{})
	for key, value := range original {
		switch v := value.(type) {
		case map[string]interface{}:
			copy[key] = e.deepCopyFlowData(v)
		case []interface{}:
			copySlice := make([]interface{}, len(v))
			for i, item := range v {
				if itemMap, ok := item.(map[string]interface{}); ok {
					copySlice[i] = e.deepCopyFlowData(itemMap)
				} else {
					copySlice[i] = item
				}
			}
			copy[key] = copySlice
		default:
			copy[key] = value
		}
	}
	return copy
}

// executeNodeWithDataReturn 执行单个节点并返回更新后的数据
func (e *FlowExecutor) executeNodeWithDataReturn(ctx context.Context, nodeID string, nodeMap map[string]*base.FlowNode,
	execCtx *base.ExecutionContext, result *ExecutionResult) (bool, map[string]interface{}, error) {

	shouldContinue, err := e.executeNode(ctx, nodeID, nodeMap, execCtx, result)
	return shouldContinue, execCtx.FlowData, err
}

// executeNode 执行单个节点
func (e *FlowExecutor) executeNode(ctx context.Context, nodeID string, nodeMap map[string]*base.FlowNode,
	execCtx *base.ExecutionContext, result *ExecutionResult) (bool, error) {

	flowNode, exists := nodeMap[nodeID]
	if !exists {
		return false, fmt.Errorf("node %s not found in flow definition", nodeID)
	}

	log.Printf("执行节点: %s (%s)", nodeID, flowNode.Text)

	// 创建执行步骤
	step := ExecutionStep{
		StepID:    fmt.Sprintf("step_%d", len(result.Steps)+1),
		NodeID:    nodeID,
		NodeType:  flowNode.Type,
		NodeName:  flowNode.Text,
		StartTime: time.Now().UnixMilli(),
		Status:    "running",
	}

	// 检查是否是预置节点
	if isPresetNode(flowNode) {
		return e.executePresetNode(ctx, flowNode, execCtx, result, &step)
	}

	// 处理基础节点（开始、结束等）
	return e.executeBasicNode(ctx, flowNode, execCtx, result, &step)
}

// isPresetNode 检查是否是预置节点
func isPresetNode(flowNode *base.FlowNode) bool {
	if flowNode.Properties == nil {
		return false
	}

	isPreset, ok := flowNode.Properties["isPresetNode"].(bool)
	return ok && isPreset
}

// executePresetNode 执行预置节点
func (e *FlowExecutor) executePresetNode(ctx context.Context, flowNode *base.FlowNode,
	execCtx *base.ExecutionContext, result *ExecutionResult, step *ExecutionStep) (shouldContinue bool, finalErr error) {

	// 添加节点级别的panic恢复
	defer func() {
		if r := recover(); r != nil {
			log.Printf("🚨 预置节点执行发生panic: 节点=%s(%s), panic=%v", flowNode.Text, flowNode.ID, r)

			// 记录panic信息到步骤中
			panicMsg := fmt.Sprintf("节点执行panic: %v", r)
			e.completeStepWithError(step, result, panicMsg)

			// 设置返回值
			shouldContinue = false
			finalErr = fmt.Errorf("节点 %s 执行panic: %v", flowNode.Text, r)
		}
	}()

	// 获取预置节点ID
	presetNodeID, ok := flowNode.Properties["presetNodeId"].(string)
	if !ok {
		e.completeStepWithError(step, result, "preset node ID not found")
		return false, fmt.Errorf("preset node ID not found")
	}

	// 获取表单数据
	formData, ok := flowNode.Properties["formData"].(map[string]interface{})
	if !ok {
		formData = make(map[string]interface{})
	}

	step.Input = formData

	// 获取节点实例
	nodeInstance, err := e.nodeRegistry.GetNode(presetNodeID)
	if err != nil {
		e.completeStepWithError(step, result, fmt.Sprintf("failed to get preset node: %v", err))
		return false, fmt.Errorf("failed to get preset node: %v", err)
	}

	// 获取上一个节点的输出（用于调试）
	var prevOutput map[string]interface{}
	if len(result.NodeResults) > 0 {
		// 简单起见，使用最后一个节点的输出
		for _, output := range result.NodeResults {
			if output.Success {
				prevOutput = output.Data
				break
			}
		}
	}

	// 构造执行输入
	input := &base.ExecutionInput{
		NodeID:      flowNode.ID,
		Params:      formData,
		FlowData:    execCtx.FlowData, // 传递共享的流程数据
		PrevOutput:  prevOutput,
		FlowID:      execCtx.FlowID,
		ExecutionID: execCtx.ExecutionID,
		CurrentStep: execCtx.CurrentStep,
	}

	log.Printf("执行预置节点: %s, 参数: %v", presetNodeID, formData)
	log.Printf("当前流程数据: %v", execCtx.FlowData)
	log.Printf("节点 %s 开始执行，数据地址: %p", flowNode.ID, execCtx.FlowData)

	// 执行节点
	output, err := nodeInstance.Execute(ctx, input)
	if err != nil {
		e.completeStepWithError(step, result, fmt.Sprintf("node execution failed: %v", err))
		return false, fmt.Errorf("node execution failed: %v", err)
	}

	// 保存结果
	result.NodeResults[flowNode.ID] = output
	step.Output = output.Data

	if output.Success {
		step.Status = "completed"

		// 更新流程共享数据
		if output.FlowData != nil {
			execCtx.FlowData = output.FlowData
			log.Printf("节点 %s 执行完成，数据已更新，数据地址: %p", flowNode.ID, execCtx.FlowData)
			log.Printf("更新后的流程数据: %v", execCtx.FlowData)
		}

		log.Printf("节点执行成功: %s", output.Message)

		// 检查是否是迭代器节点
		if presetNodeID == "iterator" {
			log.Printf("检测到迭代器节点，将进行特殊处理")
			step.EndTime = time.Now().UnixMilli()
			step.Duration = step.EndTime - step.StartTime
			result.Steps = append(result.Steps, *step)

			// 迭代器节点需要特殊处理，返回特殊标记
			// 实际的迭代逻辑将在调用方处理
			return true, nil
		}

		// 检查是否继续执行
		shouldContinue := output.Continue
		if !shouldContinue {
			log.Printf("节点 %s 指示停止执行后续节点", presetNodeID)
		}

		step.EndTime = time.Now().UnixMilli()
		step.Duration = step.EndTime - step.StartTime
		result.Steps = append(result.Steps, *step)

		return shouldContinue, nil
	} else {
		step.Status = "failed"
		step.Error = output.Error
		log.Printf("节点执行失败: %s", output.Error)

		step.EndTime = time.Now().UnixMilli()
		step.Duration = step.EndTime - step.StartTime
		result.Steps = append(result.Steps, *step)

		// 节点执行失败，停止流程
		return false, fmt.Errorf("node execution failed: %s", output.Error)
	}
}

// executeBasicNode 执行基础节点
func (e *FlowExecutor) executeBasicNode(ctx context.Context, flowNode *base.FlowNode,
	execCtx *base.ExecutionContext, result *ExecutionResult, step *ExecutionStep) (bool, error) {

	log.Printf("执行基础节点: %s (%s)", flowNode.Type, flowNode.Text)

	// 基础节点的简单处理
	switch flowNode.Type {
	case "circle":
		if flowNode.Text == "开始" || flowNode.Text == "start" {
			step.Status = "completed"
			step.Output = map[string]interface{}{
				"message": "流程开始",
				"nodeId":  flowNode.ID,
			}
		} else {
			step.Status = "completed"
			step.Output = map[string]interface{}{
				"message": "流程结束",
				"nodeId":  flowNode.ID,
			}
			// 设置最终输出
			result.FinalOutput = execCtx.FlowData
		}
	default:
		step.Status = "completed"
		step.Output = map[string]interface{}{
			"message": fmt.Sprintf("节点 %s 执行完成", flowNode.Text),
			"nodeId":  flowNode.ID,
		}
	}

	step.EndTime = time.Now().UnixMilli()
	step.Duration = step.EndTime - step.StartTime
	result.Steps = append(result.Steps, *step)

	// 基础节点默认继续执行
	return true, nil
}

// completeStepWithError 完成步骤并设置错误
func (e *FlowExecutor) completeStepWithError(step *ExecutionStep, result *ExecutionResult, errorMsg string) {
	step.Status = "failed"
	step.Error = errorMsg
	step.EndTime = time.Now().UnixMilli()
	step.Duration = step.EndTime - step.StartTime
	result.Steps = append(result.Steps, *step)
}

// isIteratorNode 检查是否是迭代器节点
func (e *FlowExecutor) isIteratorNode(nodeID string, nodeMap map[string]*base.FlowNode, result *ExecutionResult) bool {
	flowNode, exists := nodeMap[nodeID]
	if !exists {
		return false
	}

	// 检查是否是预置节点
	if !isPresetNode(flowNode) {
		return false
	}

	// 获取预置节点ID
	presetNodeID, ok := flowNode.Properties["presetNodeId"].(string)
	if !ok {
		return false
	}

	return presetNodeID == "iterator"
}

// handleIteratorNode 处理迭代器节点
func (e *FlowExecutor) handleIteratorNode(ctx context.Context, nodeID string, nodeMap map[string]*base.FlowNode,
	nextNodes map[string][]string, execCtx *base.ExecutionContext, result *ExecutionResult, visited map[string]bool) error {

	// 获取迭代器节点的执行结果
	nodeResult, exists := result.NodeResults[nodeID]
	if !exists {
		return fmt.Errorf("iterator node result not found")
	}

	// 从元数据中获取迭代信息
	metadata := nodeResult.Metadata
	if metadata == nil {
		return fmt.Errorf("iterator node metadata not found")
	}

	arrayData, ok := metadata["arrayData"].([]interface{})
	if !ok {
		return fmt.Errorf("iterator array data not found")
	}

	_, ok = metadata["arrayField"].(string)
	if !ok {
		return fmt.Errorf("iterator arrayField not found")
	}

	outputField, ok := metadata["outputField"].(string)
	if !ok {
		return fmt.Errorf("iterator outputField not found")
	}

	errorHandling, ok := metadata["errorHandling"].(string)
	if !ok {
		errorHandling = "continue" // 默认值
	}

	log.Printf("迭代器节点处理 - 数组长度: %d, 输出字段: %s, 错误处理: %s",
		len(arrayData), outputField, errorHandling)

	// 获取下一个节点列表
	nextNodeIDs, exists := nextNodes[nodeID]
	if !exists || len(nextNodeIDs) == 0 {
		log.Printf("迭代器节点 %s 没有后续节点，跳过迭代", nodeID)
		return nil
	}

	// 记录迭代统计信息
	successCount := 0
	failureCount := 0
	var lastError error

	// 对数组中的每个元素进行迭代
	for i, element := range arrayData {
		log.Printf("开始迭代第 %d/%d 个元素", i+1, len(arrayData))

		// 为当前迭代创建独立的流程数据副本
		iterationFlowData := e.deepCopyFlowData(execCtx.FlowData)

		/*TODO // 移除原数组字段（支持JSON路径）
		if !e.deleteFieldValue(iterationFlowData, arrayField) {
			// 如果JSON路径删除失败，尝试直接删除（兼容性）
			delete(iterationFlowData, arrayField)
		}

		// 将当前元素放入输出字段（深拷贝避免引用修改）
		elementCopy := e.deepCopyElement(element)
		if !e.setFieldValue(iterationFlowData, outputField, elementCopy) {
			iterationFlowData[outputField] = elementCopy
		}*/
		if !e.setFieldValue(iterationFlowData, outputField, element) {
			iterationFlowData[outputField] = element
		}

		// 为当前迭代创建独立的执行上下文
		iterationExecCtx := &base.ExecutionContext{
			FlowID:      execCtx.FlowID,
			ExecutionID: execCtx.ExecutionID,
			NodeResults: execCtx.NodeResults,
			FlowData:    iterationFlowData,
			StartTime:   execCtx.StartTime,
			CurrentStep: execCtx.CurrentStep,
		}

		// 为当前迭代创建独立的visited映射
		iterationVisited := make(map[string]bool)
		for k, v := range visited {
			iterationVisited[k] = v
		}

		// 执行后续节点（支持多个分支）
		iterationError := e.executeIterationBranches(ctx, nextNodeIDs, nodeMap, nextNodes, iterationExecCtx, result, iterationVisited, i+1)

		if iterationError != nil {
			failureCount++
			lastError = iterationError
			log.Printf("迭代第 %d 个元素失败: %v", i+1, iterationError)

			// 根据错误处理策略决定是否继续
			if errorHandling == "stop" {
				log.Printf("错误处理策略为停止，终止迭代")
				return fmt.Errorf("iteration failed at element %d: %v", i+1, iterationError)
			}
			log.Printf("错误处理策略为继续，继续下一个元素")
		} else {
			successCount++
			log.Printf("迭代第 %d 个元素成功", i+1)
		}
	}

	log.Printf("迭代完成 - 成功: %d, 失败: %d", successCount, failureCount)

	// 如果所有迭代都失败且错误处理策略是继续，仍然返回最后一个错误
	if failureCount > 0 && successCount == 0 && errorHandling == "continue" {
		log.Printf("所有迭代都失败，返回最后一个错误")
		return lastError
	}

	return nil
}

// executeIterationBranches 执行迭代分支
func (e *FlowExecutor) executeIterationBranches(ctx context.Context, nextNodeIDs []string, nodeMap map[string]*base.FlowNode,
	nextNodes map[string][]string, execCtx *base.ExecutionContext, result *ExecutionResult, visited map[string]bool, iterationIndex int) error {

	if len(nextNodeIDs) == 1 {
		// 只有一个后续节点，直接执行
		log.Printf("迭代 %d: 执行单个后续节点 %s", iterationIndex, nextNodeIDs[0])
		return e.executeNodeRecursive(ctx, nextNodeIDs[0], nodeMap, nextNodes, execCtx, result, visited)
	}

	// 有多个后续节点，为每个分支创建独立的数据副本
	log.Printf("迭代 %d: 有 %d 个后续节点，创建并行分支", iterationIndex, len(nextNodeIDs))

	for i, nextNodeID := range nextNodeIDs {
		log.Printf("迭代 %d: 执行分支 %d: %s", iterationIndex, i+1, nextNodeID)

		// 为每个分支创建独立的执行上下文
		branchExecCtx := &base.ExecutionContext{
			FlowID:      execCtx.FlowID,
			ExecutionID: execCtx.ExecutionID,
			NodeResults: execCtx.NodeResults,
			FlowData:    e.deepCopyFlowData(execCtx.FlowData), // 深拷贝数据
			StartTime:   execCtx.StartTime,
			CurrentStep: execCtx.CurrentStep,
		}

		// 为每个分支创建独立的visited映射
		branchVisited := make(map[string]bool)
		for k, v := range visited {
			branchVisited[k] = v
		}

		// 递归执行分支
		err := e.executeNodeRecursive(ctx, nextNodeID, nodeMap, nextNodes, branchExecCtx, result, branchVisited)
		if err != nil {
			log.Printf("迭代 %d: 分支 %d 执行失败: %v", iterationIndex, i+1, err)
			return err
		}
	}

	return nil
}
