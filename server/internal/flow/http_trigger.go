package flow

import (
	"fmt"
	"log"
	"sec-flow-server/internal/constants"
	"sec-flow-server/internal/model"
	"sec-flow-server/internal/service"
	"sync"
)

// HttpTriggerService HTTP触发器服务
type HttpTriggerService struct {
	flowService  *service.FlowService
	flowExecutor *FlowExecutor
	keyFlowMap   map[string]*HttpTriggerFlow // key -> flow mapping
	mutex        sync.RWMutex
}

// HttpTriggerFlow HTTP触发的流程信息
type HttpTriggerFlow struct {
	FlowID      string                 `json:"flowId"`
	FlowName    string                 `json:"flowName"`
	Key         string                 `json:"key"`
	StartNodeID string                 `json:"startNodeId"`
	NodeConfig  map[string]interface{} `json:"nodeConfig"`
}

// NewHttpTriggerService 创建HTTP触发器服务
func NewHttpTriggerService(flowService *service.FlowService, flowExecutor *FlowExecutor) *HttpTriggerService {
	service := &HttpTriggerService{
		flowService:  flowService,
		flowExecutor: flowExecutor,
		keyFlowMap:   make(map[string]*HttpTriggerFlow),
	}

	// 初始化时加载所有已发布的HTTP触发流程
	service.RefreshHttpTriggers()

	return service
}

// RefreshHttpTriggers 刷新HTTP触发器映射
func (s *HttpTriggerService) RefreshHttpTriggers() {
	log.Println("🔄 刷新HTTP触发器映射...")

	// 获取所有已发布的流程
	publishedFlows, _, err := s.flowService.GetFlows(1, 1000, constants.FlowStatusPublished.String(), "", "")
	if err != nil {
		log.Printf("❌ 获取已发布流程失败: %v", err)
		return
	}

	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 清空现有映射
	s.keyFlowMap = make(map[string]*HttpTriggerFlow)

	// 扫描所有流程，查找包含 http_start 节点的流程
	for _, flowResp := range publishedFlows {
		httpStartNode := s.findHttpStartNode(flowResp.Data.Nodes)
		if httpStartNode == nil {
			continue
		}

		// 检查是否有入边（http_start 应该是开始节点）
		if s.hasIncomingEdges(httpStartNode.ID, flowResp.Data.Edges) {
			continue
		}

		// 获取配置（优先从 formData 中获取配置，兼容旧的 config 字段）
		var config map[string]interface{}
		if formData, ok := httpStartNode.Properties["formData"].(map[string]interface{}); ok {
			config = formData
		} else if nodeConfig, ok := httpStartNode.Properties["config"].(map[string]interface{}); ok {
			config = nodeConfig
		} else {
			log.Printf("⚠️  流程 [%s] 的 http_start 节点缺少配置", flowResp.Name)
			continue
		}

		key, ok := config["key"].(string)
		if !ok || key == "" {
			log.Printf("⚠️  流程 [%s] 的 http_start 节点缺少触发Key", flowResp.Name)
			continue
		}

		// 检查Key是否重复
		if existingFlow, exists := s.keyFlowMap[key]; exists {
			log.Printf("⚠️  触发Key [%s] 重复，流程 [%s] 将覆盖流程 [%s]",
				key, flowResp.Name, existingFlow.FlowName)
		}

		// 添加到映射
		triggerFlow := &HttpTriggerFlow{
			FlowID:      flowResp.ID,
			FlowName:    flowResp.Name,
			Key:         key,
			StartNodeID: httpStartNode.ID,
			NodeConfig:  config,
		}

		s.keyFlowMap[key] = triggerFlow
		log.Printf("📌 注册HTTP触发器: Key=%s, 流程=%s", key, flowResp.Name)
	}

	log.Printf("✅ HTTP触发器映射刷新完成，共注册 %d 个触发器", len(s.keyFlowMap))
}

// findHttpStartNode 查找 http_start 节点
func (s *HttpTriggerService) findHttpStartNode(nodes []model.Node) *model.Node {
	for i := range nodes {
		if nodes[i].Properties != nil {
			if presetNodeId, ok := nodes[i].Properties["presetNodeId"].(string); ok {
				if presetNodeId == "http_start" {
					return &nodes[i]
				}
			}
		}
	}
	return nil
}

// hasIncomingEdges 检查节点是否有入边
func (s *HttpTriggerService) hasIncomingEdges(nodeID string, edges []model.Edge) bool {
	for _, edge := range edges {
		if edge.TargetNodeId == nodeID {
			return true
		}
	}
	return false
}

// TriggerFlow 通过Key触发流程执行
func (s *HttpTriggerService) TriggerFlow(key string, requestData map[string]interface{}) (*ExecutionResult, error) {
	s.mutex.RLock()
	triggerFlow, exists := s.keyFlowMap[key]
	s.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("未找到Key为 '%s' 的HTTP触发器", key)
	}

	log.Printf("🎯 触发HTTP流程: Key=%s, 流程=%s", key, triggerFlow.FlowName)

	// 准备执行请求
	req := &ExecutionRequest{
		FlowID:      triggerFlow.FlowID,
		StartNodeID: triggerFlow.StartNodeID,
		InputData:   make(map[string]interface{}),
	}

	// 将HTTP请求数据添加到流程数据中
	if requestData != nil {
		req.InputData["httpRequest"] = requestData
	}

	// 执行流程并记录
	result, err := s.flowExecutor.ExecuteFlowWithRecord(
		nil,
		req,
		constants.TriggerTypeHTTP,
		fmt.Sprintf("http-key-%s", key),
	)
	if err != nil {
		log.Printf("❌ HTTP触发流程执行失败 [%s]: %v", triggerFlow.FlowName, err)
		return nil, fmt.Errorf("流程执行失败: %v", err)
	}

	log.Printf("✅ HTTP触发流程执行完成 [%s]: 状态=%s, 执行ID=%s",
		triggerFlow.FlowName, result.Status, result.ExecutionID)

	return result, nil
}

// GetRegisteredTriggers 获取所有注册的HTTP触发器
func (s *HttpTriggerService) GetRegisteredTriggers() []*HttpTriggerFlow {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	triggers := make([]*HttpTriggerFlow, 0, len(s.keyFlowMap))
	for _, trigger := range s.keyFlowMap {
		triggers = append(triggers, trigger)
	}

	return triggers
}

// GetTriggerByKey 根据Key获取触发器信息
func (s *HttpTriggerService) GetTriggerByKey(key string) (*HttpTriggerFlow, bool) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	trigger, exists := s.keyFlowMap[key]
	return trigger, exists
}

// ValidateKey 验证Key是否可用
func (s *HttpTriggerService) ValidateKey(key string, excludeFlowID string) error {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if trigger, exists := s.keyFlowMap[key]; exists {
		if excludeFlowID == "" || trigger.FlowID != excludeFlowID {
			return fmt.Errorf("触发Key '%s' 已被流程 '%s' 使用", key, trigger.FlowName)
		}
	}

	return nil
}
