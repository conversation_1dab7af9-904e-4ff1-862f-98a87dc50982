package database

import (
	"fmt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
	"log"
	"sec-flow-server/internal/config"
	"sec-flow-server/internal/model"
)

var DB *gorm.DB

// InitDatabase 初始化数据库连接
func InitDatabase() error {
	cfg := config.Get()
	if cfg == nil {
		return fmt.Errorf("配置未加载")
	}

	// 使用配置中的数据库连接信息
	dsn := cfg.GetDSN()

	// 设置日志级别
	var logLevel logger.LogLevel

	// 优先使用数据库专门的日志级别配置
	dbLogLevel := cfg.Database.LogLevel
	if dbLogLevel == "" {
		// 如果没有设置数据库日志级别，则使用应用日志级别
		dbLogLevel = cfg.Log.Level
	}

	switch dbLogLevel {
	case "silent":
		logLevel = logger.Silent
	case "error":
		logLevel = logger.Error
	case "warn":
		logLevel = logger.Warn
	case "info":
		logLevel = logger.Info
	case "debug":
		logLevel = logger.Info
	default:
		logLevel = logger.Silent // 默认为静默模式
	}

	var err error
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   "tb_",
			SingularTable: false,
		},
	})

	if err != nil {
		return fmt.Errorf("failed to connect to database: %v", err)
	}

	// 配置连接池
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get database instance: %v", err)
	}

	sqlDB.SetMaxIdleConns(cfg.Database.MaxIdleConns)
	sqlDB.SetMaxOpenConns(cfg.Database.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(cfg.Database.ConnMaxLifetime)

	log.Printf("📊 Database connected successfully: %s:%d/%s",
		cfg.Database.Host, cfg.Database.Port, cfg.Database.Database)

	// 检查是否启用自动迁移（生产环境建议关闭）
	if cfg.Database.AutoMigrate {
		log.Println("🔄 Auto migration enabled, migrating database...")
		err = AutoMigrate()
		if err != nil {
			return fmt.Errorf("failed to migrate database: %v", err)
		}
		log.Println("📋 Database migration completed")
	} else {
		log.Println("⚠️  Auto migration disabled, skipping database migration")
		log.Println("💡 Please ensure database tables are created manually")
	}

	return nil
}

// AutoMigrate 自动迁移数据表
func AutoMigrate() error {
	return DB.AutoMigrate(
		&model.Flow{},
		&model.Template{},
		&model.User{},
		&model.Role{},
		&model.Permission{},
		&model.UserRole{},
		&model.RolePermission{},
		&model.DistributedLock{},
		&model.FlowExecution{},
		&model.EventCategory{},
		&model.EventOperation{},
		&model.EventSuppressionRecord{},
	)
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}
