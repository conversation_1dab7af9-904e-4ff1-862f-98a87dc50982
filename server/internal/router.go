package internal

import (
	"sec-flow-server/internal/config"
	"sec-flow-server/internal/flow"
	"sec-flow-server/internal/handler"
	"sec-flow-server/internal/middleware"
	"sec-flow-server/internal/scheduler"
	"sec-flow-server/internal/service"
	"sec-flow-server/pkg/response"

	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

// SetupRoutes 设置所有路由
func SetupRoutes(r *gin.Engine, authMiddleware *middleware.AuthMiddleware, permissionMiddleware *middleware.PermissionMiddleware, flowScheduler *scheduler.FlowScheduler, cleanupScheduler *scheduler.CleanupScheduler, httpTriggerService *flow.HttpTriggerService, lockService *service.DistributedLockService) {
	// 获取配置
	cfg := config.Get()

	// 健康检查接口
	r.GET("/health", func(c *gin.Context) {
		response.Success(c, gin.H{
			"status":  "ok",
			"message": cfg.App.Name + " is running",
			"version": cfg.App.Version,
			"env":     cfg.App.Env,
		})
	})

	// 添加 /sec-flow 前缀的路由组
	secFlow := r.Group("/sec-flow")
	{
		// 认证相关路由（不需要认证）
		authHandler := handler.NewAuthHandler()
		auth := secFlow.Group("/api/v1/auth")
		{
			auth.POST("/login", authHandler.Login)   // 用户登录
			auth.POST("/logout", authHandler.Logout) // 用户登出
		}

		// API路由组
		api := secFlow.Group("/api/v1")
		{
			// 流程图相关接口
			flowHandler := handler.NewFlowHandler()
			flow := api.Group("/flows")
			{
				flow.GET("", flowHandler.GetFlows)                               // 获取流程图列表
				flow.GET("/with-executions", flowHandler.GetFlowsWithExecutions) // 获取带执行记录的流程列表
				flow.POST("", flowHandler.CreateFlow)                            // 创建流程图
				flow.GET("/:id", flowHandler.GetFlow)                            // 获取单个流程图
				flow.PUT("/:id", flowHandler.UpdateFlow)                         // 更新流程图
				flow.DELETE("/:id", flowHandler.DeleteFlow)                      // 删除流程图
				flow.POST("/:id/execute", flowHandler.ExecuteFlow)               // 执行流程图
				flow.GET("/:id/example", flowHandler.GetFlowExample)             // 获取流程示例数据
				flow.POST("/preview", flowHandler.PreviewFlow)                   // 预览流程数据
			}

			// 节点模板相关接口
			templateHandler := handler.NewTemplateHandler()
			template := api.Group("/templates")
			{
				template.GET("", templateHandler.GetTemplates)          // 获取模板列表
				template.POST("", templateHandler.CreateTemplate)       // 创建模板
				template.GET("/:id", templateHandler.GetTemplate)       // 获取单个模板
				template.PUT("/:id", templateHandler.UpdateTemplate)    // 更新模板
				template.DELETE("/:id", templateHandler.DeleteTemplate) // 删除模板
			}

			// 用户相关接口
			userHandler := handler.NewUserHandler()
			user := api.Group("/users", authMiddleware.JWTAuth())
			{
				// 当前用户相关接口（需要登录）
				user.GET("/profile", userHandler.GetProfile)             // 获取当前用户信息
				user.PUT("/profile", userHandler.UpdateProfile)          // 更新当前用户信息
				user.GET("/permissions", userHandler.GetUserPermissions) // 获取当前用户权限

				// 用户管理接口（需要用户管理权限）
				user.GET("", permissionMiddleware.RequirePermission("users", "read"), userHandler.GetUsers)                           // 获取用户列表
				user.GET("/:id", permissionMiddleware.RequirePermission("users", "read"), userHandler.GetUser)                        // 获取指定用户
				user.POST("", permissionMiddleware.RequirePermission("users", "write"), userHandler.CreateUser)                       // 创建用户
				user.PUT("/:id", permissionMiddleware.RequirePermission("users", "write"), userHandler.UpdateUser)                    // 更新用户
				user.DELETE("/:id", permissionMiddleware.RequirePermission("users", "delete"), userHandler.DeleteUser)                // 删除用户
				user.GET("/:id/permissions", permissionMiddleware.RequirePermission("users", "read"), userHandler.GetUserPermissions) // 获取指定用户权限
			}

			// 角色管理接口
			roleHandler := handler.NewRoleHandler()
			role := api.Group("/roles", authMiddleware.JWTAuth())
			{
				role.GET("", permissionMiddleware.RequirePermission("roles", "read"), roleHandler.GetRoles)            // 获取角色列表
				role.GET("/all", permissionMiddleware.RequirePermission("roles", "read"), roleHandler.GetAllRoles)     // 获取所有角色（下拉选择）
				role.GET("/:id", permissionMiddleware.RequirePermission("roles", "read"), roleHandler.GetRole)         // 获取单个角色
				role.POST("", permissionMiddleware.RequirePermission("roles", "write"), roleHandler.CreateRole)        // 创建角色
				role.PUT("/:id", permissionMiddleware.RequirePermission("roles", "write"), roleHandler.UpdateRole)     // 更新角色
				role.DELETE("/:id", permissionMiddleware.RequirePermission("roles", "delete"), roleHandler.DeleteRole) // 删除角色
			}

			// 权限管理接口
			permissionHandler := handler.NewPermissionHandler()
			permission := api.Group("/permissions", authMiddleware.JWTAuth())
			{
				permission.GET("", permissionMiddleware.RequirePermission("permissions", "read"), permissionHandler.GetPermissions)                       // 获取权限列表
				permission.GET("/all", permissionMiddleware.RequirePermission("permissions", "read"), permissionHandler.GetAllPermissions)                // 获取所有权限（下拉选择）
				permission.GET("/by-resource", permissionMiddleware.RequirePermission("permissions", "read"), permissionHandler.GetPermissionsByResource) // 根据资源获取权限
				permission.GET("/:id", permissionMiddleware.RequirePermission("permissions", "read"), permissionHandler.GetPermission)                    // 获取单个权限
				permission.POST("", permissionMiddleware.RequirePermission("permissions", "write"), permissionHandler.CreatePermission)                   // 创建权限
				permission.PUT("/:id", permissionMiddleware.RequirePermission("permissions", "write"), permissionHandler.UpdatePermission)                // 更新权限
				permission.DELETE("/:id", permissionMiddleware.RequirePermission("permissions", "delete"), permissionHandler.DeletePermission)            // 删除权限
			}

			// 预置节点相关接口
			presetNodeHandler := handler.NewPresetNodeHandler()
			presetNode := api.Group("/preset-nodes")
			{
				presetNode.GET("", presetNodeHandler.GetPresetNodes)                      // 获取预置节点列表
				presetNode.GET("/:id", presetNodeHandler.GetPresetNode)                   // 获取单个预置节点详情
				presetNode.POST("/instances", presetNodeHandler.CreatePresetNodeInstance) // 创建预置节点实例
			}

			// 调度器相关接口
			schedulerHandler := handler.NewSchedulerHandler(flowScheduler)
			schedulerGroup := api.Group("/scheduler")
			{
				schedulerGroup.GET("/scheduled-flows", schedulerHandler.GetScheduledFlows)     // 获取计划流程
				schedulerGroup.POST("/trigger-preprocess", schedulerHandler.TriggerPreprocess) // 手动触发预处理
			}

			// HTTP触发器相关接口
			httpTriggerHandler := handler.NewHttpTriggerHandler(httpTriggerService)
			httpTriggerGroup := api.Group("/http-triggers")
			{
				httpTriggerGroup.GET("", httpTriggerHandler.GetRegisteredTriggers)    // 获取所有HTTP触发器
				httpTriggerGroup.GET("/:key", httpTriggerHandler.GetTriggerByKey)     // 获取指定Key的触发器
				httpTriggerGroup.POST("/refresh", httpTriggerHandler.RefreshTriggers) // 刷新触发器映射
				httpTriggerGroup.POST("/validate", httpTriggerHandler.ValidateKey)    // 验证Key可用性
				httpTriggerGroup.POST("/test/:key", httpTriggerHandler.TestTrigger)   // 测试触发器
			}

			// 清理管理相关接口
			cleanupHandler := handler.NewCleanupHandler(cleanupScheduler)
			cleanupGroup := api.Group("/cleanup")
			{
				cleanupGroup.GET("/statistics", cleanupHandler.GetCleanupStatistics)     // 获取清理统计信息
				cleanupGroup.POST("/run-now", cleanupHandler.RunCleanupNow)              // 立即执行清理任务
				cleanupGroup.GET("/status", cleanupHandler.GetCleanupStatus)             // 获取清理调度器状态
				cleanupGroup.POST("/fix-stuck", cleanupHandler.FixStuckExecutions)       // 修复卡住的执行记录
				cleanupGroup.GET("/stuck-count", cleanupHandler.GetStuckExecutionsCount) // 获取卡住的执行记录数量
			}

			// 分布式锁相关接口
			lockHandler := handler.NewDistributedLockHandler(lockService)
			lockGroup := api.Group("/locks")
			{
				lockGroup.GET("/active", lockHandler.GetActiveLocks) // 获取当前实例的活跃锁
				lockGroup.GET("/status", lockHandler.GetLockStatus)  // 检查指定锁的状态
			}

			// 执行记录相关接口
			executionHandler := handler.NewFlowExecutionHandler()
			execution := api.Group("/executions")
			{
				execution.GET("", executionHandler.GetExecutions)                        // 获取执行记录列表
				execution.GET("/:id", executionHandler.GetExecution)                     // 获取执行记录详情
				execution.GET("/flow/:flowId", executionHandler.GetFlowExecutions)       // 获取指定流程的执行记录
				execution.DELETE("/flow/:flowId", executionHandler.DeleteFlowExecutions) // 删除指定流程的执行记录
			}

			// 事件分类相关接口
			eventCategoryHandler := handler.NewEventCategoryHandler()
			eventCategory := api.Group("/event-categories", authMiddleware.JWTAuth())
			{
				eventCategory.GET("", permissionMiddleware.RequirePermission("event_categories", "read"), eventCategoryHandler.GetCategories)                         // 获取事件分类列表
				eventCategory.GET("/options", permissionMiddleware.RequirePermission("event_categories", "read"), eventCategoryHandler.GetCategoryOptions)            // 获取分类选项
				eventCategory.GET("/:id", permissionMiddleware.RequirePermission("event_categories", "read"), eventCategoryHandler.GetCategory)                       // 获取单个事件分类
				eventCategory.POST("", permissionMiddleware.RequirePermission("event_categories", "write"), eventCategoryHandler.CreateCategory)                      // 创建事件分类
				eventCategory.PUT("/:id", permissionMiddleware.RequirePermission("event_categories", "write"), eventCategoryHandler.UpdateCategory)                   // 更新事件分类
				eventCategory.DELETE("/:id", permissionMiddleware.RequirePermission("event_categories", "delete"), eventCategoryHandler.DeleteCategory)               // 删除事件分类
				eventCategory.POST("/batch-delete", permissionMiddleware.RequirePermission("event_categories", "delete"), eventCategoryHandler.BatchDeleteCategories) // 批量删除
			}

			// 事件运营相关接口
			eventOperationHandler := handler.NewEventOperationHandler()
			eventOperation := api.Group("/event-operations", authMiddleware.JWTAuth())
			{
				eventOperation.GET("", permissionMiddleware.RequirePermission("event_operations", "read"), eventOperationHandler.GetOperations)                         // 获取事件运营列表
				eventOperation.GET("/process-types", permissionMiddleware.RequirePermission("event_operations", "read"), eventOperationHandler.GetProcessTypeOptions)   // 获取处理分类选项
				eventOperation.GET("/search-employees", permissionMiddleware.RequirePermission("event_operations", "read"), eventOperationHandler.SearchEmployees)      // 搜索员工
				eventOperation.GET("/:id", permissionMiddleware.RequirePermission("event_operations", "read"), eventOperationHandler.GetOperation)                      // 获取单个事件运营
				eventOperation.POST("", permissionMiddleware.RequirePermission("event_operations", "write"), eventOperationHandler.CreateOperation)                     // 创建事件运营
				eventOperation.PUT("/:id", permissionMiddleware.RequirePermission("event_operations", "write"), eventOperationHandler.UpdateOperation)                  // 更新事件运营
				eventOperation.DELETE("/:id", permissionMiddleware.RequirePermission("event_operations", "delete"), eventOperationHandler.DeleteOperation)              // 删除事件运营
				eventOperation.POST("/batch-delete", permissionMiddleware.RequirePermission("event_operations", "delete"), eventOperationHandler.BatchDeleteOperations) // 批量删除
				eventOperation.POST("/batch-update", permissionMiddleware.RequirePermission("event_operations", "write"), eventOperationHandler.BatchUpdateOperations)  // 批量更新
			}

			// HTTP流程执行接口（公开接口，不在/api/v1下）
			secFlow.POST("/api/execute/flow/:key", httpTriggerHandler.ExecuteFlow) // HTTP触发流程执行
		}
	}

	// 前端静态资源与SPA回退（可选，存在静态目录时启用）
	staticDir := resolveStaticDir()
	if staticDir != "" {
		// 静态资源目录：/sec-flow/assets
		assetsDir := filepath.Join(staticDir, "assets")
		if dirExists(assetsDir) {
			r.StaticFS("/sec-flow/assets", http.Dir(assetsDir))
		}

		// 显式暴露 /sec-flow/index.html
		indexFile := filepath.Join(staticDir, "index.html")
		if fileExists(indexFile) {
			r.StaticFile("/sec-flow/index.html", indexFile)
		}

		iconFile := filepath.Join(staticDir, "fav.png")
		if fileExists(iconFile) {
			r.StaticFile("/sec-flow/fav.png", iconFile)
		}

		icoFile := filepath.Join(staticDir, "favicon.ico")
		if fileExists(icoFile) {
			r.StaticFile("/sec-flow/favicon.ico", icoFile)
		}

		// 根路径重定向
		r.GET("/", func(c *gin.Context) { c.Redirect(http.StatusMovedPermanently, "/sec-flow/flows") })

		// 显式入口
		r.GET("/sec-flow/", func(c *gin.Context) {
			if fileExists(indexFile) {
				c.File(indexFile)
				return
			}
			c.Status(http.StatusNotFound)
		})

		// SPA 回退：使用 NoRoute 兜底，避免与已注册前缀冲突
		r.NoRoute(func(c *gin.Context) {
			p := c.Request.URL.Path
			if strings.HasPrefix(p, "/sec-flow/api/") {
				c.Status(http.StatusNotFound)
				return
			}
			if strings.HasPrefix(p, "/sec-flow/") || p == "/sec-flow" {
				if fileExists(indexFile) {
					c.File(indexFile)
					return
				}
			}
			c.Status(http.StatusNotFound)
		})
	}
}

// resolveStaticDir 返回可用的前端构建目录
func resolveStaticDir() string {
	if v := os.Getenv("STATIC_DIR"); v != "" && dirExists(v) {
		return v
	}
	if dirExists("/app/web") {
		return "/app/web"
	}
	if dirExists(filepath.Clean("../web/dist")) {
		return filepath.Clean("../web/dist")
	}
	if dirExists(filepath.Clean("./web/dist")) {
		return filepath.Clean("./web/dist")
	}
	return ""
}

func dirExists(path string) bool {
	info, err := os.Stat(path)
	if err != nil {
		return false
	}
	return info.IsDir()
}

func fileExists(path string) bool {
	info, err := os.Stat(path)
	if err != nil {
		return false
	}
	return !info.IsDir()
}
