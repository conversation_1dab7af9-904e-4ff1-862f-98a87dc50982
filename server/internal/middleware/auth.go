package middleware

import (
	"fmt"
	"sec-flow-server/internal/model"
	"sec-flow-server/internal/service"
	"sec-flow-server/pkg/response"

	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

// AuthMiddleware JWT认证中间件
type AuthMiddleware struct {
	userService *service.UserService
	jwtSecret   string
}

// NewAuthMiddleware 创建认证中间件
func NewAuthMiddleware(jwtSecret string) *AuthMiddleware {
	return &AuthMiddleware{
		userService: service.NewUserService(),
		jwtSecret:   jwtSecret,
	}
}

// JWTAuth JWT认证中间件函数
func (m *AuthMiddleware) JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从Cookie中获取token
		tokenString, err := c.<PERSON>("secgate_token")
		if err != nil {
			response.Unauthorized(c, "Missing authentication token")
			c.Abort()
			return
		}

		// 解析JWT token（仅允许HS256算法）
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			if token.Method != jwt.SigningMethodHS256 {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			return []byte(m.jwtSecret), nil
		})

		if err != nil {
			response.Unauthorized(c, "Invalid token")
			c.Abort()
			return
		}

		// 验证token有效性
		if !token.Valid {
			response.Unauthorized(c, "Invalid token")
			c.Abort()
			return
		}

		// 获取claims
		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			response.Unauthorized(c, "Invalid token claims")
			c.Abort()
			return
		}

		// 从payload中获取data.username
		data, ok := claims["data"].(map[string]interface{})
		if !ok {
			response.Unauthorized(c, "Invalid token data structure")
			c.Abort()
			return
		}

		username, ok := data["username"].(string)
		if !ok || username == "" {
			response.Unauthorized(c, "Username not found in token")
			c.Abort()
			return
		}

		// 获取或创建用户
		user, err := m.userService.GetOrCreateUserByUsername(username)
		if err != nil {
			response.ServerError(c, "Failed to get user information")
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user", user)
		c.Set("username", username)
		c.Set("userID", user.ID)

		c.Next()
	}
}

// PermissionMiddleware 权限检查中间件
type PermissionMiddleware struct {
	userService *service.UserService
}

// NewPermissionMiddleware 创建权限中间件
func NewPermissionMiddleware() *PermissionMiddleware {
	return &PermissionMiddleware{
		userService: service.NewUserService(),
	}
}

// RequirePermission 需要特定权限的中间件
func (m *PermissionMiddleware) RequirePermission(resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户信息
		userInterface, exists := c.Get("user")
		if !exists {
			response.Unauthorized(c, "User not authenticated")
			c.Abort()
			return
		}

		user, ok := userInterface.(*model.User)
		if !ok {
			response.ServerError(c, "Invalid user data")
			c.Abort()
			return
		}

		// 检查用户权限
		hasPermission, err := m.userService.CheckUserPermission(user.ID, resource, action)
		if err != nil {
			response.ServerError(c, "Failed to check permissions")
			c.Abort()
			return
		}

		if !hasPermission {
			response.Forbidden(c, "Insufficient permissions")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireRole 需要特定角色的中间件
func (m *PermissionMiddleware) RequireRole(roleName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户信息
		userInterface, exists := c.Get("user")
		if !exists {
			response.Unauthorized(c, "User not authenticated")
			c.Abort()
			return
		}

		user, ok := userInterface.(*model.User)
		if !ok {
			response.ServerError(c, "Invalid user data")
			c.Abort()
			return
		}

		// 检查用户角色
		hasRole, err := m.userService.CheckUserRole(user.ID, roleName)
		if err != nil {
			response.ServerError(c, "Failed to check role")
			c.Abort()
			return
		}

		if !hasRole {
			response.Forbidden(c, fmt.Sprintf("Role '%s' required", roleName))
			c.Abort()
			return
		}

		c.Next()
	}
}

// OptionalAuth 可选认证中间件（不强制要求认证）
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 尝试从Cookie中获取token
		tokenString, err := c.Cookie("secgate_token")
		if err != nil {
			// 没有token，继续执行但不设置用户信息
			c.Next()
			return
		}

		// 解析JWT token（仅允许HS256算法）
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			if token.Method != jwt.SigningMethodHS256 {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			return []byte(m.jwtSecret), nil
		})

		if err != nil || !token.Valid {
			// token无效，继续执行但不设置用户信息
			c.Next()
			return
		}

		// 获取claims并设置用户信息
		if claims, ok := token.Claims.(jwt.MapClaims); ok {
			if data, ok := claims["data"].(map[string]interface{}); ok {
				if username, ok := data["username"].(string); ok && username != "" {
					user, err := m.userService.GetOrCreateUserByUsername(username)
					if err == nil {
						c.Set("user", user)
						c.Set("username", username)
						c.Set("userID", user.ID)
					}
				}
			}
		}

		c.Next()
	}
}

// GetPermissionKey 生成权限键
func GetPermissionKey(resource, action string) string {
	return fmt.Sprintf("%s:%s", resource, action)
}

// ParsePermissionKey 解析权限键
func ParsePermissionKey(key string) (resource, action string) {
	parts := strings.Split(key, ":")
	if len(parts) == 2 {
		return parts[0], parts[1]
	}
	return "", ""
}
