package middleware

import (
	"log"
	"net/http"
	"runtime/debug"
	"sec-flow-server/pkg/response"
	"time"

	"github.com/gin-gonic/gin"
)

// ErrorHandler 错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 记录详细的panic信息
				timestamp := time.Now().Format("2006-01-02 15:04:05")
				stack := debug.Stack()

				log.Printf("🚨 =========================== PANIC RECOVERED ===========================")
				log.Printf("🚨 时间: %s", timestamp)
				log.Printf("🚨 请求: %s %s", c.Request.Method, c.Request.URL.Path)
				log.Printf("🚨 客户端IP: %s", c.ClientIP())
				log.Printf("🚨 User-Agent: %s", c.Request.UserAgent())
				log.Printf("🚨 Panic内容: %v", err)
				log.Printf("🚨 调用栈:")
				log.Printf("%s", stack)
				log.Printf("🚨 =====================================================================")

				// 如果是流程执行相关的API，记录更多上下文信息
				if c.Request.URL.Path == "/api/execute/flow" ||
					c.Request.URL.Path == "/api/v1/flows/execute" {
					log.Printf("🚨 这是流程执行API的panic，可能导致执行记录状态异常")
				}

				response.ServerError(c, "Internal server error")
				c.Abort()
			}
		}()

		c.Next()

		// 处理错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()
			log.Printf("❌ Request error: %s %s - %v", c.Request.Method, c.Request.URL.Path, err.Error())

			switch err.Type {
			case gin.ErrorTypeBind:
				response.BadRequest(c, "Invalid request parameters")
			case gin.ErrorTypePublic:
				response.Error(c, http.StatusBadRequest, err.Error())
			default:
				response.ServerError(c, "Internal server error")
			}
			c.Abort()
		}
	}
}
