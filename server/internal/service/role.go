package service

import (
	"fmt"
	"sec-flow-server/internal/database"
	"sec-flow-server/internal/model"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// RoleService 角色服务
type RoleService struct {
	db *gorm.DB
}

// NewRoleService 创建角色服务
func NewRoleService() *RoleService {
	return &RoleService{
		db: database.GetDB(),
	}
}

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	Name          string   `json:"name" binding:"required"`
	DisplayName   string   `json:"displayName" binding:"required"`
	Description   string   `json:"description"`
	IsDefault     bool     `json:"isDefault"`
	PermissionIDs []string `json:"permissionIds"`
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	Name          string   `json:"name"`
	DisplayName   string   `json:"displayName"`
	Description   string   `json:"description"`
	IsDefault     bool     `json:"isDefault"`
	PermissionIDs []string `json:"permissionIds"`
}

// RoleListRequest 角色列表请求
type RoleListRequest struct {
	Page     int    `form:"page"`
	PageSize int    `form:"pageSize"`
	Keyword  string `form:"keyword"`
}

// GetRoles 获取角色列表
func (s *RoleService) GetRoles(req *RoleListRequest) ([]model.Role, int64, error) {
	var roles []model.Role
	var total int64

	query := s.db.Model(&model.Role{}).Preload("Permissions")

	// 添加搜索条件
	if req.Keyword != "" {
		query = query.Where("name LIKE ? OR display_name LIKE ? OR description LIKE ?",
			"%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	offset := (req.Page - 1) * req.PageSize
	err = query.Offset(offset).Limit(req.PageSize).Find(&roles).Error

	return roles, total, err
}

// GetRole 获取单个角色
func (s *RoleService) GetRole(roleID string) (*model.Role, error) {
	var role model.Role
	err := s.db.Preload("Permissions").Preload("Users").
		Where("id = ?", roleID).First(&role).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("角色不存在")
		}
		return nil, fmt.Errorf("查询角色失败: %v", err)
	}

	return &role, nil
}

// CreateRole 创建角色
func (s *RoleService) CreateRole(req *CreateRoleRequest) (*model.Role, error) {
	// 检查角色名是否已存在
	var count int64
	s.db.Model(&model.Role{}).Where("name = ?", req.Name).Count(&count)
	if count > 0 {
		return nil, fmt.Errorf("角色名已存在")
	}

	// 验证权限ID
	if len(req.PermissionIDs) > 0 {
		var permissionCount int64
		s.db.Model(&model.Permission{}).Where("id IN ?", req.PermissionIDs).Count(&permissionCount)
		if int(permissionCount) != len(req.PermissionIDs) {
			return nil, fmt.Errorf("部分权限ID无效")
		}
	}

	role := &model.Role{
		ID:          uuid.New().String(),
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		IsDefault:   req.IsDefault,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 在事务中创建角色并分配权限
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// 如果设置为默认角色，先将其他角色的默认状态取消
		if req.IsDefault {
			if err := tx.Model(&model.Role{}).Where("is_default = ?", true).
				Update("is_default", false).Error; err != nil {
				return err
			}
		}

		// 创建角色
		if err := tx.Create(role).Error; err != nil {
			return err
		}

		// 分配权限
		if len(req.PermissionIDs) > 0 {
			for _, permissionID := range req.PermissionIDs {
				rolePermission := &model.RolePermission{
					RoleID:       role.ID,
					PermissionID: permissionID,
				}
				if err := tx.Create(rolePermission).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("创建角色失败: %v", err)
	}

	return s.GetRole(role.ID)
}

// UpdateRole 更新角色
func (s *RoleService) UpdateRole(roleID string, req *UpdateRoleRequest) (*model.Role, error) {
	var role model.Role
	err := s.db.Where("id = ?", roleID).First(&role).Error
	if err != nil {
		return nil, fmt.Errorf("角色不存在")
	}

	// 检查角色名是否已存在
	if req.Name != "" && req.Name != role.Name {
		var count int64
		s.db.Model(&model.Role{}).Where("name = ? AND id != ?", req.Name, roleID).Count(&count)
		if count > 0 {
			return nil, fmt.Errorf("角色名已存在")
		}
	}

	// 验证权限ID
	if len(req.PermissionIDs) > 0 {
		var permissionCount int64
		s.db.Model(&model.Permission{}).Where("id IN ?", req.PermissionIDs).Count(&permissionCount)
		if int(permissionCount) != len(req.PermissionIDs) {
			return nil, fmt.Errorf("部分权限ID无效")
		}
	}

	// 在事务中更新角色和权限
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 如果设置为默认角色，先将其他角色的默认状态取消
		if req.IsDefault && !role.IsDefault {
			if err := tx.Model(&model.Role{}).Where("is_default = ? AND id != ?", true, roleID).
				Update("is_default", false).Error; err != nil {
				return err
			}
		}

		// 更新角色信息
		if req.Name != "" {
			role.Name = req.Name
		}
		if req.DisplayName != "" {
			role.DisplayName = req.DisplayName
		}
		if req.Description != "" {
			role.Description = req.Description
		}
		role.IsDefault = req.IsDefault
		role.UpdatedAt = time.Now()

		if err := tx.Save(&role).Error; err != nil {
			return err
		}

		// 更新权限关联
		if len(req.PermissionIDs) >= 0 {
			// 删除原有权限关联
			if err := tx.Where("role_id = ?", roleID).Delete(&model.RolePermission{}).Error; err != nil {
				return err
			}

			// 添加新的权限关联
			for _, permissionID := range req.PermissionIDs {
				rolePermission := &model.RolePermission{
					RoleID:       roleID,
					PermissionID: permissionID,
				}
				if err := tx.Create(rolePermission).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("更新角色失败: %v", err)
	}

	return s.GetRole(roleID)
}

// DeleteRole 删除角色
func (s *RoleService) DeleteRole(roleID string) error {
	// 检查角色是否存在
	var role model.Role
	err := s.db.Where("id = ?", roleID).First(&role).Error
	if err != nil {
		return fmt.Errorf("角色不存在")
	}

	// 检查是否有用户使用该角色
	var userCount int64
	s.db.Model(&model.UserRole{}).Where("role_id = ?", roleID).Count(&userCount)
	if userCount > 0 {
		return fmt.Errorf("该角色正在被使用，无法删除")
	}

	// 在事务中删除角色和相关关联
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 删除角色权限关联
		if err := tx.Where("role_id = ?", roleID).Delete(&model.RolePermission{}).Error; err != nil {
			return err
		}

		// 删除角色
		if err := tx.Delete(&role).Error; err != nil {
			return err
		}

		return nil
	})
}

// GetDefaultRole 获取默认角色
func (s *RoleService) GetDefaultRole() (*model.Role, error) {
	var role model.Role
	err := s.db.Preload("Permissions").Where("is_default = ?", true).First(&role).Error
	if err != nil {
		return nil, fmt.Errorf("未找到默认角色")
	}
	return &role, nil
}

// GetRolesByNames 根据角色名列表获取角色
func (s *RoleService) GetRolesByNames(roleNames []string) ([]model.Role, error) {
	var roles []model.Role
	err := s.db.Preload("Permissions").Where("name IN ?", roleNames).Find(&roles).Error
	return roles, err
}

// GetAllRoles 获取所有角色
func (s *RoleService) GetAllRoles() ([]model.Role, error) {
	var roles []model.Role
	err := s.db.Preload("Permissions").Find(&roles).Error
	return roles, err
}
