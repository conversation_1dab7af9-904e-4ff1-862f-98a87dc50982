package service

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"sec-flow-server/internal/config"
	thirdParty "sec-flow-server/internal/service/third_party"
	"testing"
	"time"
)

// findServerRoot resolves the server module root so config files are found
func findServerRoot() (string, error) {
	_, file, _, ok := runtime.Caller(0)
	if !ok {
		return "", fmt.<PERSON><PERSON>("unable to resolve caller path")
	}
	dir := filepath.Dir(file)
	for i := 0; i < 8; i++ {
		if filepath.Base(dir) == "server" {
			return dir, nil
		}
		parent := filepath.Dir(dir)
		if parent == dir {
			break
		}
		dir = parent
	}
	return "", fmt.Errorf("could not locate server root from %s", file)
}

func TestEmployeeServiceStruct_ScanEmployeeResigned(t *testing.T) {
	root, err := findServerRoot()
	if err != nil {
		t.Fatalf("findServerRoot error: %v", err)
	}
	if err := os.Ch<PERSON>(root); err != nil {
		t.Fatalf("chdir error: %v", err)
	}
	if os.Getenv("APP_ENV") == "" {
		_ = os.Setenv("APP_ENV", "local")
	}
	config.Load()
	thirdParty.InitThirdParty()
	executeDayStr := time.Now().Format("2006-01-02")
	resignedUsers, err := EmployeeService.ScanEmployeeResigned(executeDayStr)
	if err != nil {
		t.Errorf("ScanEmployeeResigned error: %v", err)
	}
	t.Logf("resigned users: %v", resignedUsers)

}
