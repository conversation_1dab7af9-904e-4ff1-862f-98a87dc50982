package service

import (
	"fmt"
	"log"
	"sec-flow-server/internal/database"
	"sec-flow-server/internal/model"
	"time"

	"gorm.io/gorm"
)

// ExecutionCleanupService 执行记录清理服务
type ExecutionCleanupService struct {
	db *gorm.DB
}

// NewExecutionCleanupService 创建执行记录清理服务
func NewExecutionCleanupService() *ExecutionCleanupService {
	return &ExecutionCleanupService{
		db: database.GetDB(),
	}
}

// CleanupExpiredExecutions 清理过期的执行记录
func (s *ExecutionCleanupService) CleanupExpiredExecutions() error {
	log.Printf("🧹 开始清理过期的执行记录...")

	// 获取所有流程的保存期限配置
	var flows []model.Flow
	err := s.db.Select("id, name, execution_retention_days").Find(&flows).Error
	if err != nil {
		return fmt.Errorf("failed to get flows: %v", err)
	}

	totalCleaned := 0
	for _, flow := range flows {
		cleaned, err := s.cleanupFlowExecutions(flow.ID, flow.Name, flow.ExecutionRetentionDays)
		if err != nil {
			log.Printf("❌ 清理流程 %s (%s) 的执行记录失败: %v", flow.Name, flow.ID, err)
			continue
		}
		totalCleaned += cleaned
	}

	log.Printf("✅ 执行记录清理完成，共清理 %d 条记录", totalCleaned)
	return nil
}

// cleanupFlowExecutions 清理指定流程的过期执行记录
func (s *ExecutionCleanupService) cleanupFlowExecutions(flowID, flowName string, retentionDays int) (int, error) {
	// 如果保存期限为0，表示永久保存，不清理
	if retentionDays <= 0 {
		log.Printf("📌 流程 %s (%s) 设置为永久保存，跳过清理", flowName, flowID)
		return 0, nil
	}

	// 计算过期时间
	expireTime := time.Now().AddDate(0, 0, -retentionDays)

	log.Printf("🔍 检查流程 %s (%s) 在 %s 之前的执行记录 (保存期限: %d天)",
		flowName, flowID, expireTime.Format("2006-01-02 15:04:05"), retentionDays)

	// 查询过期的执行记录数量
	var count int64
	err := s.db.Model(&model.FlowExecution{}).
		Where("flow_id = ? AND created_at < ?", flowID, expireTime).
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count expired executions: %v", err)
	}

	if count == 0 {
		log.Printf("✅ 流程 %s (%s) 没有过期的执行记录", flowName, flowID)
		return 0, nil
	}

	// 删除过期的执行记录
	result := s.db.Where("flow_id = ? AND created_at < ?", flowID, expireTime).
		Delete(&model.FlowExecution{})
	if result.Error != nil {
		return 0, fmt.Errorf("failed to delete expired executions: %v", result.Error)
	}

	deletedCount := int(result.RowsAffected)
	log.Printf("🗑️ 流程 %s (%s) 清理了 %d 条过期执行记录", flowName, flowID, deletedCount)

	return deletedCount, nil
}

// GetCleanupStatistics 获取清理统计信息
func (s *ExecutionCleanupService) GetCleanupStatistics() (*CleanupStatistics, error) {
	stats := &CleanupStatistics{
		FlowStats: make([]FlowCleanupStat, 0),
	}

	// 获取所有流程的统计信息
	var flows []model.Flow
	err := s.db.Select("id, name, execution_retention_days").Find(&flows).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get flows: %v", err)
	}

	for _, flow := range flows {
		stat, err := s.getFlowCleanupStat(flow)
		if err != nil {
			log.Printf("❌ 获取流程 %s 的清理统计失败: %v", flow.Name, err)
			continue
		}
		stats.FlowStats = append(stats.FlowStats, *stat)
		stats.TotalExecutions += stat.TotalExecutions
		stats.ExpiredExecutions += stat.ExpiredExecutions
	}

	return stats, nil
}

// getFlowCleanupStat 获取单个流程的清理统计
func (s *ExecutionCleanupService) getFlowCleanupStat(flow model.Flow) (*FlowCleanupStat, error) {
	stat := &FlowCleanupStat{
		FlowID:        flow.ID,
		FlowName:      flow.Name,
		RetentionDays: flow.ExecutionRetentionDays,
		PermanentSave: flow.ExecutionRetentionDays <= 0,
	}

	// 统计总执行记录数
	err := s.db.Model(&model.FlowExecution{}).
		Where("flow_id = ?", flow.ID).
		Count(&stat.TotalExecutions).Error
	if err != nil {
		return nil, err
	}

	// 如果是永久保存，过期记录数为0
	if flow.ExecutionRetentionDays <= 0 {
		stat.ExpiredExecutions = 0
		return stat, nil
	}

	// 统计过期执行记录数
	expireTime := time.Now().AddDate(0, 0, -flow.ExecutionRetentionDays)
	err = s.db.Model(&model.FlowExecution{}).
		Where("flow_id = ? AND created_at < ?", flow.ID, expireTime).
		Count(&stat.ExpiredExecutions).Error
	if err != nil {
		return nil, err
	}

	// 计算下次清理时间（明天凌晨）
	now := time.Now()
	nextCleanup := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
	stat.NextCleanupTime = &nextCleanup

	return stat, nil
}

// CleanupStatistics 清理统计信息
type CleanupStatistics struct {
	TotalExecutions   int64             `json:"totalExecutions"`   // 总执行记录数
	ExpiredExecutions int64             `json:"expiredExecutions"` // 过期执行记录数
	FlowStats         []FlowCleanupStat `json:"flowStats"`         // 各流程统计
}

// FlowCleanupStat 流程清理统计
type FlowCleanupStat struct {
	FlowID            string     `json:"flowId"`            // 流程ID
	FlowName          string     `json:"flowName"`          // 流程名称
	RetentionDays     int        `json:"retentionDays"`     // 保存天数
	PermanentSave     bool       `json:"permanentSave"`     // 是否永久保存
	TotalExecutions   int64      `json:"totalExecutions"`   // 总执行记录数
	ExpiredExecutions int64      `json:"expiredExecutions"` // 过期执行记录数
	NextCleanupTime   *time.Time `json:"nextCleanupTime"`   // 下次清理时间
}
