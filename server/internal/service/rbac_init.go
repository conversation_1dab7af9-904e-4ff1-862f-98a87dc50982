package service

import (
	"fmt"
	"log"
	"sec-flow-server/internal/constants"
	"sec-flow-server/internal/database"
	"sec-flow-server/internal/model"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// RBACInitService RBAC初始化服务
type RBACInitService struct {
	db                *gorm.DB
	permissionService *PermissionService
	roleService       *RoleService
}

// NewRBACInitService 创建RBAC初始化服务
func NewRBACInitService() *RBACInitService {
	return &RBACInitService{
		db:                database.GetDB(),
		permissionService: NewPermissionService(),
		roleService:       NewRoleService(),
	}
}

// InitRBAC 初始化RBAC系统
func (s *RBACInitService) InitRBAC() error {
	log.Println("🔐 开始初始化RBAC系统...")

	// 1. 初始化权限
	if err := s.initPermissions(); err != nil {
		return err
	}

	// 2. 初始化角色
	if err := s.initRoles(); err != nil {
		return err
	}

	// 3. 初始化默认admin用户
	if err := s.initDefaultAdminUser(); err != nil {
		return err
	}

	log.Println("✅ RBAC系统初始化完成")
	return nil
}

// initPermissions 初始化权限
func (s *RBACInitService) initPermissions() error {
	log.Println("📋 初始化权限...")

	// 收集所有权限定义
	allPermissions := []string{}
	allPermissions = append(allPermissions, constants.AdminPermissions...)
	allPermissions = append(allPermissions, constants.UserPermissions...)
	allPermissions = append(allPermissions, constants.ViewerPermissions...)
	allPermissions = append(allPermissions, constants.ExecutorPermissions...)

	// 去重
	permissionMap := make(map[string]bool)
	uniquePermissions := []string{}
	for _, perm := range allPermissions {
		if !permissionMap[perm] {
			permissionMap[perm] = true
			uniquePermissions = append(uniquePermissions, perm)
		}
	}

	// 批量创建权限
	return s.permissionService.CreatePermissionsFromKeys(uniquePermissions)
}

// initRoles 初始化角色
func (s *RBACInitService) initRoles() error {
	log.Println("👥 初始化角色...")

	roles := []struct {
		name        string
		displayName string
		description string
		isDefault   bool
		permissions []string
	}{
		{
			name:        constants.RoleAdmin,
			displayName: "系统管理员",
			description: "拥有系统所有权限的管理员角色",
			isDefault:   false,
			permissions: constants.AdminPermissions,
		},
		{
			name:        constants.RoleUser,
			displayName: "普通用户",
			description: "可以管理流程和模板的普通用户角色",
			isDefault:   true, // 设为默认角色
			permissions: constants.UserPermissions,
		},
		{
			name:        constants.RoleViewer,
			displayName: "只读用户",
			description: "只能查看信息的只读用户角色",
			isDefault:   false,
			permissions: constants.ViewerPermissions,
		},
		{
			name:        constants.RoleExecutor,
			displayName: "执行用户",
			description: "可以查看和执行流程的用户角色",
			isDefault:   false,
			permissions: constants.ExecutorPermissions,
		},
	}

	for _, roleData := range roles {
		if err := s.createRoleIfNotExists(roleData.name, roleData.displayName,
			roleData.description, roleData.isDefault, roleData.permissions); err != nil {
			log.Printf("❌ 创建角色 %s 失败: %v", roleData.name, err)
			continue
		}
		log.Printf("✅ 角色 %s 初始化完成", roleData.name)
	}

	return nil
}

// createRoleIfNotExists 如果角色不存在则创建
func (s *RBACInitService) createRoleIfNotExists(name, displayName, description string,
	isDefault bool, permissionKeys []string) error {

	// 检查角色是否已存在
	var existingRole model.Role
	err := s.db.Where("name = ?", name).First(&existingRole).Error
	if err == nil {
		// 角色已存在，更新权限
		return s.updateRolePermissions(existingRole.ID, permissionKeys)
	}

	if err != gorm.ErrRecordNotFound {
		return err
	}

	// 获取权限ID
	permissionIDs, err := s.getPermissionIDsByKeys(permissionKeys)
	if err != nil {
		return err
	}

	// 创建角色
	role := &model.Role{
		ID:          uuid.New().String(),
		Name:        name,
		DisplayName: displayName,
		Description: description,
		IsDefault:   isDefault,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// 如果设置为默认角色，先将其他角色的默认状态取消
		if isDefault {
			if err := tx.Model(&model.Role{}).Where("is_default = ?", true).
				Update("is_default", false).Error; err != nil {
				return err
			}
		}

		// 创建角色
		if err := tx.Create(role).Error; err != nil {
			return err
		}

		// 分配权限
		for _, permissionID := range permissionIDs {
			rolePermission := &model.RolePermission{
				RoleID:       role.ID,
				PermissionID: permissionID,
			}
			if err := tx.Create(rolePermission).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// updateRolePermissions 更新角色权限
func (s *RBACInitService) updateRolePermissions(roleID string, permissionKeys []string) error {
	// 获取权限ID
	permissionIDs, err := s.getPermissionIDsByKeys(permissionKeys)
	if err != nil {
		return err
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// 删除原有权限关联
		if err := tx.Where("role_id = ?", roleID).Delete(&model.RolePermission{}).Error; err != nil {
			return err
		}

		// 添加新的权限关联
		for _, permissionID := range permissionIDs {
			rolePermission := &model.RolePermission{
				RoleID:       roleID,
				PermissionID: permissionID,
			}
			if err := tx.Create(rolePermission).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// getPermissionIDsByKeys 根据权限键获取权限ID
func (s *RBACInitService) getPermissionIDsByKeys(permissionKeys []string) ([]string, error) {
	var permissionIDs []string

	for _, key := range permissionKeys {
		// 解析权限键
		colonIndex := -1
		for i, r := range key {
			if r == ':' {
				colonIndex = i
				break
			}
		}

		if colonIndex == -1 {
			continue
		}

		resource := key[:colonIndex]
		action := key[colonIndex+1:]

		// 查找权限
		var permission model.Permission
		err := s.db.Where("resource = ? AND action = ?", resource, action).First(&permission).Error
		if err != nil {
			log.Printf("⚠️  权限 %s 不存在，跳过", key)
			continue
		}

		permissionIDs = append(permissionIDs, permission.ID)
	}

	return permissionIDs, nil
}

// CheckAndInitRBAC 检查并初始化RBAC（如果需要）
func (s *RBACInitService) CheckAndInitRBAC() error {
	// 检查是否已经初始化过
	var permissionCount int64
	s.db.Model(&model.Permission{}).Count(&permissionCount)

	var roleCount int64
	s.db.Model(&model.Role{}).Count(&roleCount)

	// 如果权限或角色表为空，则需要初始化
	if permissionCount == 0 || roleCount == 0 {
		return s.InitRBAC()
	}

	// 检查是否有默认角色
	var defaultRoleCount int64
	s.db.Model(&model.Role{}).Where("is_default = ?", true).Count(&defaultRoleCount)
	if defaultRoleCount == 0 {
		log.Println("⚠️  未找到默认角色，重新初始化角色...")
		return s.initRoles()
	}

	log.Println("✅ RBAC系统已存在，跳过初始化")
	return nil
}

// initDefaultAdminUser 初始化默认admin用户
func (s *RBACInitService) initDefaultAdminUser() error {
	log.Println("👤 初始化默认admin用户...")

	// 检查admin用户是否已存在
	var user model.User
	err := s.db.Where("username = ?", "admin").First(&user).Error
	if err == nil {
		log.Println("✅ admin用户已存在，跳过创建")
		return nil
	}

	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("查询admin用户失败: %v", err)
	}

	// 获取admin角色
	var adminRole model.Role
	err = s.db.Where("name = ?", constants.RoleAdmin).First(&adminRole).Error
	if err != nil {
		return fmt.Errorf("未找到admin角色: %v", err)
	}

	// 创建admin用户
	adminUser := &model.User{
		ID:        uuid.New().String(),
		Username:  "admin",
		Email:     "<EMAIL>",
		Avatar:    "",
		Status:    "active",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 在事务中创建用户并分配admin角色
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 创建用户
		if err := tx.Create(adminUser).Error; err != nil {
			return err
		}

		// 分配admin角色
		userRole := &model.UserRole{
			UserID: adminUser.ID,
			RoleID: adminRole.ID,
		}
		if err := tx.Create(userRole).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return fmt.Errorf("创建admin用户失败: %v", err)
	}

	log.Println("✅ 默认admin用户创建成功")
	return nil
}
