package service

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"sec-flow-server/internal/config"
	"sec-flow-server/internal/database"
	"sec-flow-server/internal/model"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// DistributedLockService 分布式锁服务
type DistributedLockService struct {
	db         *gorm.DB
	instanceID string // 当前实例ID
}

// NewDistributedLockService 创建分布式锁服务
func NewDistributedLockService() *DistributedLockService {
	// 生成实例ID，可以使用主机名+UUID的组合
	hostname, _ := os.Hostname()
	instanceID := fmt.Sprintf("%s-%s", hostname, uuid.New().String()[:8])

	service := &DistributedLockService{
		db:         database.GetDB(),
		instanceID: instanceID,
	}

	// 启动锁清理任务

	log.Printf("🔒 分布式锁服务已启动，实例ID: %s", instanceID)
	return service
}

// GetInstanceID 获取当前实例ID
func (s *DistributedLockService) GetInstanceID() string {
	return s.instanceID
}

// TryLock 尝试获取锁
func (s *DistributedLockService) TryLock(lockType, resourceID string, duration time.Duration, metadata *model.LockMetadata) (*model.DistributedLock, error) {
	lockID := fmt.Sprintf("%s:%s", lockType, resourceID)
	expiresAt := time.Now().Add(duration)

	// 序列化元数据
	var metadataJSON string
	if metadata != nil {
		metadataBytes, err := json.Marshal(metadata)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal metadata: %v", err)
		}
		metadataJSON = string(metadataBytes)
	}

	lock := &model.DistributedLock{
		ID:         lockID,
		LockType:   lockType,
		ResourceID: resourceID,
		InstanceID: s.instanceID,
		ExpiresAt:  expiresAt,
		Metadata:   metadataJSON,
	}

	// 使用数据库事务确保原子性
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// 首先尝试清理过期锁
		err := tx.Where("id = ? AND expires_at < ?", lockID, time.Now()).Delete(&model.DistributedLock{}).Error
		if err != nil {
			return fmt.Errorf("failed to clean expired lock: %v", err)
		}

		// 尝试创建新锁
		err = tx.Create(lock).Error
		if err != nil {
			// 检查是否是因为锁已存在
			var existingLock model.DistributedLock
			if tx.Where("id = ?", lockID).First(&existingLock).Error == nil {
				if existingLock.ExpiresAt.After(time.Now()) {
					return fmt.Errorf("lock already held by instance %s, expires at %s",
						existingLock.InstanceID, existingLock.ExpiresAt.Format("15:04:05"))
				}
			}
			return fmt.Errorf("failed to create lock: %v", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	log.Printf("🔒 获取锁成功: %s (实例: %s, 过期: %s)",
		lockID, s.instanceID, expiresAt.Format("15:04:05"))
	return lock, nil
}

// ReleaseLock 释放锁
func (s *DistributedLockService) ReleaseLock(lockType, resourceID string) error {
	lockID := fmt.Sprintf("%s:%s", lockType, resourceID)

	result := s.db.Where("id = ? AND instance_id = ?", lockID, s.instanceID).Delete(&model.DistributedLock{})
	if result.Error != nil {
		return fmt.Errorf("failed to release lock: %v", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("lock not found or not owned by this instance")
	}

	log.Printf("🔓 释放锁成功: %s (实例: %s)", lockID, s.instanceID)
	return nil
}

// ExtendLock 延长锁的过期时间
func (s *DistributedLockService) ExtendLock(lockType, resourceID string, duration time.Duration) error {
	lockID := fmt.Sprintf("%s:%s", lockType, resourceID)
	newExpiresAt := time.Now().Add(duration)

	result := s.db.Model(&model.DistributedLock{}).
		Where("id = ? AND instance_id = ? AND expires_at > ?", lockID, s.instanceID, time.Now()).
		Update("expires_at", newExpiresAt)

	if result.Error != nil {
		return fmt.Errorf("failed to extend lock: %v", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("lock not found, expired, or not owned by this instance")
	}

	log.Printf("⏰ 延长锁成功: %s (实例: %s, 新过期时间: %s)",
		lockID, s.instanceID, newExpiresAt.Format("15:04:05"))
	return nil
}

// IsLockHeld 检查锁是否被持有
func (s *DistributedLockService) IsLockHeld(lockType, resourceID string) (bool, string, error) {
	lockID := fmt.Sprintf("%s:%s", lockType, resourceID)

	var lock model.DistributedLock
	err := s.db.Where("id = ? AND expires_at > ?", lockID, time.Now()).First(&lock).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, "", nil
		}
		return false, "", fmt.Errorf("failed to check lock: %v", err)
	}

	return true, lock.InstanceID, nil
}

// GetActiveLocks 获取当前实例持有的所有活跃锁
func (s *DistributedLockService) GetActiveLocks() ([]model.DistributedLock, error) {
	var locks []model.DistributedLock
	err := s.db.Where("instance_id = ? AND expires_at > ?", s.instanceID, time.Now()).Find(&locks).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get active locks: %v", err)
	}

	return locks, nil
}

// startLockCleanup 启动锁清理任务
func (s *DistributedLockService) startLockCleanup() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒清理一次过期锁
	defer ticker.Stop()

	for range ticker.C {
		s.cleanupExpiredLocks()
	}
}

// cleanupExpiredLocks 清理过期锁
func (s *DistributedLockService) cleanupExpiredLocks() {
	result := s.db.Where("expires_at < ?", time.Now()).Delete(&model.DistributedLock{})
	if result.Error != nil {
		log.Printf("⚠️  清理过期锁失败: %v", result.Error)
		return
	}

	if result.RowsAffected > 0 {
		log.Printf("🧹 清理了 %d 个过期锁", result.RowsAffected)
	}
}

// UpdateInstanceHeartbeat 更新实例心跳
func (s *DistributedLockService) UpdateInstanceHeartbeat(instanceID string) error {
	lockID := fmt.Sprintf("instance_heartbeat:%s", instanceID)
	cfg := config.Get()
	expiresAt := time.Now().Add(cfg.Scheduler.HeartbeatTimeout) // 心跳有效期

	metadata := &model.LockMetadata{}
	metadataJSON, _ := json.Marshal(metadata)

	// 使用原子 UPSERT（基于唯一键 id）
	lock := &model.DistributedLock{
		ID:         lockID,
		LockType:   "instance_heartbeat",
		ResourceID: instanceID,
		InstanceID: instanceID,
		ExpiresAt:  expiresAt,
		Metadata:   string(metadataJSON),
	}

	// Insert ... On Duplicate Key Update
	// 仅更新必要字段，避免全字段覆盖
	err := s.db.Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "id"}},
		DoUpdates: clause.Assignments(map[string]interface{}{
			"lock_type":   "instance_heartbeat",
			"resource_id": instanceID,
			"instance_id": instanceID,
			"expires_at":  expiresAt,
			"metadata":    string(metadataJSON),
		}),
	}).Create(lock).Error
	if err != nil {
		return fmt.Errorf("创建/更新心跳记录失败: %v", err)
	}

	return nil
}

// GetActiveInstances 获取活跃的实例列表
func (s *DistributedLockService) GetActiveInstances(heartbeatTimeout time.Duration) ([]string, error) {
	var locks []model.DistributedLock

	// 查询所有未过期的心跳记录
	cutoffTime := time.Now().Add(-heartbeatTimeout)
	err := s.db.Where("lock_type = ? AND expires_at > ?", "instance_heartbeat", cutoffTime).Find(&locks).Error
	if err != nil {
		return nil, fmt.Errorf("查询活跃实例失败: %v", err)
	}

	var instances []string
	for _, lock := range locks {
		instances = append(instances, lock.InstanceID)
	}

	return instances, nil
}

// LockWithMetadata 带有解析后元数据的锁信息
type LockWithMetadata struct {
	*model.DistributedLock
	Metadata *model.LockMetadata `json:"parsedMetadata"`
}

// GetLock 获取指定的锁信息
func (s *DistributedLockService) GetLock(lockType, resourceID string) (*LockWithMetadata, error) {
	lockID := fmt.Sprintf("%s:%s", lockType, resourceID)

	var lock model.DistributedLock
	err := s.db.Where("id = ?", lockID).First(&lock).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("查询锁失败: %v", err)
	}

	result := &LockWithMetadata{
		DistributedLock: &lock,
	}

	// 解析元数据
	if lock.Metadata != "" {
		var metadata model.LockMetadata
		if err := json.Unmarshal([]byte(lock.Metadata), &metadata); err == nil {
			result.Metadata = &metadata
		}
	}

	return result, nil
}

// CleanupExpiredHeartbeats 清理过期的心跳记录
func (s *DistributedLockService) CleanupExpiredHeartbeats() error {
	result := s.db.Where("lock_type = ? AND expires_at < ?", "instance_heartbeat", time.Now()).Delete(&model.DistributedLock{})
	if result.Error != nil {
		return fmt.Errorf("清理过期心跳失败: %v", result.Error)
	}

	if result.RowsAffected > 0 {
		log.Printf("🧹 清理了 %d 个过期心跳记录", result.RowsAffected)
	}

	return nil
}
