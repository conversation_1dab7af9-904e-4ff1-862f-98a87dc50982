package service

import (
	"fmt"
	"sec-flow-server/internal/database"
	"sec-flow-server/internal/model"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// EventCategoryService 事件分类服务
type EventCategoryService struct {
	db *gorm.DB
}

// NewEventCategoryService 创建事件分类服务
func NewEventCategoryService() *EventCategoryService {
	return &EventCategoryService{
		db: database.GetDB(),
	}
}

// GetCategories 获取事件分类列表
func (s *EventCategoryService) GetCategories(req *model.EventCategoryListRequest) ([]*model.EventCategory, int64, error) {
	var categories []*model.EventCategory
	var total int64

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	query := s.db.Model(&model.EventCategory{}).Preload("Owner")

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count event categories: %v", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	err = query.Order("created_at DESC").Offset(offset).Limit(req.PageSize).Find(&categories).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get event categories: %v", err)
	}

	return categories, total, nil
}

// GetCategory 获取单个事件分类
func (s *EventCategoryService) GetCategory(id string) (*model.EventCategory, error) {
	var category model.EventCategory
	err := s.db.Preload("Owner").Where("id = ?", id).First(&category).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("event category not found")
		}
		return nil, fmt.Errorf("failed to get event category: %v", err)
	}
	return &category, nil
}

// CreateCategory 创建事件分类
func (s *EventCategoryService) CreateCategory(req *model.EventCategoryCreateRequest) (*model.EventCategory, error) {
	// 检查名称是否已存在
	var count int64
	err := s.db.Model(&model.EventCategory{}).Where("name = ?", req.Name).Count(&count).Error
	if err != nil {
		return nil, fmt.Errorf("failed to check category name: %v", err)
	}
	if count > 0 {
		return nil, fmt.Errorf("category name already exists")
	}

	// 验证负责人是否存在
	var user model.User
	err = s.db.Where("id = ?", req.OwnerID).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("owner user not found")
		}
		return nil, fmt.Errorf("failed to verify owner: %v", err)
	}

	category := &model.EventCategory{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Description: req.Description,
		OwnerID:     req.OwnerID,
	}

	err = s.db.Create(category).Error
	if err != nil {
		return nil, fmt.Errorf("failed to create event category: %v", err)
	}

	// 重新查询以获取关联数据
	return s.GetCategory(category.ID)
}

// UpdateCategory 更新事件分类
func (s *EventCategoryService) UpdateCategory(id string, req *model.EventCategoryUpdateRequest) (*model.EventCategory, error) {
	// 检查分类是否存在
	var category model.EventCategory
	err := s.db.Where("id = ?", id).First(&category).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("event category not found")
		}
		return nil, fmt.Errorf("failed to get event category: %v", err)
	}

	// 检查名称是否已被其他分类使用
	var count int64
	err = s.db.Model(&model.EventCategory{}).Where("name = ? AND id != ?", req.Name, id).Count(&count).Error
	if err != nil {
		return nil, fmt.Errorf("failed to check category name: %v", err)
	}
	if count > 0 {
		return nil, fmt.Errorf("category name already exists")
	}

	// 验证负责人是否存在
	var user model.User
	err = s.db.Where("id = ?", req.OwnerID).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("owner user not found")
		}
		return nil, fmt.Errorf("failed to verify owner: %v", err)
	}

	// 更新分类
	updates := map[string]interface{}{
		"name":        req.Name,
		"description": req.Description,
		"owner_id":    req.OwnerID,
	}

	err = s.db.Model(&category).Updates(updates).Error
	if err != nil {
		return nil, fmt.Errorf("failed to update event category: %v", err)
	}

	// 重新查询以获取关联数据
	return s.GetCategory(id)
}

// DeleteCategory 删除事件分类
func (s *EventCategoryService) DeleteCategory(id string) error {
	// 检查分类是否存在
	var category model.EventCategory
	err := s.db.Where("id = ?", id).First(&category).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("event category not found")
		}
		return fmt.Errorf("failed to get event category: %v", err)
	}

	// 检查是否有关联的事件运营记录
	var count int64
	err = s.db.Model(&model.EventOperation{}).Where("category_id = ?", id).Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to check related event operations: %v", err)
	}
	if count > 0 {
		return fmt.Errorf("cannot delete category with related event operations")
	}

	// 删除分类
	err = s.db.Delete(&category).Error
	if err != nil {
		return fmt.Errorf("failed to delete event category: %v", err)
	}

	return nil
}

// BatchDeleteCategories 批量删除事件分类
func (s *EventCategoryService) BatchDeleteCategories(ids []string) error {
	if len(ids) == 0 {
		return fmt.Errorf("no category IDs provided")
	}

	// 检查是否有关联的事件运营记录
	var count int64
	err := s.db.Model(&model.EventOperation{}).Where("category_id IN ?", ids).Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to check related event operations: %v", err)
	}
	if count > 0 {
		return fmt.Errorf("cannot delete categories with related event operations")
	}

	// 批量删除
	err = s.db.Where("id IN ?", ids).Delete(&model.EventCategory{}).Error
	if err != nil {
		return fmt.Errorf("failed to batch delete event categories: %v", err)
	}

	return nil
}

// GetCategoryOptions 获取事件分类选项（用于下拉框）
func (s *EventCategoryService) GetCategoryOptions() ([]*model.EventCategoryOption, error) {
	var categories []*model.EventCategory
	err := s.db.Select("id, name").Order("name ASC").Find(&categories).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get category options: %v", err)
	}

	options := make([]*model.EventCategoryOption, len(categories))
	for i, category := range categories {
		options[i] = &model.EventCategoryOption{
			ID:   category.ID,
			Name: category.Name,
		}
	}

	return options, nil
}
