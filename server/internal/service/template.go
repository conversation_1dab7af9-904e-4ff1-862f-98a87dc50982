package service

import (
	"encoding/json"
	"fmt"
	"sec-flow-server/internal/database"
	"sec-flow-server/internal/model"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// TemplateService 模板服务
type TemplateService struct {
	db *gorm.DB
}

// NewTemplateService 创建模板服务
func NewTemplateService() *TemplateService {
	service := &TemplateService{
		db: database.GetDB(),
	}

	service.initSampleTemplatesIfEmpty()
	return service
}

// initSampleTemplatesIfEmpty 如果数据库为空则初始化示例模板
func (s *TemplateService) initSampleTemplatesIfEmpty() {
	var count int64
	s.db.Model(&model.Template{}).Count(&count)
	if count > 0 {
		return // 数据库已有数据，不需要初始化
	}
	// 准备模板配置
	config1 := map[string]interface{}{
		"color":     "#52c41a",
		"radius":    30,
		"textColor": "#fff",
		"fontSize":  12,
	}
	config1JSON, _ := json.<PERSON>(config1)

	templates := []*model.Template{
		{
			ID:          "template-1",
			Name:        "开始节点",
			Description: "流程开始节点模板",
			Category:    "basic",
			NodeType:    "circle",
			Icon:        "play-circle",
			Config:      string(config1JSON),
		},
		{
			ID:          "template-2",
			Name:        "处理节点",
			Description: "通用处理节点模板",
			Category:    "basic",
			NodeType:    "rect",
			Icon:        "setting",
			Config:      `{"color":"#1890ff","width":100,"height":50,"textColor":"#fff","fontSize":12}`,
		},
		{
			ID:          "template-3",
			Name:        "判断节点",
			Description: "条件判断节点模板",
			Category:    "basic",
			NodeType:    "diamond",
			Icon:        "question-circle",
			Config:      `{"color":"#fa8c16","width":80,"height":60,"textColor":"#fff","fontSize":12}`,
		},
		{
			ID:          "template-4",
			Name:        "结束节点",
			Description: "流程结束节点模板",
			Category:    "basic",
			NodeType:    "circle",
			Icon:        "check-circle",
			Config:      `{"color":"#52c41a","radius":30,"textColor":"#fff","fontSize":12}`,
		},
		{
			ID:          "template-5",
			Name:        "API调用",
			Description: "API接口调用节点",
			Category:    "advanced",
			NodeType:    "rect",
			Icon:        "api",
			Config:      `{"color":"#722ed1","width":120,"height":60,"textColor":"#fff","fontSize":12,"apiConfig":{"method":"POST","timeout":30}}`,
		},
	}

	// 批量插入到数据库
	for _, template := range templates {
		s.db.Create(template)
	}
}

// GetTemplates 获取模板列表
func (s *TemplateService) GetTemplates(page, pageSize int, category string) ([]*model.Template, int64, error) {
	var templates []model.Template
	var total int64

	query := s.db.Model(&model.Template{})

	// 分类过滤
	if category != "" {
		query = query.Where("category = ?", category)
	}

	// 获取总数
	query.Count(&total)

	// 分页查询
	offset := (page - 1) * pageSize
	err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&templates).Error
	if err != nil {
		return nil, 0, err
	}

	// 转换为指针切片
	var result []*model.Template
	for i := range templates {
		result = append(result, &templates[i])
	}

	return result, total, nil
}

// GetTemplate 获取单个模板
func (s *TemplateService) GetTemplate(id string) (*model.Template, error) {
	var template model.Template
	err := s.db.Where("id = ?", id).First(&template).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("template not found")
		}
		return nil, err
	}
	return &template, nil
}

// CreateTemplate 创建模板
func (s *TemplateService) CreateTemplate(req *model.CreateTemplateRequest) (*model.Template, error) {
	configJSON, err := json.Marshal(req.Config)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal config: %v", err)
	}

	template := &model.Template{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Description: req.Description,
		Category:    req.Category,
		NodeType:    req.NodeType,
		Icon:        req.Icon,
		Config:      string(configJSON),
	}

	err = s.db.Create(template).Error
	if err != nil {
		return nil, err
	}

	return template, nil
}

// UpdateTemplate 更新模板
func (s *TemplateService) UpdateTemplate(id string, req *model.UpdateTemplateRequest) (*model.Template, error) {
	var template model.Template
	err := s.db.Where("id = ?", id).First(&template).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("template not found")
		}
		return nil, err
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Category != "" {
		updates["category"] = req.Category
	}
	if req.NodeType != "" {
		updates["node_type"] = req.NodeType
	}
	if req.Icon != "" {
		updates["icon"] = req.Icon
	}
	if req.Config != nil {
		configJSON, err := json.Marshal(req.Config)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal config: %v", err)
		}
		updates["config"] = string(configJSON)
	}

	err = s.db.Model(&template).Updates(updates).Error
	if err != nil {
		return nil, err
	}

	return &template, nil
}

// DeleteTemplate 删除模板
func (s *TemplateService) DeleteTemplate(id string) error {
	result := s.db.Where("id = ?", id).Delete(&model.Template{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("template not found")
	}
	return nil
}
