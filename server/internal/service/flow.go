package service

import (
	"encoding/json"
	"fmt"
	"log"
	"sec-flow-server/internal/constants"
	"sec-flow-server/internal/database"
	"sec-flow-server/internal/model"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// FlowService 流程图服务
type FlowService struct {
	db               *gorm.DB
	executionService *FlowExecutionService
}

// NewFlowService 创建流程图服务
func NewFlowService() *FlowService {
	service := &FlowService{
		db:               database.GetDB(),
		executionService: NewFlowExecutionService(),
	}

	return service
}

// GetFlows 获取流程图列表
func (s *FlowService) GetFlows(page, pageSize int, status, keyword, description string) ([]*model.FlowResponse, int64, error) {
	var flows []model.Flow
	var total int64

	query := s.db.Model(&model.Flow{})

	// 状态过滤
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 关键词过滤（流程名称）
	if keyword != "" {
		query = query.Where("name LIKE ?", "%"+keyword+"%")
	}

	// 描述过滤
	if description != "" {
		query = query.Where("description LIKE ?", "%"+description+"%")
	}

	// 获取总数
	query.Count(&total)

	// 分页查询
	offset := (page - 1) * pageSize
	err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&flows).Error
	if err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	var result []*model.FlowResponse
	for _, flow := range flows {
		flowResp, err := s.convertToFlowResponse(&flow)
		if err != nil {
			continue // 跳过转换失败的记录
		}
		result = append(result, flowResp)
	}

	return result, total, nil
}

// GetFlowsWithLastExecution 获取带最新执行记录的流程列表
func (s *FlowService) GetFlowsWithLastExecution(page, pageSize int, status, keyword, description string) ([]*model.FlowWithLastExecution, int64, error) {
	var flows []model.Flow
	var total int64

	query := s.db.Model(&model.Flow{})

	// 添加状态过滤
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 添加关键词搜索（流程名称）
	if keyword != "" {
		query = query.Where("name LIKE ?", "%"+keyword+"%")
	}

	// 添加描述搜索
	if description != "" {
		query = query.Where("description LIKE ?", "%"+description+"%")
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Order("updated_at DESC").Offset(offset).Limit(pageSize).Find(&flows).Error
	if err != nil {
		return nil, 0, err
	}

	// 转换为带执行记录的响应格式
	var responses []*model.FlowWithLastExecution
	for _, flow := range flows {
		flowWithExecution := &model.FlowWithLastExecution{
			Flow: flow,
		}

		// 获取最新执行记录
		lastExecution, err := s.executionService.GetLastExecution(flow.ID)
		if err != nil {
			log.Printf("Failed to get last execution for flow %s: %v", flow.ID, err)
		} else {
			flowWithExecution.LastExecution = lastExecution
		}

		responses = append(responses, flowWithExecution)
	}

	return responses, total, nil
}

// convertToFlowResponse 转换为响应格式
func (s *FlowService) convertToFlowResponse(flow *model.Flow) (*model.FlowResponse, error) {
	var flowData model.FlowData
	err := json.Unmarshal([]byte(flow.Data), &flowData)
	if err != nil {
		return nil, err
	}

	return &model.FlowResponse{
		ID:                     flow.ID,
		Name:                   flow.Name,
		ExecutionTime:          flow.ExecutionTime,
		Description:            flow.Description,
		Data:                   flowData,
		ExecutionRetentionDays: flow.ExecutionRetentionDays,
		CreatedAt:              flow.CreatedAt,
		UpdatedAt:              flow.UpdatedAt,
		CreatedBy:              flow.CreatedBy,
		Status:                 flow.Status,
	}, nil
}

// GetFlow 获取单个流程图
func (s *FlowService) GetFlow(id string) (*model.FlowResponse, error) {
	var flow model.Flow
	err := s.db.Where("id = ?", id).First(&flow).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("flow not found")
		}
		return nil, err
	}

	return s.convertToFlowResponse(&flow)
}

// CreateFlow 创建流程图
func (s *FlowService) CreateFlow(req *model.CreateFlowRequest) (*model.FlowResponse, error) {
	dataJSON, err := json.Marshal(req.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal flow data: %v", err)
	}

	// 设置默认保存期限
	retentionDays := req.ExecutionRetentionDays
	if retentionDays <= 0 {
		retentionDays = 30 // 默认30天
	}

	flow := &model.Flow{
		ID:                     uuid.New().String(),
		Name:                   req.Name,
		ExecutionTime:          req.ExecutionTime,
		Description:            req.Description,
		Data:                   string(dataJSON),
		ExecutionRetentionDays: retentionDays,
		Status:                 constants.FlowStatusDraft,
		CreatedBy:              "user-1", // 实际项目中应该从上下文获取
	}

	err = s.db.Create(flow).Error
	if err != nil {
		return nil, err
	}

	return s.convertToFlowResponse(flow)
}

// UpdateFlow 更新流程图
func (s *FlowService) UpdateFlow(id string, req *model.UpdateFlowRequest) (*model.FlowResponse, error) {
	var flow model.Flow
	err := s.db.Where("id = ?", id).First(&flow).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("flow not found")
		}
		return nil, err
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.ExecutionTime != "" {
		updates["execution_time"] = req.ExecutionTime
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}
	if req.ExecutionRetentionDays != nil {
		updates["execution_retention_days"] = *req.ExecutionRetentionDays
	}
	if len(req.Data.Nodes) > 0 || len(req.Data.Edges) > 0 {
		dataJSON, err := json.Marshal(req.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal flow data: %v", err)
		}
		updates["data"] = string(dataJSON)
	}

	err = s.db.Model(&flow).Updates(updates).Error
	if err != nil {
		return nil, err
	}

	// 重新查询更新后的数据
	err = s.db.Where("id = ?", id).First(&flow).Error
	if err != nil {
		return nil, err
	}

	return s.convertToFlowResponse(&flow)
}

// DeleteFlow 删除流程图
func (s *FlowService) DeleteFlow(id string) error {
	result := s.db.Where("id = ?", id).Delete(&model.Flow{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("flow not found")
	}
	return nil
}

// ExecuteFlow 执行流程图
func (s *FlowService) ExecuteFlow(id string, req *model.ExecuteFlowRequest) (*model.ExecuteFlowResponse, error) {
	flowResp, err := s.GetFlow(id)
	if err != nil {
		return nil, err
	}

	// 模拟执行流程
	executionId := uuid.New().String()

	response := &model.ExecuteFlowResponse{
		ExecutionId: executionId,
		Status:      "running",
		Steps:       []model.ExecutionStep{},
	}

	// 模拟执行步骤
	for _, node := range flowResp.Data.Nodes {
		step := model.ExecutionStep{
			NodeId:    node.ID,
			NodeType:  node.Type,
			Status:    "completed",
			StartTime: time.Now(),
			EndTime:   &[]time.Time{time.Now().Add(time.Second)}[0],
			Input:     req.InputData,
			Output:    map[string]interface{}{"result": "success"},
		}
		response.Steps = append(response.Steps, step)
	}

	response.Status = "completed"
	response.Result = map[string]interface{}{
		"message": "Flow executed successfully",
		"output":  "Process completed",
	}

	return response, nil
}
