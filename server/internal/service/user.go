package service

import (
	"errors"
	"fmt"
	"log"
	"sec-flow-server/internal/constants"
	"sec-flow-server/internal/database"
	"sec-flow-server/internal/model"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserService 用户服务
type UserService struct {
	db *gorm.DB
}

// NewUserService 创建用户服务
func NewUserService() *UserService {
	return &UserService{
		db: database.GetDB(),
	}
}

// GetOrCreateUserByUsername 根据用户名获取或创建用户
func (s *UserService) GetOrCreateUserByUsername(username string) (*model.User, error) {
	log.Printf("🔍 查找用户: %s", username)

	var user model.User
	err := s.db.Preload("Roles").Preload("Roles.Permissions").
		Where("username = ?", username).First(&user).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Printf("⚠️  用户 %s 不存在，开始创建新用户", username)
			// 用户不存在，创建新用户
			return s.createDefaultUser(username)
		}
		log.Printf("❌ 查询用户失败: %v", err)
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}

	log.Printf("✅ 找到用户: %s, ID: %s, 角色数量: %d", user.Username, user.ID, len(user.Roles))
	return &user, nil
}

// createDefaultUser 创建默认用户
func (s *UserService) createDefaultUser(username string) (*model.User, error) {
	log.Printf("🆕 开始创建默认用户: %s", username)

	// 获取默认角色
	var defaultRole model.Role
	err := s.db.Where("name = ? AND is_default = ?", constants.RoleUser, true).First(&defaultRole).Error
	if err != nil {
		log.Printf("❌ 获取默认角色失败: %v", err)
		return nil, fmt.Errorf("获取默认角色失败: %v", err)
	}

	log.Printf("✅ 找到默认角色: %s (ID: %s)", defaultRole.Name, defaultRole.ID)

	// 创建用户
	user := &model.User{
		ID:        uuid.New().String(),
		Username:  username,
		Email:     "",
		Avatar:    "",
		Status:    "active",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	log.Printf("🏗️  创建用户对象: ID=%s, Username=%s", user.ID, user.Username)

	// 在事务中创建用户并分配角色
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 创建用户
		if err := tx.Create(user).Error; err != nil {
			log.Printf("❌ 创建用户记录失败: %v", err)
			return err
		}
		log.Printf("✅ 用户记录创建成功")

		// 分配默认角色
		userRole := &model.UserRole{
			UserID: user.ID,
			RoleID: defaultRole.ID,
		}
		if err := tx.Create(userRole).Error; err != nil {
			log.Printf("❌ 分配角色失败: %v", err)
			return err
		}
		log.Printf("✅ 角色分配成功: UserID=%s, RoleID=%s", userRole.UserID, userRole.RoleID)

		return nil
	})

	if err != nil {
		log.Printf("❌ 创建用户事务失败: %v", err)
		return nil, fmt.Errorf("创建用户失败: %v", err)
	}

	log.Printf("✅ 用户创建完成，重新加载用户信息")
	// 重新加载用户信息
	return s.GetOrCreateUserByUsername(username)
}

// GetProfile 获取用户信息
func (s *UserService) GetProfile(userID string) (*model.User, error) {
	var user model.User
	err := s.db.Preload("Roles").Preload("Roles.Permissions").
		Where("id = ?", userID).First(&user).Error

	if err != nil {
		return nil, fmt.Errorf("用户不存在")
	}

	return &user, nil
}

// GetUserWithPermissions 获取用户及其权限信息
func (s *UserService) GetUserWithPermissions(userID string) (*model.UserWithPermissions, error) {
	user, err := s.GetProfile(userID)
	if err != nil {
		return nil, err
	}

	// 获取用户所有权限
	permissions, err := s.getUserPermissions(userID)
	if err != nil {
		return nil, err
	}

	return &model.UserWithPermissions{
		User:        user,
		Roles:       user.Roles,
		Permissions: permissions,
	}, nil
}

// getUserPermissions 获取用户的所有权限
func (s *UserService) getUserPermissions(userID string) ([]model.Permission, error) {
	var permissions []model.Permission
	err := s.db.Table("tb_permissions").
		Joins("JOIN tb_role_permissions ON tb_permissions.id = tb_role_permissions.permission_id").
		Joins("JOIN tb_user_roles ON tb_role_permissions.role_id = tb_user_roles.role_id").
		Where("tb_user_roles.user_id = ?", userID).
		Group("tb_permissions.id").
		Find(&permissions).Error

	return permissions, err
}

// CheckUserPermission 检查用户权限
func (s *UserService) CheckUserPermission(userID, resource, action string) (bool, error) {
	permissions, err := s.getUserPermissions(userID)
	if err != nil {
		return false, err
	}

	// 检查是否有对应的权限
	for _, permission := range permissions {
		if permission.Resource == resource {
			// 如果有manage权限，则拥有该资源的所有权限
			if permission.Action == constants.ActionManage {
				return true, nil
			}
			// 检查具体的操作权限
			if permission.Action == action {
				return true, nil
			}
		}
	}

	return false, nil
}

// CheckUserRole 检查用户角色
func (s *UserService) CheckUserRole(userID, roleName string) (bool, error) {
	var count int64
	err := s.db.Table("tb_users").
		Joins("JOIN tb_user_roles ON tb_users.id = tb_user_roles.user_id").
		Joins("JOIN tb_roles ON tb_user_roles.role_id = tb_roles.id").
		Where("tb_users.id = ? AND tb_roles.name = ?", userID, roleName).
		Count(&count).Error

	return count > 0, err
}

// UpdateProfile 更新用户信息
func (s *UserService) UpdateProfile(userID string, req *model.UpdateProfileRequest) (*model.User, error) {
	var user model.User
	err := s.db.Where("id = ?", userID).First(&user).Error
	if err != nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 更新字段
	if req.Username != "" && req.Username != user.Username {
		// 检查用户名是否已存在
		var count int64
		s.db.Model(&model.User{}).Where("username = ? AND id != ?", req.Username, userID).Count(&count)
		if count > 0 {
			return nil, fmt.Errorf("用户名已存在")
		}
		user.Username = req.Username
	}

	if req.Email != "" {
		user.Email = req.Email
	}

	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}

	user.UpdatedAt = time.Now()

	err = s.db.Save(&user).Error
	if err != nil {
		return nil, fmt.Errorf("更新用户信息失败: %v", err)
	}

	return &user, nil
}

// GetUsers 获取用户列表
func (s *UserService) GetUsers(req *model.UserListRequest) ([]model.User, int64, error) {
	var users []model.User
	var total int64

	query := s.db.Model(&model.User{}).Preload("Roles")

	// 添加搜索条件
	if req.Keyword != "" {
		query = query.Where("username LIKE ? OR email LIKE ?",
			"%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	if req.RoleID != "" {
		query = query.Joins("JOIN tb_user_roles ON tb_users.id = tb_user_roles.user_id").
			Where("tb_user_roles.role_id = ?", req.RoleID)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	offset := (req.Page - 1) * req.PageSize
	err = query.Offset(offset).Limit(req.PageSize).Find(&users).Error

	return users, total, err
}

// CreateUser 创建用户
func (s *UserService) CreateUser(req *model.CreateUserRequest) (*model.User, error) {
	// 检查用户名是否已存在
	var count int64
	s.db.Model(&model.User{}).Where("username = ?", req.Username).Count(&count)
	if count > 0 {
		return nil, fmt.Errorf("用户名已存在")
	}

	// 验证角色ID
	if len(req.RoleIDs) > 0 {
		var roleCount int64
		s.db.Model(&model.Role{}).Where("id IN ?", req.RoleIDs).Count(&roleCount)
		if int(roleCount) != len(req.RoleIDs) {
			return nil, fmt.Errorf("部分角色ID无效")
		}
	}

	user := &model.User{
		ID:        uuid.New().String(),
		Username:  req.Username,
		Email:     req.Email,
		Avatar:    req.Avatar,
		Status:    "active",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 在事务中创建用户并分配角色
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// 创建用户
		if err := tx.Create(user).Error; err != nil {
			return err
		}

		// 分配角色
		if len(req.RoleIDs) > 0 {
			for _, roleID := range req.RoleIDs {
				userRole := &model.UserRole{
					UserID: user.ID,
					RoleID: roleID,
				}
				if err := tx.Create(userRole).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("创建用户失败: %v", err)
	}

	// 重新加载用户信息
	return s.GetProfile(user.ID)
}

// UpdateUser 更新用户
func (s *UserService) UpdateUser(userID string, req *model.UpdateUserRequest) (*model.User, error) {
	var user model.User
	err := s.db.Where("id = ?", userID).First(&user).Error
	if err != nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 检查用户名是否已存在
	if req.Username != "" && req.Username != user.Username {
		var count int64
		s.db.Model(&model.User{}).Where("username = ? AND id != ?", req.Username, userID).Count(&count)
		if count > 0 {
			return nil, fmt.Errorf("用户名已存在")
		}
	}

	// 验证角色ID
	if len(req.RoleIDs) > 0 {
		var roleCount int64
		s.db.Model(&model.Role{}).Where("id IN ?", req.RoleIDs).Count(&roleCount)
		if int(roleCount) != len(req.RoleIDs) {
			return nil, fmt.Errorf("部分角色ID无效")
		}
	}

	// 在事务中更新用户和角色
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 更新用户信息
		if req.Username != "" {
			user.Username = req.Username
		}
		if req.Email != "" {
			user.Email = req.Email
		}
		if req.Avatar != "" {
			user.Avatar = req.Avatar
		}
		if req.Status != "" {
			user.Status = req.Status
		}
		user.UpdatedAt = time.Now()

		if err := tx.Save(&user).Error; err != nil {
			return err
		}

		// 更新角色关联
		if len(req.RoleIDs) > 0 {
			// 删除原有角色关联
			if err := tx.Where("user_id = ?", userID).Delete(&model.UserRole{}).Error; err != nil {
				return err
			}

			// 添加新的角色关联
			for _, roleID := range req.RoleIDs {
				userRole := &model.UserRole{
					UserID: userID,
					RoleID: roleID,
				}
				if err := tx.Create(userRole).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("更新用户失败: %v", err)
	}

	return s.GetProfile(userID)
}

// DeleteUser 删除用户
func (s *UserService) DeleteUser(userID string) error {
	// 检查用户是否存在
	var user model.User
	err := s.db.Where("id = ?", userID).First(&user).Error
	if err != nil {
		return fmt.Errorf("用户不存在")
	}

	// 在事务中删除用户和相关关联
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 删除用户角色关联
		if err := tx.Where("user_id = ?", userID).Delete(&model.UserRole{}).Error; err != nil {
			return err
		}

		// 删除用户
		if err := tx.Delete(&user).Error; err != nil {
			return err
		}

		return nil
	})
}

// GetUserPermissions 获取用户权限列表（字符串格式）
func (s *UserService) GetUserPermissions(userID string) ([]string, error) {
	permissions, err := s.getUserPermissions(userID)
	if err != nil {
		return nil, err
	}

	var permissionKeys []string
	for _, permission := range permissions {
		key := fmt.Sprintf("%s:%s", permission.Resource, permission.Action)
		permissionKeys = append(permissionKeys, key)
	}

	return permissionKeys, nil
}

// HasPermission 检查权限（支持通配符）
func (s *UserService) HasPermission(userID, permissionKey string) (bool, error) {
	parts := strings.Split(permissionKey, ":")
	if len(parts) != 2 {
		return false, fmt.Errorf("权限格式错误")
	}

	return s.CheckUserPermission(userID, parts[0], parts[1])
}
