package service

import (
	"context"
	"fmt"
	"go.mongodb.org/mongo-driver/bson"
	"sec-flow-server/internal/config"
	"sec-flow-server/internal/constants"
	"sec-flow-server/internal/model"
	"sec-flow-server/internal/service/third_party/lark"
	"sec-flow-server/internal/service/third_party/mongo"
	"sec-flow-server/internal/utils"
	"strings"
	"time"
)

type EmployeeServiceStruct struct {
}

var EmployeeService *EmployeeServiceStruct

func (s *EmployeeServiceStruct) GetEmployeesByUsernames(usernames []string) ([]*model.Employee, error) {
	var employees = make([]*model.Employee, 0)
	var results []map[string]interface{}
	var emails = make([]string, 0)
	for _, username := range usernames {
		emails = append(emails, username+constants.EmailSuffix)
	}
	filter := bson.M{
		"email": bson.M{
			"$in": emails,
		},
	}
	err := mongo.FindMany(context.Background(), "Reaper", "Employee", filter, &results)
	if err != nil {
		return nil, err
	}
	for _, result := range results {
		e := &model.Employee{}
		name, ok := result["name"]
		if ok {
			e.Name = name.(string)
		}
		email, ok := result["email"]
		if ok {
			emailStr := email.(string)
			e.Email = emailStr
			e.Username = strings.Replace(emailStr, constants.EmailSuffix, "", -1)
		}
		userId, ok := result["user_id"]
		if ok {
			e.UserId = userId.(string)
		}
		employees = append(employees, e)
	}

	return employees, nil
}

func (s *EmployeeServiceStruct) SearchEmployees(keyword string) ([]*model.Employee, error) {
	var employees = make([]*model.Employee, 0)
	var results []map[string]interface{}
	filter := bson.M{
		"status.is_resigned": false,
		"$or": []bson.M{
			{"name": bson.M{"$regex": keyword, "$options": "i"}},
			{"email": bson.M{"$regex": keyword, "$options": "i"}},
		},
	}
	err := mongo.FindMany(context.Background(), "Reaper", "Employee", filter, &results)
	if err != nil {
		return nil, err
	}
	for _, result := range results {
		e := &model.Employee{}
		name, ok := result["name"]
		if ok {
			e.Name = name.(string)
		}
		email, ok := result["email"]
		if ok {
			emailStr := email.(string)
			e.Email = emailStr
			e.Username = strings.Replace(emailStr, constants.EmailSuffix, "", -1)
		}
		userId, ok := result["user_id"]
		if ok {
			e.UserId = userId.(string)
		}
		employees = append(employees, e)
	}

	return employees, nil
}

func (s *EmployeeServiceStruct) ScanEmployeeResigned(executeDayStr string) ([]map[string]interface{}, error) {
	// 1. 扫描离职人员
	var dbc *config.DbConfig
	configs := config.Get()
	for _, db := range configs.Databases {
		if db.Database == "db_security" {
			dbc = &db
			break
		}
	}
	if dbc == nil {
		return nil, fmt.Errorf("db_security not found")
	}
	dbConfig := utils.SimpleConfig(dbc.Host, dbc.Username, dbc.Password, dbc.Database)
	sqlClient, err := utils.NewMySQLClient(dbConfig)
	if err != nil {
		return nil, err
	}
	defer sqlClient.Close()

	today := time.Now().Format("2006-01-02")
	if executeDayStr == "" {
		executeDayStr = today
	}
	var argsAny []interface{}
	argsAny = append(argsAny, executeDayStr)
	argsAny = append(argsAny, executeDayStr)
	// 工具: 安全字符串转换
	getString := func(val interface{}) string {
		if val == nil {
			return ""
		}
		switch v := val.(type) {
		case string:
			return v
		case []byte:
			return string(v)
		default:
			return fmt.Sprintf("%v", v)
		}
	}
	sql := "select * from tb_sy_employee where updated_at < (?) and is_resigned = 0 and lastday < (?);"
	rows, err := sqlClient.Query(sql, argsAny...)
	resignedUsers := make([]map[string]interface{}, 0)
	if err != nil {
		fmt.Printf("批量查询可能离职人员失败: %v\n", err)
	} else {
		for _, row := range rows {
			larkUserId := getString(row["lark_user_id"])
			user, err := lark.GetLarkUser(larkUserId)
			if err != nil {
				fmt.Printf("查询飞书用户失败: %v\n", err)
				continue
			}
			if user == nil {
				continue
			}
			if user.Status.IsResigned {
				// 离职
				emp := map[string]interface{}{
					"id":             row["id"],
					"emp_id":         getString(row["emp_id"]),
					"soyoung_uid":    row["soyoung_uid"],
					"dept_name":      getString(row["dept_name"]),
					"full_name":      getString(row["full_name"]),
					"name":           getString(row["name"]),
					"user_base_city": getString(row["user_base_city"]),
					"email":          getString(row["email"]),
					"username":       getString(row["username"]),
					"lark_user_id":   larkUserId,
					"enc_mobile":     row["enc_mobile"],
					"emp_type":       row["emp_type"],
					"emp_type_name":  getString(row["emp_type_name"]),
					"join_time":      getString(row["join_time"]),
					"is_resigned":    1,
				}
				resignedUsers = append(resignedUsers, emp)
			}
		}
	}
	if len(resignedUsers) > 0 {

		var dbcp *config.DbConfig
		for _, db := range configs.Databases {
			if db.Database == "db_groupbuy" {
				dbcp = &db
				break
			}
		}
		if dbcp == nil {
			return nil, fmt.Errorf("db_passport not found")
		}
		dbpConfig := utils.SimpleConfig(dbcp.Host, dbcp.Username, dbcp.Password, "db_passport")
		sqlClientPublic, err := utils.NewMySQLClient(dbpConfig)
		if err != nil {
			return nil, err
		}
		defer sqlClientPublic.Close()

		//var soyoung_uid_map = make(map[string]map[string]interface{})
		var soyoung_uids = make([]string, 0)
		for _, user := range resignedUsers {
			soyoung_uid_str := fmt.Sprintf("%v", user["soyoung_uid"])
			if soyoung_uid_str == "0" {
				continue
			}
			soyoung_uids = append(soyoung_uids, soyoung_uid_str)
		}

		if len(soyoung_uids) > 0 {
			hospital_ids := make([]string, 0)
			uid_certified_map := make(map[string]map[string]interface{})
			hospital_name_map := make(map[string]string)
			// 查认证
			uids := strings.Join(soyoung_uids, ",")
			query, err := sqlClientPublic.Query(fmt.Sprintf("select uid, certified_type, certified_id from db_passport.tb_user_details where uid in (%s);", uids))
			if err != nil {
				return nil, err
			}
			for _, row := range query {
				soyoung_uid_str := fmt.Sprintf("%v", row["uid"])
				m1, ok := uid_certified_map[soyoung_uid_str]
				if !ok {
					m1 = make(map[string]interface{})
					uid_certified_map[soyoung_uid_str] = m1
				}
				m1["certified_type"] = row["certified_type"]
				m1["certified_id"] = row["certified_id"]
				if fmt.Sprintf("%v", row["certified_id"]) != "0" {
					hospital_ids = append(hospital_ids, fmt.Sprintf("%v", row["certified_id"]))
				}
			}
			// 查医院绑定
			if len(hospital_ids) > 0 {
				hids := strings.Join(hospital_ids, ",")
				query1, err := sqlClientPublic.Query(fmt.Sprintf("select hospital_id, name_cn from db_kr.tb_hospital where hospital_id in (%s);", hids))
				if err != nil {
					return nil, err
				}
				for _, row := range query1 {
					hospital_name_map[fmt.Sprintf("%v", row["id"])] = fmt.Sprintf("%v", row["name"])
				}
			}
			for _, user := range resignedUsers {
				soyoung_uid_str := fmt.Sprintf("%v", user["soyoung_uid"])
				m1, ok := uid_certified_map[soyoung_uid_str]
				if ok {
					user["certified_type"] = m1["certified_type"]
					user["certified_id"] = m1["certified_id"]
					user["hospital_name"] = hospital_name_map[fmt.Sprintf("%v", m1["certified_id"])]
				}
			}
		}

		/*argsIds := make([]interface{}, 0)
		for _, user := range resignedUsers {
			argsIds = append(argsIds, user["id"])
		}
		placeholders := strings.TrimRight(strings.Repeat("?,", len(argsIds)), ",")
		sql = "update tb_sy_employee set is_resigned = 1 where id in (%s);"
		sql = fmt.Sprintf(sql, placeholders)
		_, _ = sqlClient.Execute(sql, argsIds...)*/
	}
	return resignedUsers, nil
}
