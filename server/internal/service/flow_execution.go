package service

import (
	"encoding/json"
	"fmt"
	"log"
	"sec-flow-server/internal/constants"
	"sec-flow-server/internal/database"
	"sec-flow-server/internal/model"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// FlowExecutionService 流程执行记录服务
type FlowExecutionService struct {
	db *gorm.DB
}

// NewFlowExecutionService 创建流程执行记录服务
func NewFlowExecutionService() *FlowExecutionService {
	return &FlowExecutionService{
		db: database.GetDB(),
	}
}

// CreateExecution 创建执行记录
func (s *FlowExecutionService) CreateExecution(flowID, flowName string, flowSnapshot *model.FlowData,
	triggerType constants.TriggerType, triggerSource string, inputData map[string]interface{}) (*model.FlowExecution, error) {

	// 序列化流程快照
	snapshotJSON, err := json.Marshal(flowSnapshot)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal flow snapshot: %v", err)
	}

	// 序列化输入数据
	var inputDataJSON string
	if inputData != nil {
		inputBytes, err := json.Marshal(inputData)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal input data: %v", err)
		}
		inputDataJSON = string(inputBytes)
	}

	execution := &model.FlowExecution{
		ID:            uuid.New().String(),
		FlowID:        flowID,
		FlowName:      flowName,
		FlowSnapshot:  string(snapshotJSON),
		TriggerType:   triggerType,
		TriggerSource: triggerSource,
		Status:        constants.ExecutionStatusRunning,
		StartTime:     time.Now(),
		InputData:     inputDataJSON,
	}

	err = s.db.Create(execution).Error
	if err != nil {
		return nil, fmt.Errorf("failed to create execution record: %v", err)
	}

	return execution, nil
}

// UpdateExecution 更新执行记录
func (s *FlowExecutionService) UpdateExecution(executionID string, status constants.ExecutionStatus,
	outputData map[string]interface{}, executionLog *model.FlowExecutionLog, errorMessage string) error {

	updates := map[string]interface{}{
		"status": status,
	}

	// 设置结束时间和计算执行时长
	if status != constants.ExecutionStatusRunning {
		endTime := time.Now()
		updates["end_time"] = endTime

		// 获取开始时间计算时长
		var execution model.FlowExecution
		if err := s.db.Select("start_time").Where("id = ?", executionID).First(&execution).Error; err == nil {
			duration := endTime.Sub(execution.StartTime).Milliseconds()
			updates["duration"] = duration
		}
	}

	// 序列化输出数据
	if outputData != nil {
		outputBytes, err := json.Marshal(outputData)
		if err != nil {
			return fmt.Errorf("failed to marshal output data: %v", err)
		}
		updates["output_data"] = string(outputBytes)
	}

	// 序列化执行日志
	if executionLog != nil {
		logBytes, err := json.Marshal(executionLog)
		if err != nil {
			return fmt.Errorf("failed to marshal execution log: %v", err)
		}
		updates["execution_log"] = string(logBytes)
	}

	// 设置错误信息
	if errorMessage != "" {
		updates["error_message"] = errorMessage
	}

	err := s.db.Model(&model.FlowExecution{}).Where("id = ?", executionID).Updates(updates).Error
	if err != nil {
		return fmt.Errorf("failed to update execution record: %v", err)
	}

	return nil
}

// GetExecutions 获取执行记录列表
func (s *FlowExecutionService) GetExecutions(flowID string, page, pageSize int) ([]*model.FlowExecutionListResponse, int64, error) {
	var executions []model.FlowExecution
	var total int64

	query := s.db.Model(&model.FlowExecution{})
	if flowID != "" {
		query = query.Where("flow_id = ?", flowID)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count executions: %v", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&executions).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get executions: %v", err)
	}

	// 转换为响应格式
	var responses []*model.FlowExecutionListResponse
	for _, execution := range executions {
		responses = append(responses, &model.FlowExecutionListResponse{
			ID:            execution.ID,
			FlowID:        execution.FlowID,
			FlowName:      execution.FlowName,
			TriggerType:   execution.TriggerType,
			TriggerSource: execution.TriggerSource,
			Status:        execution.Status,
			StartTime:     execution.StartTime,
			EndTime:       execution.EndTime,
			Duration:      execution.Duration,
			ErrorMessage:  execution.ErrorMessage,
			CreatedAt:     execution.CreatedAt,
		})
	}

	return responses, total, nil
}

// GetExecution 获取执行记录详情
func (s *FlowExecutionService) GetExecution(executionID string) (*model.FlowExecutionDetailResponse, error) {
	var execution model.FlowExecution
	err := s.db.Where("id = ?", executionID).First(&execution).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("execution record not found")
		}
		return nil, fmt.Errorf("failed to get execution record: %v", err)
	}

	// 解析流程快照
	var flowSnapshot model.FlowData
	if err := json.Unmarshal([]byte(execution.FlowSnapshot), &flowSnapshot); err != nil {
		return nil, fmt.Errorf("failed to unmarshal flow snapshot: %v", err)
	}

	// 解析输入数据
	var inputData map[string]interface{}
	if execution.InputData != "" {
		if err := json.Unmarshal([]byte(execution.InputData), &inputData); err != nil {
			return nil, fmt.Errorf("failed to unmarshal input data: %v", err)
		}
	}

	// 解析输出数据
	var outputData map[string]interface{}
	if execution.OutputData != "" {
		if err := json.Unmarshal([]byte(execution.OutputData), &outputData); err != nil {
			return nil, fmt.Errorf("failed to unmarshal output data: %v", err)
		}
	}

	// 解析执行日志
	var executionLog model.FlowExecutionLog
	if execution.ExecutionLog != "" {
		if err := json.Unmarshal([]byte(execution.ExecutionLog), &executionLog); err != nil {
			return nil, fmt.Errorf("failed to unmarshal execution log: %v", err)
		}
	}

	response := &model.FlowExecutionDetailResponse{
		FlowExecutionListResponse: model.FlowExecutionListResponse{
			ID:            execution.ID,
			FlowID:        execution.FlowID,
			FlowName:      execution.FlowName,
			TriggerType:   execution.TriggerType,
			TriggerSource: execution.TriggerSource,
			Status:        execution.Status,
			StartTime:     execution.StartTime,
			EndTime:       execution.EndTime,
			Duration:      execution.Duration,
			ErrorMessage:  execution.ErrorMessage,
			CreatedAt:     execution.CreatedAt,
		},
		FlowSnapshot: flowSnapshot,
		InputData:    inputData,
		OutputData:   outputData,
		ExecutionLog: executionLog,
	}

	return response, nil
}

// GetLastExecution 获取流程的最新执行记录
func (s *FlowExecutionService) GetLastExecution(flowID string) (*model.FlowExecutionListResponse, error) {
	var execution model.FlowExecution
	err := s.db.Where("flow_id = ?", flowID).Order("created_at DESC").First(&execution).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 没有执行记录
		}
		return nil, fmt.Errorf("failed to get last execution: %v", err)
	}

	return &model.FlowExecutionListResponse{
		ID:            execution.ID,
		FlowID:        execution.FlowID,
		FlowName:      execution.FlowName,
		TriggerType:   execution.TriggerType,
		TriggerSource: execution.TriggerSource,
		Status:        execution.Status,
		StartTime:     execution.StartTime,
		EndTime:       execution.EndTime,
		Duration:      execution.Duration,
		ErrorMessage:  execution.ErrorMessage,
		CreatedAt:     execution.CreatedAt,
	}, nil
}

// DeleteExecutions 删除流程的所有执行记录
func (s *FlowExecutionService) DeleteExecutions(flowID string) error {
	err := s.db.Where("flow_id = ?", flowID).Delete(&model.FlowExecution{}).Error
	if err != nil {
		return fmt.Errorf("failed to delete executions: %v", err)
	}
	return nil
}

// FixStuckExecutions 修复卡住的执行记录
func (s *FlowExecutionService) FixStuckExecutions() (int, error) {
	log.Printf("🔧 开始修复卡住的执行记录...")

	// 查找状态为running但创建时间超过1小时的执行记录
	var stuckExecutions []model.FlowExecution
	oneHourAgo := time.Now().Add(-1 * time.Hour)

	err := s.db.Where("status = ? AND created_at < ?", constants.ExecutionStatusRunning, oneHourAgo).
		Find(&stuckExecutions).Error
	if err != nil {
		return 0, fmt.Errorf("failed to find stuck executions: %v", err)
	}

	if len(stuckExecutions) == 0 {
		log.Printf("✅ 没有发现卡住的执行记录")
		return 0, nil
	}

	log.Printf("🔍 发现 %d 个卡住的执行记录", len(stuckExecutions))

	// 批量更新这些记录为失败状态
	fixedCount := 0
	for _, execution := range stuckExecutions {
		endTime := time.Now()
		duration := endTime.Sub(execution.StartTime).Milliseconds()

		updates := map[string]interface{}{
			"status":        constants.ExecutionStatusFailed,
			"end_time":      endTime,
			"duration":      duration,
			"error_message": "执行超时，系统自动标记为失败（可能由于程序崩溃导致）",
		}

		err := s.db.Model(&model.FlowExecution{}).Where("id = ?", execution.ID).Updates(updates).Error
		if err != nil {
			log.Printf("❌ 修复执行记录失败 [%s]: %v", execution.ID, err)
			continue
		}

		fixedCount++
		log.Printf("✅ 修复执行记录 [%s]: %s -> %s", execution.ID, execution.FlowName, constants.ExecutionStatusFailed)
	}

	log.Printf("🔧 修复完成，共修复 %d 个执行记录", fixedCount)
	return fixedCount, nil
}

// GetStuckExecutionsCount 获取卡住的执行记录数量
func (s *FlowExecutionService) GetStuckExecutionsCount() (int64, error) {
	var count int64
	oneHourAgo := time.Now().Add(-1 * time.Hour)

	err := s.db.Model(&model.FlowExecution{}).
		Where("status = ? AND created_at < ?", constants.ExecutionStatusRunning, oneHourAgo).
		Count(&count).Error

	return count, err
}
