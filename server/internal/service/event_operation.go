package service

import (
	"encoding/json"
	"fmt"
	"sec-flow-server/internal/database"
	"sec-flow-server/internal/model"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// EventOperationService 事件运营服务
type EventOperationService struct {
	db *gorm.DB
}

// NewEventOperationService 创建事件运营服务
func NewEventOperationService() *EventOperationService {
	return &EventOperationService{
		db: database.GetDB(),
	}
}

// GetOperations 获取事件运营列表
func (s *EventOperationService) GetOperations(req *model.EventOperationListRequest) ([]*model.EventOperationListResponse, int64, error) {
	var operations []*model.EventOperation
	var total int64

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	query := s.db.Model(&model.EventOperation{}).
		Preload("Category").
		Preload("Owner")

	// 添加筛选条件
	if req.CategoryID != "" {
		query = query.Where("category_id = ?", req.CategoryID)
	}
	if req.OwnerID != "" {
		query = query.Where("owner_id = ?", req.OwnerID)
	}
	if req.ProcessType != "" {
		query = query.Where("process_type = ?", req.ProcessType)
	}
	if req.OccurredStartTime != "" {
		startTime, err := time.Parse("2006-01-02", req.OccurredStartTime)
		if err == nil {
			query = query.Where("occurred_at >= ?", startTime)
		}
	}
	if req.OccurredEndTime != "" {
		endTime, err := time.Parse("2006-01-02", req.OccurredEndTime)
		if err == nil {
			// 结束时间包含当天，所以加一天
			endTime = endTime.AddDate(0, 0, 1)
			query = query.Where("occurred_at < ?", endTime)
		}
	}
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("occurred_condition LIKE ? OR description LIKE ? OR process_description LIKE ?", keyword, keyword, keyword)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count event operations: %v", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	err = query.Order("occurred_at DESC").Offset(offset).Limit(req.PageSize).Find(&operations).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get event operations: %v", err)
	}

	// 转换为响应格式并加载关联员工
	responses := make([]*model.EventOperationListResponse, len(operations))
	for i, operation := range operations {
		response := &model.EventOperationListResponse{
			EventOperation: *operation,
		}

		// 设置分类名称
		if operation.Category != nil {
			response.CategoryName = operation.Category.Name
		}

		// 设置负责人名称
		if operation.Owner != nil {
			response.OwnerName = operation.Owner.Username
		}

		// 加载关联员工
		if operation.RelatedEmployeeIDs != "" {
			var employeeIDs []string
			if err := json.Unmarshal([]byte(operation.RelatedEmployeeIDs), &employeeIDs); err == nil && len(employeeIDs) > 0 {
				usernames, err := EmployeeService.GetEmployeesByUsernames(employeeIDs)
				if err != nil {
					return nil, 0, fmt.Errorf("failed to get related employees: %v", err)
				}
				response.RelatedEmployees = usernames
			}
		}

		responses[i] = response
	}

	return responses, total, nil
}

// GetOperation 获取单个事件运营
func (s *EventOperationService) GetOperation(id string) (*model.EventOperation, error) {
	var operation model.EventOperation
	err := s.db.Preload("Category").Preload("Owner").Where("id = ?", id).First(&operation).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("event operation not found")
		}
		return nil, fmt.Errorf("failed to get event operation: %v", err)
	}

	// 加载关联员工
	if operation.RelatedEmployeeIDs != "" {
		var employeeIDs []string
		if err := json.Unmarshal([]byte(operation.RelatedEmployeeIDs), &employeeIDs); err == nil && len(employeeIDs) > 0 {
			usernames, err := EmployeeService.GetEmployeesByUsernames(employeeIDs)
			if err != nil {
				return nil, fmt.Errorf("failed to get related employees: %v", err)
			}
			operation.RelatedEmployees = usernames
		}
	}

	return &operation, nil
}

// CreateOperation 创建事件运营
func (s *EventOperationService) CreateOperation(req *model.EventOperationCreateRequest) (*model.EventOperation, error) {
	// 验证分类是否存在
	var category model.EventCategory
	err := s.db.Where("id = ?", req.CategoryID).First(&category).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("event category not found")
		}
		return nil, fmt.Errorf("failed to verify category: %v", err)
	}

	// 验证负责人是否存在
	var owner model.User
	err = s.db.Where("id = ?", req.OwnerID).First(&owner).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("owner user not found")
		}
		return nil, fmt.Errorf("failed to verify owner: %v", err)
	}

	// 验证关联员工是否存在
	var relatedEmployeeIDsJSON string
	if len(req.RelatedEmployeeIDs) > 0 {
		//var count int64
		//err = s.db.Model(&model.User{}).Where("id IN ?", req.RelatedEmployeeIDs).Count(&count).Error
		//if err != nil {
		//	return nil, fmt.Errorf("failed to verify related employees: %v", err)
		//}
		//if int(count) != len(req.RelatedEmployeeIDs) {
		//	return nil, fmt.Errorf("some related employees not found")
		//}

		employeeIDsBytes, _ := json.Marshal(req.RelatedEmployeeIDs)
		relatedEmployeeIDsJSON = string(employeeIDsBytes)
	}

	operation := &model.EventOperation{
		ID:                 uuid.New().String(),
		OccurredAt:         req.OccurredAt,
		OccurredCondition:  req.OccurredCondition,
		CategoryID:         req.CategoryID,
		Description:        req.Description,
		OwnerID:            req.OwnerID,
		RelatedEmployeeIDs: relatedEmployeeIDsJSON,
		ProcessType:        req.ProcessType,
		ProcessDescription: req.ProcessDescription,
		ProcessCompletedAt: req.ProcessCompletedAt,
	}

	err = s.db.Create(operation).Error
	if err != nil {
		return nil, fmt.Errorf("failed to create event operation: %v", err)
	}

	// 重新查询以获取关联数据
	return s.GetOperation(operation.ID)
}

// UpdateOperation 更新事件运营
func (s *EventOperationService) UpdateOperation(id string, req *model.EventOperationUpdateRequest) (*model.EventOperation, error) {
	// 检查记录是否存在
	var operation model.EventOperation
	err := s.db.Where("id = ?", id).First(&operation).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("event operation not found")
		}
		return nil, fmt.Errorf("failed to get event operation: %v", err)
	}

	// 验证分类是否存在
	var category model.EventCategory
	err = s.db.Where("id = ?", req.CategoryID).First(&category).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("event category not found")
		}
		return nil, fmt.Errorf("failed to verify category: %v", err)
	}

	// 验证负责人是否存在
	var owner model.User
	err = s.db.Where("id = ?", req.OwnerID).First(&owner).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("owner user not found")
		}
		return nil, fmt.Errorf("failed to verify owner: %v", err)
	}

	// 验证关联员工是否存在
	var relatedEmployeeIDsJSON string
	if len(req.RelatedEmployeeIDs) > 0 {
		var count int64
		err = s.db.Model(&model.User{}).Where("id IN ?", req.RelatedEmployeeIDs).Count(&count).Error
		if err != nil {
			return nil, fmt.Errorf("failed to verify related employees: %v", err)
		}
		if int(count) != len(req.RelatedEmployeeIDs) {
			return nil, fmt.Errorf("some related employees not found")
		}

		employeeIDsBytes, _ := json.Marshal(req.RelatedEmployeeIDs)
		relatedEmployeeIDsJSON = string(employeeIDsBytes)
	}

	// 更新记录
	updates := map[string]interface{}{
		"occurred_at":          req.OccurredAt,
		"occurred_condition":   req.OccurredCondition,
		"category_id":          req.CategoryID,
		"description":          req.Description,
		"owner_id":             req.OwnerID,
		"related_employee_ids": relatedEmployeeIDsJSON,
		"process_type":         req.ProcessType,
		"process_description":  req.ProcessDescription,
		"process_completed_at": req.ProcessCompletedAt,
	}

	err = s.db.Model(&operation).Updates(updates).Error
	if err != nil {
		return nil, fmt.Errorf("failed to update event operation: %v", err)
	}

	// 重新查询以获取关联数据
	return s.GetOperation(id)
}

// DeleteOperation 删除事件运营
func (s *EventOperationService) DeleteOperation(id string) error {
	// 检查记录是否存在
	var operation model.EventOperation
	err := s.db.Where("id = ?", id).First(&operation).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("event operation not found")
		}
		return fmt.Errorf("failed to get event operation: %v", err)
	}

	// 删除记录
	err = s.db.Delete(&operation).Error
	if err != nil {
		return fmt.Errorf("failed to delete event operation: %v", err)
	}

	return nil
}

// BatchDeleteOperations 批量删除事件运营
func (s *EventOperationService) BatchDeleteOperations(ids []string) error {
	if len(ids) == 0 {
		return fmt.Errorf("no operation IDs provided")
	}

	// 批量删除
	err := s.db.Where("id IN ?", ids).Delete(&model.EventOperation{}).Error
	if err != nil {
		return fmt.Errorf("failed to batch delete event operations: %v", err)
	}

	return nil
}

// BatchUpdateOperations 批量更新事件运营
func (s *EventOperationService) BatchUpdateOperations(req *model.EventOperationBatchUpdateRequest) error {
	if len(req.IDs) == 0 {
		return fmt.Errorf("no operation IDs provided")
	}

	updates := make(map[string]interface{})

	if req.ProcessType != "" {
		updates["process_type"] = req.ProcessType
	}
	if req.ProcessDescription != "" {
		updates["process_description"] = req.ProcessDescription
	}
	if req.ProcessCompletedAt != nil {
		updates["process_completed_at"] = req.ProcessCompletedAt
	}

	if len(updates) == 0 {
		return fmt.Errorf("no fields to update")
	}

	err := s.db.Model(&model.EventOperation{}).Where("id IN ?", req.IDs).Updates(updates).Error
	if err != nil {
		return fmt.Errorf("failed to batch update event operations: %v", err)
	}

	return nil
}

// SearchEmployees 搜索员工（用于关联员工下拉框）
func (s *EventOperationService) SearchEmployees(keyword string, limit int) ([]*model.Employee, error) {
	return EmployeeService.SearchEmployees(keyword)
}
