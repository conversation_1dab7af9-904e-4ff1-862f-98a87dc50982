package service

import (
	"fmt"
	"sec-flow-server/internal/model"

	"github.com/google/uuid"
)

// PresetNodeService 预置节点服务
type PresetNodeService struct {
	presetNodes map[string]*model.PresetNode
}

// NewPresetNodeService 创建预置节点服务
func NewPresetNodeService() *PresetNodeService {
	service := &PresetNodeService{
		presetNodes: make(map[string]*model.PresetNode),
	}

	service.initPresetNodes()
	return service
}

// initPresetNodes 初始化预置节点
func (s *PresetNodeService) initPresetNodes() {
	// 用户信息补充节点
	userInfoNode := &model.PresetNode{
		ID:          "preset-user-info",
		Name:        "用户信息补充",
		Description: "根据指定字段补充用户信息",
		Category:    "user",
		NodeType:    "rect",
		Icon:        "user",
		Config: model.PresetNodeConfig{
			Width:     200,
			Height:    120,
			Resizable: true,
			Deletable: true,
			Editable:  true,
			Properties: map[string]interface{}{
				"functionType": "user_info_supplement",
			},
		},
		FormFields: []model.PresetNodeFormField{
			{
				Key:          "sourceField",
				Label:        "用户来源字段",
				Type:         "input",
				Required:     true,
				Placeholder:  "请输入用户来源字段名",
				DefaultValue: "",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 50,
				},
			},
			{
				Key:          "sourceFieldType",
				Label:        "用户来源字段类型",
				Type:         "select",
				Required:     true,
				DefaultValue: "email",
				Options: []model.FormFieldOption{
					{Label: "邮箱", Value: "email"},
					{Label: "用户ID", Value: "user_id"},
					{Label: "用户名", Value: "username"},
				},
			},
			{
				Key:          "outputField",
				Label:        "输出字段",
				Type:         "input",
				Required:     true,
				Placeholder:  "请输入输出字段名",
				DefaultValue: "",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 50,
				},
			},
		},
		Style: model.PresetNodeStyle{
			Fill:         "#e6f7ff",
			Stroke:       "#1890ff",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}

	// 数据转换节点
	dataTransformNode := &model.PresetNode{
		ID:          "preset-data-transform",
		Name:        "数据转换",
		Description: "对数据进行格式转换和处理",
		Category:    "data",
		NodeType:    "rect",
		Icon:        "swap",
		Config: model.PresetNodeConfig{
			Width:     180,
			Height:    100,
			Resizable: true,
			Deletable: true,
			Editable:  true,
			Properties: map[string]interface{}{
				"functionType": "data_transform",
			},
		},
		FormFields: []model.PresetNodeFormField{
			{
				Key:          "inputField",
				Label:        "输入字段",
				Type:         "input",
				Required:     true,
				Placeholder:  "请输入要转换的字段名",
				DefaultValue: "",
			},
			{
				Key:          "transformType",
				Label:        "转换类型",
				Type:         "select",
				Required:     true,
				DefaultValue: "string",
				Options: []model.FormFieldOption{
					{Label: "转为字符串", Value: "string"},
					{Label: "转为数字", Value: "number"},
					{Label: "转为日期", Value: "date"},
					{Label: "转为布尔值", Value: "boolean"},
				},
			},
			{
				Key:          "outputField",
				Label:        "输出字段",
				Type:         "input",
				Required:     true,
				Placeholder:  "请输入输出字段名",
				DefaultValue: "",
			},
		},
		Style: model.PresetNodeStyle{
			Fill:         "#fff7e6",
			Stroke:       "#fa8c16",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}

	// API调用节点
	apiCallNode := &model.PresetNode{
		ID:          "preset-api-call",
		Name:        "API调用",
		Description: "调用外部API接口",
		Category:    "api",
		NodeType:    "rect",
		Icon:        "api",
		Config: model.PresetNodeConfig{
			Width:     220,
			Height:    140,
			Resizable: true,
			Deletable: true,
			Editable:  true,
			Properties: map[string]interface{}{
				"functionType": "api_call",
			},
		},
		FormFields: []model.PresetNodeFormField{
			{
				Key:          "url",
				Label:        "API地址",
				Type:         "input",
				Required:     true,
				Placeholder:  "请输入API地址",
				DefaultValue: "",
			},
			{
				Key:          "method",
				Label:        "请求方法",
				Type:         "select",
				Required:     true,
				DefaultValue: "GET",
				Options: []model.FormFieldOption{
					{Label: "GET", Value: "GET"},
					{Label: "POST", Value: "POST"},
					{Label: "PUT", Value: "PUT"},
					{Label: "DELETE", Value: "DELETE"},
				},
			},
			{
				Key:          "headers",
				Label:        "请求头",
				Type:         "textarea",
				Required:     false,
				Placeholder:  "请输入JSON格式的请求头",
				DefaultValue: "{}",
			},
			{
				Key:          "timeout",
				Label:        "超时时间(秒)",
				Type:         "input",
				Required:     false,
				DefaultValue: "30",
				Validation: map[string]interface{}{
					"type": "number",
					"min":  1,
					"max":  300,
				},
			},
		},
		Style: model.PresetNodeStyle{
			Fill:         "#f6ffed",
			Stroke:       "#52c41a",
			StrokeWidth:  2,
			FontSize:     12,
			FontColor:    "#333",
			BorderRadius: 8,
		},
	}

	// 条件判断节点
	conditionNode := &model.PresetNode{
		ID:          "preset-condition",
		Name:        "条件判断",
		Description: "根据条件进行流程分支",
		Category:    "logic",
		NodeType:    "diamond",
		Icon:        "question-circle",
		Config: model.PresetNodeConfig{
			Width:     160,
			Height:    80,
			Resizable: true,
			Deletable: true,
			Editable:  true,
			Properties: map[string]interface{}{
				"functionType": "condition",
			},
		},
		FormFields: []model.PresetNodeFormField{
			{
				Key:          "field",
				Label:        "判断字段",
				Type:         "input",
				Required:     true,
				Placeholder:  "请输入要判断的字段名",
				DefaultValue: "",
			},
			{
				Key:          "operator",
				Label:        "操作符",
				Type:         "select",
				Required:     true,
				DefaultValue: "equals",
				Options: []model.FormFieldOption{
					{Label: "等于", Value: "equals"},
					{Label: "不等于", Value: "not_equals"},
					{Label: "大于", Value: "greater_than"},
					{Label: "小于", Value: "less_than"},
					{Label: "包含", Value: "contains"},
					{Label: "为空", Value: "is_empty"},
				},
			},
			{
				Key:          "value",
				Label:        "比较值",
				Type:         "input",
				Required:     false,
				Placeholder:  "请输入比较值",
				DefaultValue: "",
			},
		},
		Style: model.PresetNodeStyle{
			Fill:        "#fff2f0",
			Stroke:      "#ff4d4f",
			StrokeWidth: 2,
			FontSize:    12,
			FontColor:   "#333",
		},
	}

	// 存储预置节点
	s.presetNodes[userInfoNode.ID] = userInfoNode
	s.presetNodes[dataTransformNode.ID] = dataTransformNode
	s.presetNodes[apiCallNode.ID] = apiCallNode
	s.presetNodes[conditionNode.ID] = conditionNode
}

// GetPresetNodes 获取预置节点列表
func (s *PresetNodeService) GetPresetNodes(category string) ([]*model.PresetNode, error) {
	var result []*model.PresetNode

	for _, node := range s.presetNodes {
		// 分类过滤
		if category != "" && node.Category != category {
			continue
		}
		result = append(result, node)
	}

	return result, nil
}

// GetPresetNode 获取单个预置节点详情
func (s *PresetNodeService) GetPresetNode(id string) (*model.PresetNode, error) {
	node, exists := s.presetNodes[id]
	if !exists {
		return nil, fmt.Errorf("preset node not found")
	}
	return node, nil
}

// CreatePresetNodeInstance 创建预置节点实例
func (s *PresetNodeService) CreatePresetNodeInstance(req *model.CreatePresetNodeInstanceRequest) (*model.PresetNodeInstance, error) {
	presetNode, err := s.GetPresetNode(req.PresetNodeID)
	if err != nil {
		return nil, err
	}

	instance := &model.PresetNodeInstance{
		ID:           uuid.New().String(),
		PresetNodeID: req.PresetNodeID,
		X:            req.X,
		Y:            req.Y,
		FormData:     req.FormData,
		Config:       presetNode.Config,
		Style:        presetNode.Style,
	}

	return instance, nil
}
