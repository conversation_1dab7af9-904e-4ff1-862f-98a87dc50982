package third_party

import (
	"fmt"
	"sec-flow-server/internal/config"
	"sec-flow-server/internal/service/third_party/aliyun"
	"sec-flow-server/internal/service/third_party/lark"
	"sec-flow-server/internal/service/third_party/mongo"
	"sec-flow-server/internal/service/third_party/yongan"
)

func InitThirdParty() error {
	cfg := config.Get()
	if cfg == nil {
		return fmt.Errorf("配置未加载")
	}
	lark.InitLarkClient(cfg.Third.Lark)
	mongo.InitMongoClient(cfg.Third.Mongo)
	aliyun.InitAliyunSlsClient(cfg.Third.Aliyun, cfg.Databases)
	yongan.InitYonganClient(cfg.Third.Yongan)
	return nil
}
