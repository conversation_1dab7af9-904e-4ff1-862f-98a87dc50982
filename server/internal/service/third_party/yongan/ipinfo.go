package yongan

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/tidwall/gjson"
	"io"
	"net/http"
	"sec-flow-server/internal/config"
	"sec-flow-server/internal/utils"
	"time"
)

const (
	AESBlockSize = 16
	SNKEY        = "your-secret-key" // 请替换为实际的密钥
	SNUSER       = "your-username"   // 请替换为实际的用户名
)

type YongAnClient struct {
	AESBlockSize int
	snKEY        string
	snUSER       string
	PostUrl      string
	httpClient   *http.Client
}

var yonganClient *YongAnClient

func InitYonganClient(config config.YonganConfig) {
	yonganClient = &YongAnClient{
		AESBlockSize: AESBlockSize,
		snKEY:        config.SNKEY,
		snUSER:       config.SNUSER,
		PostUrl:      "https://ipdata.yazx.com/api/ip/v2/check/",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func GetYonganClient() *YongAnClient {
	return yonganClient

}

func (c *YongAnClient) aesEncryptSeg(content string) (string, error) {
	// 生成随机IV
	iv := make([]byte, c.AESBlockSize)
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", err
	}

	// 创建CFB加密器
	block, err := aes.NewCipher([]byte(c.snKEY))
	if err != nil {
		return "", err
	}

	stream := cipher.NewCFBEncrypter(block, iv)
	ciphertext := make([]byte, len(content))
	stream.XORKeyStream(ciphertext, []byte(content))

	// 组合IV和密文
	encrypted := append(iv, ciphertext...)
	return base64.StdEncoding.EncodeToString(encrypted), nil
}

func (c *YongAnClient) aesDecryptSeg(encryptedData string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return "", err
	}

	// 分离IV和密文
	iv := data[:c.AESBlockSize]
	ciphertext := data[c.AESBlockSize:]

	// 创建CFB解密器
	block, err := aes.NewCipher([]byte(c.snKEY))
	if err != nil {
		return "", err
	}

	stream := cipher.NewCFBDecrypter(block, iv)
	plaintext := make([]byte, len(ciphertext))
	stream.XORKeyStream(plaintext, ciphertext)

	return string(plaintext), nil
}

func (c *YongAnClient) getIPInfo(data interface{}) (map[string]interface{}, error) {
	// 序列化数据
	pstr, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	// 加密数据
	cstr, err := c.aesEncryptSeg(string(pstr))
	if err != nil {
		return nil, err
	}

	// 创建请求负载
	payload := map[string]interface{}{
		"snuser": c.snUSER,
		"data":   cstr,
	}

	// 序列化请求体
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}

	// 创建请求
	req, err := http.NewRequest("POST", c.PostUrl, bytes.NewBuffer(jsonPayload))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, err
	}

	if status, ok := response["status"].(float64); ok && status == 200 {
		if data, ok := response["data"].(string); ok {
			ipInfo, err := c.aesDecryptSeg(data)
			if err != nil {
				return nil, err
			}
			return map[string]interface{}{
				"code":    200,
				"ip_info": ipInfo,
			}, nil
		}
	}

	return map[string]interface{}{"code": 0}, nil
}

// 辅助函数：从 JSON 路径获取数据
// 使用 gjson 库的 getData 函数
func getData(jsonStr, path string) string {
	result := gjson.Get(jsonStr, path)
	if result.Exists() {
		return result.String()
	}
	return ""
}

func (c *YongAnClient) GetIPDesc(ip interface{}) (map[string]interface{}, error) {
	isPrivateIP, err := utils.IsPrivateIP(ip)
	if err != nil {
		return nil, err
	}
	if isPrivateIP {
		return map[string]interface{}{"country": "内网", "detail": "内网"}, nil
	}
	reqParam := map[string]interface{}{"ip": ip}
	ipResult, err := c.getIPInfo(reqParam)
	if err != nil {
		return nil, err
	}

	result := make(map[string]interface{})

	if code, ok := ipResult["code"].(int); ok && code == 200 {
		if ipInfo, ok := ipResult["ip_info"].(string); ok {

			riskScore := getData(ipInfo, "risk_score")
			tagName := getData(ipInfo, "score_tag.name")
			country := getData(ipInfo, "location.country")
			city := getData(ipInfo, "location.city")
			owner := getData(ipInfo, "location.owner")
			province := getData(ipInfo, "location.province")
			type_ := getData(ipInfo, "type")

			result["country"] = country

			if riskScore == "" || riskScore == "0" {
				result["detail"] = fmt.Sprintf("%s(0:%s-%s-%s-%s-%s)",
					ip, owner, country, province, city, type_)
			} else {
				result["detail"] = fmt.Sprintf("%s(%s:%s-%s-%s-%s-%s-%s)",
					ip, riskScore, owner, country, province, city, type_, tagName)
			}
		}
	}

	return result, err
}
