package mongo

import (
	"context"
)

// EmailInBlack 检查邮箱是否在黑名单中
func EmailInBlack(email string) (bool, error) {
	ctx := context.Background()

	// 使用配置中的数据库名和集合名
	// 这里假设配置中有数据库名，如果没有可以硬编码或通过参数传入
	dbName := "Reaper" // 可以根据实际配置调整
	collName := "NoticeBlack"

	var result map[string]interface{}
	err := FindOne(ctx, dbName, collName, map[string]interface{}{
		"email": email,
	}, &result)

	if err != nil {
		// 如果没有找到文档，返回false
		if err.Error() == "mongo: no documents in result" {
			return false, nil
		}
		return false, err
	}

	// 如果找到了文档，返回true
	return true, nil
}

// UserIDInBlack 检查用户ID是否在黑名单中
func UserIDInBlack(userID string) (bool, error) {
	ctx := context.Background()

	// 使用配置中的数据库名和集合名
	dbName := "Reaper" // 可以根据实际配置调整
	collName := "NoticeBlack"

	var result map[string]interface{}
	err := FindOne(ctx, dbName, collName, map[string]interface{}{
		"user_id": userID,
	}, &result)

	if err != nil {
		// 如果没有找到文档，返回false
		if err.Error() == "mongo: no documents in result" {
			return false, nil
		}
		return false, err
	}

	// 如果找到了文档，返回true
	return true, nil
}
