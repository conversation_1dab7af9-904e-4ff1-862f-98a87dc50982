package mongo

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"sec-flow-server/internal/config"
)

var (
	client     *mongo.Client
	clientOnce sync.Once
)

// InitMongoClient 初始化MongoDB客户端
func InitMongoClient(cfg config.MongoConfig) error {
	var err error
	clientOnce.Do(func() {
		// 构建URI
		uri := fmt.Sprintf("mongodb://%s", cfg.Address)
		clientOpts := options.Client().ApplyURI(uri)

		// 设置认证
		if cfg.Username != "" && cfg.Password != "" {
			clientOpts.SetAuth(options.Credential{
				Username: cfg.Username,
				Password: cfg.Password,
			})
		}

		// 推荐设置连接池和超时
		clientOpts.SetMaxPoolSize(20)
		clientOpts.SetConnectTimeout(10 * time.Second)
		clientOpts.SetServerSelectionTimeout(10 * time.Second)

		// 连接
		client, err = mongo.Connect(context.Background(), clientOpts)
		if err != nil {
			return
		}
		// 检查连接
		err = client.Ping(context.Background(), nil)
	})
	return err
}

// GetMongoClient 获取全局Mongo客户端
func GetMongoClient() *mongo.Client {
	return client
}

// GetCollection 获取集合
func GetCollection(db, coll string) *mongo.Collection {
	return client.Database(db).Collection(coll)
}

// InsertOne 插入单条文档
func InsertOne(ctx context.Context, db, coll string, doc interface{}) (*mongo.InsertOneResult, error) {
	return GetCollection(db, coll).InsertOne(ctx, doc)
}

// FindOne 查询单条文档
func FindOne(ctx context.Context, db, coll string, filter interface{}, result interface{}) error {
	return GetCollection(db, coll).FindOne(ctx, filter).Decode(result)
}

// FindMany 查询多条文档
func FindMany(ctx context.Context, db, coll string, filter interface{}, results interface{}) error {
	cur, err := GetCollection(db, coll).Find(ctx, filter)
	if err != nil {
		return err
	}
	defer cur.Close(ctx)
	return cur.All(ctx, results)
}

// UpdateOne 更新单条文档
func UpdateOne(ctx context.Context, db, coll string, filter interface{}, update interface{}) (*mongo.UpdateResult, error) {
	return GetCollection(db, coll).UpdateOne(ctx, filter, update)
}

// DeleteOne 删除单条文档
func DeleteOne(ctx context.Context, db, coll string, filter interface{}) (*mongo.DeleteResult, error) {
	return GetCollection(db, coll).DeleteOne(ctx, filter)
}

// Aggregate 执行聚合查询
func Aggregate(ctx context.Context, db, coll string, pipeline interface{}, results interface{}) error {
	cur, err := GetCollection(db, coll).Aggregate(ctx, pipeline)
	if err != nil {
		return err
	}
	defer cur.Close(ctx)
	return cur.All(ctx, results)
}
