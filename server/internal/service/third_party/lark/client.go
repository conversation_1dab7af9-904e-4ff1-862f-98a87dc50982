package lark

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/url"
	"sec-flow-server/internal/config"
	"time"
)

type LarkClientStruct struct {
	baseUrl           string
	appId             string
	appSecret         string
	tenantAccessToken string
	appAccessToken    string
	expire            time.Time
	httpClient        *http.Client
}

var larkClient LarkClientStruct

func InitLarkClient(cfg config.LarkConfig) {
	httpClient := &http.Client{
		Timeout: 30 * time.Second, // 设置超时时间
	}
	larkClient = LarkClientStruct{
		baseUrl:    "https://open.feishu.cn",
		appId:      cfg.AppId,
		appSecret:  cfg.AppSecret,
		httpClient: httpClient,
	}
}

// GetLarkClient 获取飞书客户端实例
func GetLarkClient() *LarkClientStruct {
	return &larkClient
}

func (l *LarkClientStruct) getAppAccessToken() error {
	reqParam := make(map[string]string)
	reqParam["app_id"] = l.appId
	reqParam["app_secret"] = l.appSecret
	marshal, _ := json.Marshal(reqParam)
	buffer := bytes.NewBuffer(marshal)
	resp, err := l.httpClient.Post(l.baseUrl+"/open-apis/auth/v3/app_access_token/internal", "application/json", buffer)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	var result map[string]interface{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		return err
	}
	code, ok := result["code"]
	if !ok || code.(float64) != 0 {
		return errors.New(string(body))
	}
	l.tenantAccessToken = result["tenant_access_token"].(string)
	l.appAccessToken = result["app_access_token"].(string)
	exp := result["expire"].(float64)
	l.expire = time.Now().Add(time.Duration(int64(exp)) * time.Second)
	return nil
}

// checkAndRefreshToken 检查并刷新token
func (l *LarkClientStruct) checkAndRefreshToken() error {
	// 检查是否需要刷新token：
	// 1. tenantAccessToken 为空
	// 2. 当前时间距离过期时间在10分钟以内
	// 3. 已经过期
	if l.tenantAccessToken == "" || time.Until(l.expire) <= 10*time.Minute {
		return l.getAppAccessToken()
	}
	return nil
}

// Get 发送GET请求
func (l *LarkClientStruct) Get(urlPath string, queryParams map[string]string, headers map[string]string) (*http.Response, error) {
	// 检查并刷新token
	if err := l.checkAndRefreshToken(); err != nil {
		return nil, err
	}

	// 构建完整URL
	fullUrl := l.baseUrl + urlPath

	// 添加查询参数
	if len(queryParams) > 0 {
		u, err := url.Parse(fullUrl)
		if err != nil {
			return nil, err
		}

		query := u.Query()
		for key, value := range queryParams {
			query.Set(key, value)
		}
		u.RawQuery = query.Encode()
		fullUrl = u.String()
	}

	// 创建GET请求
	req, err := http.NewRequest("GET", fullUrl, nil)
	if err != nil {
		return nil, err
	}

	// 设置默认请求头
	req.Header.Set("Authorization", "Bearer "+l.tenantAccessToken)
	req.Header.Set("Content-Type", "application/json")

	// 覆盖自定义请求头
	if headers != nil {
		for key, value := range headers {
			req.Header.Set(key, value)
		}
	}

	// 发送请求
	return l.httpClient.Do(req)
}

// Post 发送POST请求
func (l *LarkClientStruct) Post(urlPath string, queryParams map[string]string, body interface{}, headers map[string]string) (*http.Response, error) {
	// 检查并刷新token
	if err := l.checkAndRefreshToken(); err != nil {
		return nil, err
	}

	// 构建完整URL
	fullUrl := l.baseUrl + urlPath

	// 添加查询参数
	if len(queryParams) > 0 {
		u, err := url.Parse(fullUrl)
		if err != nil {
			return nil, err
		}

		query := u.Query()
		for key, value := range queryParams {
			query.Set(key, value)
		}
		u.RawQuery = query.Encode()
		fullUrl = u.String()
	}

	// 序列化请求体
	var bodyReader *bytes.Buffer
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		bodyReader = bytes.NewBuffer(jsonBody)
	} else {
		bodyReader = bytes.NewBuffer([]byte{})
	}

	// 创建POST请求
	req, err := http.NewRequest("POST", fullUrl, bodyReader)
	if err != nil {
		return nil, err
	}

	// 设置默认请求头
	req.Header.Set("Authorization", "Bearer "+l.tenantAccessToken)
	req.Header.Set("Content-Type", "application/json")

	// 覆盖自定义请求头
	if headers != nil {
		for key, value := range headers {
			req.Header.Set(key, value)
		}
	}

	// 发送请求
	return l.httpClient.Do(req)
}

// GetWithResponse 发送GET请求并解析响应
func (l *LarkClientStruct) GetWithResponse(urlPath string, queryParams map[string]string, headers map[string]string, result interface{}) error {
	resp, err := l.Get(urlPath, queryParams, headers)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	return json.Unmarshal(body, result)
}

// PostWithResponse 发送POST请求并解析响应
func (l *LarkClientStruct) PostWithResponse(urlPath string, queryParams map[string]string, reqBody interface{}, headers map[string]string, result interface{}) error {
	resp, err := l.Post(urlPath, queryParams, reqBody, headers)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	return json.Unmarshal(body, result)
}
