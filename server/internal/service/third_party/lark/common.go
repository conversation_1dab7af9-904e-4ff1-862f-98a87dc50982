package lark

import (
	"fmt"
	"strings"
	"sync"
	"time"
)

// User 飞书用户结构体
type User struct {
	UserID        string   `json:"user_id"`
	Name          string   `json:"name"`
	Email         string   `json:"email"`
	LeaderUserID  string   `json:"leader_user_id"`
	DepartmentIDs []string `json:"department_ids"`
	JoinTime      int64    `json:"join_time"`
	Status        struct {
		IsResigned bool `json:"is_resigned"`
	} `json:"status"`
	CustomAttrs []CustomAttr `json:"custom_attrs"`
	Dept        string       `json:"dept"`
	Leader      LeaderInfo   `json:"leader"`
}

// CustomAttr 自定义属性
type CustomAttr struct {
	ID    string `json:"id"`
	Value struct {
		Text string `json:"text"`
	} `json:"value"`
}

// Department 部门结构体
type Department struct {
	DepartmentID string `json:"department_id"`
	Name         string `json:"name"`
}

// LeaderInfo 领导信息
type LeaderInfo struct {
	LeaderID    string `json:"leader_id"`
	LeaderName  string `json:"leader_name"`
	LeaderEmail string `json:"leader_email"`
}

// 缓存与过期控制（每天中午12点过期）
var (
	cacheControlMu sync.Mutex
	cacheExpiry    time.Time

	larkUserMu    sync.RWMutex
	larkUserCache map[string]*User // key: userID

	leaderMu    sync.RWMutex
	leaderCache map[string]*LeaderInfo // key: leaderUserID

	deptMu        sync.RWMutex
	deptNameCache map[string]string // key: firstDeptID
)

// nextExpiryTime 计算下一个过期时间点：本地时区的最近一次中午12点或夜晚0点（二者中的最近一个）
func nextExpiryTime(now time.Time) time.Time {
	localNow := now.In(time.Local)
	y, m, d := localNow.Date()

	// 今天的中午12点
	todayNoon := time.Date(y, m, d, 12, 0, 0, 0, time.Local)
	// 明天的0点
	tomorrowMidnight := time.Date(y, m, d, 0, 0, 0, 0, time.Local).Add(24 * time.Hour)

	if localNow.Before(todayNoon) {
		// 现在到今天中午更近
		return todayNoon
	}
	// 现在已经过了今天中午，则下一个过期点是今晚0点
	return tomorrowMidnight
}

// ensureCacheFresh 保证缓存结构存在并在过期后重置
func ensureCacheFresh() {
	cacheControlMu.Lock()
	defer cacheControlMu.Unlock()

	// 初始化
	if larkUserCache == nil {
		larkUserCache = make(map[string]*User)
	}
	if leaderCache == nil {
		leaderCache = make(map[string]*LeaderInfo)
	}
	if deptNameCache == nil {
		deptNameCache = make(map[string]string)
	}

	// 过期检测（首次也会被视为过期，从而设置正确的到期时间）
	if time.Now().After(cacheExpiry) {
		larkUserMu.Lock()
		larkUserCache = make(map[string]*User)
		larkUserMu.Unlock()

		leaderMu.Lock()
		leaderCache = make(map[string]*LeaderInfo)
		leaderMu.Unlock()

		deptMu.Lock()
		deptNameCache = make(map[string]string)
		deptMu.Unlock()

		cacheExpiry = nextExpiryTime(time.Now())
	}
}

// UserResponse 用户API响应
type UserResponse struct {
	Code int `json:"code"`
	Data struct {
		User *User `json:"user"`
	} `json:"data"`
}

// DepartmentResponse 部门API响应
type DepartmentResponse struct {
	Code int `json:"code"`
	Data struct {
		Department *Department `json:"department"`
	} `json:"data"`
}

// ParentDepartmentResponse 父部门API响应
type ParentDepartmentResponse struct {
	Code int `json:"code"`
	Data struct {
		Items []Department `json:"items"`
	} `json:"data"`
}

// GetLarkUser 获取飞书用户信息
func GetLarkUser(userID string) (*User, error) {
	if userID == "" {
		return nil, nil
	}

	ensureCacheFresh()

	// 读缓存
	larkUserMu.RLock()
	if u, ok := larkUserCache[userID]; ok && u != nil {
		larkUserMu.RUnlock()
		return u, nil
	}
	larkUserMu.RUnlock()

	client := GetLarkClient()
	var resp UserResponse

	err := client.GetWithResponse(
		fmt.Sprintf("/open-apis/contact/v3/users/%s", userID),
		map[string]string{"user_id_type": "user_id"},
		nil,
		&resp,
	)

	if err != nil {
		return nil, err
	}

	if resp.Code == 0 && resp.Data.User != nil {
		// 写缓存（仅缓存成功结果）
		larkUserMu.Lock()
		larkUserCache[userID] = resp.Data.User
		larkUserMu.Unlock()
		return resp.Data.User, nil
	}

	return nil, nil
}

// GetLeaderByLarkUser 根据飞书用户获取领导信息
func GetLeaderByLarkUser(user *User) (*LeaderInfo, error) {
	if user == nil {
		return nil, nil
	}

	leaderID := user.LeaderUserID
	if leaderID == "" {
		return nil, nil
	}

	ensureCacheFresh()

	// 读缓存
	leaderMu.RLock()
	if info, ok := leaderCache[leaderID]; ok && info != nil {
		leaderMu.RUnlock()
		return info, nil
	}
	leaderMu.RUnlock()

	client := GetLarkClient()
	var resp UserResponse

	err := client.GetWithResponse(
		fmt.Sprintf("/open-apis/contact/v3/users/%s", leaderID),
		map[string]string{"user_id_type": "user_id"},
		nil,
		&resp,
	)

	if err != nil {
		return nil, err
	}

	if resp.Code == 0 && resp.Data.User != nil {
		leader := resp.Data.User
		info := &LeaderInfo{
			LeaderID:    leaderID,
			LeaderName:  leader.Name,
			LeaderEmail: leader.Email,
		}

		// 写缓存
		leaderMu.Lock()
		leaderCache[leaderID] = info
		leaderMu.Unlock()

		return info, nil
	}

	return nil, nil
}

// GetParentDepts 获取父部门信息
func GetParentDepts(departmentID string, names []string) string {
	client := GetLarkClient()
	var resp ParentDepartmentResponse

	err := client.GetWithResponse(
		"/open-apis/contact/v3/departments/parent",
		map[string]string{"department_id": departmentID},
		nil,
		&resp,
	)

	if err != nil {
		return strings.Join(names, "-")
	}

	if resp.Code == 0 && len(resp.Data.Items) > 0 {
		for _, item := range resp.Data.Items {
			names = append(names, item.Name)
		}
	}

	// 反转数组
	for i, j := 0, len(names)-1; i < j; i, j = i+1, j-1 {
		names[i], names[j] = names[j], names[i]
	}

	return strings.Join(names, "-")
}

// GetDeptName 获取部门名称
func GetDeptName(departmentID string) (string, error) {
	client := GetLarkClient()
	var resp DepartmentResponse

	err := client.GetWithResponse(
		fmt.Sprintf("/open-apis/contact/v3/departments/%s", departmentID),
		nil,
		nil,
		&resp,
	)

	if err != nil {
		return "", err
	}

	if resp.Code == 0 && resp.Data.Department != nil {
		return resp.Data.Department.Name, nil
	}

	return "", nil
}

// DeptNameTop 获取完整部门路径
func DeptNameTop(departmentID string) (string, error) {
	thisName, err := GetDeptName(departmentID)
	if err != nil {
		return "", err
	}

	if thisName == "" {
		return "", nil
	}

	names := []string{thisName}
	return GetParentDepts(departmentID, names), nil
}

// GetDeptByIDs 根据部门ID列表获取部门名称
func GetDeptByIDs(deptIDs []string) (string, error) {
	if len(deptIDs) > 0 {
		deptID := deptIDs[0]
		return DeptNameTop(deptID)
	}
	return "", nil
}

// GetDeptByLarkUser 根据飞书用户获取部门信息
func GetDeptByLarkUser(user *User) (string, error) {
	if user == nil || len(user.DepartmentIDs) == 0 {
		return "", nil
	}

	ensureCacheFresh()

	firstDeptID := user.DepartmentIDs[0]

	// 读缓存
	deptMu.RLock()
	if name, ok := deptNameCache[firstDeptID]; ok {
		deptMu.RUnlock()
		return name, nil
	}
	deptMu.RUnlock()

	name, err := GetDeptByIDs(user.DepartmentIDs)
	if err != nil {
		return "", err
	}

	// 写缓存（仅缓存非空结果）
	if name != "" {
		deptMu.Lock()
		deptNameCache[firstDeptID] = name
		deptMu.Unlock()
	}

	return name, nil
}

// GetJobTypeByUser 根据用户获取岗位类型
func GetJobTypeByUser(user *User) string {
	if user == nil || len(user.CustomAttrs) == 0 {
		return ""
	}

	for _, customAttr := range user.CustomAttrs {
		// 疑似岗位
		if customAttr.ID == "C-6832109570425372674" {
			return customAttr.Value.Text
		}
	}

	return ""
}
