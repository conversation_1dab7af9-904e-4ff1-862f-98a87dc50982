package lark

import (
	"encoding/json"
	"fmt"
	"log"
)

// 飞书API响应的通用结构
type LarkResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// 用户信息结构
type UserInfo struct {
	UserId    string `json:"user_id"`
	Name      string `json:"name"`
	AvatarUrl string `json:"avatar_url"`
	Email     string `json:"email"`
}

// 发送消息请求结构
type SendMessageRequest struct {
	ReceiveId string `json:"receive_id"`
	MsgType   string `json:"msg_type"`
	Content   string `json:"content"`
}

// 使用示例函数

// GetUserInfo 获取用户信息示例
func GetUserInfo(userId string) (*UserInfo, error) {
	client := GetLarkClient()

	// 构建API路径
	url := fmt.Sprintf("/open-apis/contact/v3/users/%s", userId)

	var response LarkResponse
	err := client.GetWithResponse(url, nil, nil, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %v", err)
	}

	if response.Code != 0 {
		return nil, fmt.Errorf("lark API error: code=%d, msg=%s", response.Code, response.Msg)
	}

	// 解析用户信息
	userBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal user data: %v", err)
	}

	var userInfo UserInfo
	err = json.Unmarshal(userBytes, &userInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal user info: %v", err)
	}

	return &userInfo, nil
}

// SendTextMessage 发送文本消息示例
func SendTextMessage(receiveId, content string) error {
	client := GetLarkClient()

	// 构建请求体
	request := SendMessageRequest{
		ReceiveId: receiveId,
		MsgType:   "text",
		Content:   fmt.Sprintf(`{"text": "%s"}`, content),
	}

	var response LarkResponse
	err := client.PostWithResponse("/open-apis/im/v1/messages", nil, request, nil, &response)
	if err != nil {
		return fmt.Errorf("failed to send message: %v", err)
	}

	if response.Code != 0 {
		return fmt.Errorf("lark API error: code=%d, msg=%s", response.Code, response.Msg)
	}

	log.Printf("Message sent successfully to %s", receiveId)
	return nil
}

// GetDepartmentList 获取部门列表示例
func GetDepartmentList() ([]interface{}, error) {
	client := GetLarkClient()

	var response LarkResponse
	err := client.GetWithResponse("/open-apis/contact/v3/departments", nil, nil, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to get department list: %v", err)
	}

	if response.Code != 0 {
		return nil, fmt.Errorf("lark API error: code=%d, msg=%s", response.Code, response.Msg)
	}

	// 返回部门数据
	if data, ok := response.Data.(map[string]interface{}); ok {
		if items, ok := data["items"].([]interface{}); ok {
			return items, nil
		}
	}

	return nil, fmt.Errorf("invalid response data format")
}

// UploadImage 上传图片示例（使用原始HTTP响应处理）
func UploadImage(imageData []byte, fileName string) (string, error) {
	client := GetLarkClient()

	// 对于文件上传，可能需要特殊处理，这里展示如何获取原始响应
	resp, err := client.Post("/open-apis/im/v1/images", nil, map[string]interface{}{
		"image_type": "message",
		"image":      imageData,
	}, nil)
	if err != nil {
		return "", fmt.Errorf("failed to upload image: %v", err)
	}
	defer resp.Body.Close()

	var response LarkResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %v", err)
	}

	if response.Code != 0 {
		return "", fmt.Errorf("lark API error: code=%d, msg=%s", response.Code, response.Msg)
	}

	// 提取图片ID
	if data, ok := response.Data.(map[string]interface{}); ok {
		if imageKey, ok := data["image_key"].(string); ok {
			return imageKey, nil
		}
	}

	return "", fmt.Errorf("failed to get image key from response")
}

// BatchGetUsers 批量获取用户信息示例
func BatchGetUsers(userIds []string) (map[string]*UserInfo, error) {
	result := make(map[string]*UserInfo)

	for _, userId := range userIds {
		userInfo, err := GetUserInfo(userId)
		if err != nil {
			log.Printf("Failed to get user info for %s: %v", userId, err)
			continue
		}
		result[userId] = userInfo
	}

	return result, nil
}

// 演示如何处理分页数据
func GetAllUsers() ([]UserInfo, error) {
	client := GetLarkClient()
	var allUsers []UserInfo
	pageToken := ""

	for {
		// 构建查询参数
		queryParams := map[string]string{
			"page_size": "100",
		}
		if pageToken != "" {
			queryParams["page_token"] = pageToken
		}

		var response LarkResponse
		err := client.GetWithResponse("/open-apis/contact/v3/users", queryParams, nil, &response)
		if err != nil {
			return nil, fmt.Errorf("failed to get users: %v", err)
		}

		if response.Code != 0 {
			return nil, fmt.Errorf("lark API error: code=%d, msg=%s", response.Code, response.Msg)
		}

		// 解析响应数据
		if data, ok := response.Data.(map[string]interface{}); ok {
			if items, ok := data["items"].([]interface{}); ok {
				for _, item := range items {
					userBytes, _ := json.Marshal(item)
					var user UserInfo
					if json.Unmarshal(userBytes, &user) == nil {
						allUsers = append(allUsers, user)
					}
				}
			}

			// 检查是否还有下一页
			if hasMore, ok := data["has_more"].(bool); ok && hasMore {
				if nextPageToken, ok := data["page_token"].(string); ok {
					pageToken = nextPageToken
					continue
				}
			}
		}

		// 没有更多数据，退出循环
		break
	}

	return allUsers, nil
}

// 使用示例：展示查询参数和自定义请求头
func ExampleWithCustomParams() error {
	client := GetLarkClient()

	// 示例1: 使用查询参数
	queryParams := map[string]string{
		"user_type":     "user",
		"page_size":     "50",
		"department_id": "123",
	}

	var response LarkResponse
	err := client.GetWithResponse("/open-apis/contact/v3/users", queryParams, nil, &response)
	if err != nil {
		return fmt.Errorf("failed to get users with params: %v", err)
	}

	// 示例2: 使用自定义请求头
	customHeaders := map[string]string{
		"X-Custom-Header": "custom-value",
		"Accept-Language": "zh-CN",
	}

	err = client.GetWithResponse("/open-apis/contact/v3/departments", nil, customHeaders, &response)
	if err != nil {
		return fmt.Errorf("failed to get departments with headers: %v", err)
	}

	// 示例3: 同时使用查询参数和自定义请求头
	searchParams := map[string]string{
		"query":     "张三",
		"page_size": "10",
	}

	searchHeaders := map[string]string{
		"Accept":     "application/json",
		"User-Agent": "CustomClient/1.0",
	}

	err = client.GetWithResponse("/open-apis/search/v2/user", searchParams, searchHeaders, &response)
	if err != nil {
		return fmt.Errorf("failed to search users: %v", err)
	}

	return nil
}

// POST请求使用自定义头的示例
func ExamplePostWithCustomHeaders() error {
	client := GetLarkClient()

	// 发送消息时使用自定义请求头
	request := SendMessageRequest{
		ReceiveId: "user123",
		MsgType:   "text",
		Content:   `{"text": "Hello with custom headers"}`,
	}

	customHeaders := map[string]string{
		"X-Request-ID": "req-12345",
		"Content-Type": "application/json; charset=utf-8",
	}

	var response LarkResponse
	err := client.PostWithResponse("/open-apis/im/v1/messages", nil, request, customHeaders, &response)
	if err != nil {
		return fmt.Errorf("failed to send message with custom headers: %v", err)
	}

	if response.Code != 0 {
		return fmt.Errorf("API error: code=%d, msg=%s", response.Code, response.Msg)
	}

	log.Printf("Message sent successfully with custom headers")
	return nil
}
