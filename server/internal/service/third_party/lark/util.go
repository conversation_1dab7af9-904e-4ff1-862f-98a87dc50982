package lark

import (
	"encoding/json"
	"sec-flow-server/internal/service/third_party/mongo"
)

// SendCardMessage 发送文本消息示例
func SendCardMessage(receiveIdType, receiveId, content string, result interface{}) error {
	if receiveIdType == "user_id" {
		black, err := mongo.UserIDInBlack(receiveId)
		if black {
			return nil
		}
		if err != nil {
			return err
		}
	}
	client := GetLarkClient()

	queryParams := make(map[string]string)
	queryParams["receive_id_type"] = receiveIdType

	// 构建请求体
	request := SendMessageRequest{
		ReceiveId: receiveId,
		MsgType:   "interactive",
		Content:   content,
	}

	return client.PostWithResponse("/open-apis/im/v1/messages", queryParams, request, nil, result)
}

func FillLarkUserInfo(userId string) (map[string]interface{}, error) {
	larkUser, err := GetLarkUser(userId)
	if err != nil {
		return nil, err
	}
	leader, err := GetLeaderByLarkUser(larkUser)
	if err == nil && leader != nil {
		larkUser.Leader = *leader
	}
	Dept, err := GetDeptByLarkUser(larkUser)
	if err == nil {
		larkUser.Dept = Dept
	}
	marshal, _ := json.Marshal(larkUser)

	var user map[string]interface{}
	err = json.Unmarshal(marshal, &user)
	if err != nil {
		return nil, err
	}
	return user, nil
	//client := GetLarkClient()
	//queryParams := make(map[string]string)
	//queryParams["user_id_type"] = "user_id"
	//u := fmt.Sprintf("/open-apis/contact/v3/users/%s", userId)
	//result := make(map[string]interface{})
	//err := client.GetWithResponse(u, queryParams, nil, &result)
	//if err != nil {
	//	return nil, err
	//}
	//if code, ok := result["code"]; ok && code == 0 {
	//	return result["data"].(map[string]interface{})["user"].(map[string]interface{}), nil
	//}
	//return nil, errors.New(result["msg"].(string))
}
