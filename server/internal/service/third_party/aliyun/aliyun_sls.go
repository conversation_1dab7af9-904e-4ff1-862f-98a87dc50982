package aliyun

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sec-flow-server/internal/config"
	"sort"
	"strings"
	"time"
)

// Config 阿里云配置结构
type Config struct {
	AccessKeyId string `json:"AccessKeyId"`
	AccessKey   string `json:"AccessKey"`
}

// GetLogsRequest SLS GetLogs 请求参数
type GetLogsRequest struct {
	Project  string            `json:"project"`
	Logstore string            `json:"logstore"`
	EndPoint string            `json:"endPoint,omitempty"`
	Method   string            `json:"method,omitempty"`
	Args     map[string]string `json:"args,omitempty"`
}

// GetLogsResponse SLS GetLogs 响应结构
type GetLogsResponse struct {
	Error   string      `json:"error,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Code    int         `json:"code,omitempty"`
	Message string      `json:"message,omitempty"`
}

// AliyunSLSClient 阿里云 SLS 客户端
type AliyunSLSClient struct {
	config *Config
	client *http.Client
}

// NewAliyunSLSClient 创建新的阿里云 SLS 客户端
func NewAliyunSLSClient(config *Config) *AliyunSLSClient {
	return &AliyunSLSClient{
		config: config,
		client: &http.Client{
			Timeout: 300 * time.Second,
		},
	}
}

// GetLogs 获取阿里云 SLS 日志
// 对应 Python 版本的 getLogs 函数
func (c *AliyunSLSClient) GetLogs(req *GetLogsRequest) (*GetLogsResponse, error) {
	// 设置默认值
	endPointNew := req.EndPoint
	if endPointNew == "" {
		endPointNew = "cn-beijing.log.aliyuncs.com"
	}

	method := req.Method
	if method == "" {
		method = "GET"
	}

	// 构建请求头
	now := time.Now().UTC()
	dateStr := now.Format("Mon, 02 Jan 2006 15:04:05 GMT")

	headers := map[string]string{
		"Accept":                "application/json",
		"Date":                  dateStr,
		"x-log-apiversion":      "0.6.0",
		"x-log-signaturemethod": "hmac-sha1",
		"Host":                  fmt.Sprintf("%s.%s", req.Project, endPointNew),
	}

	// 构建签名字符串
	signString := c.buildSignString(method, dateStr, headers, req.Logstore, req.Args)

	// 计算签名
	signature := c.calculateSignature(signString)

	// 添加授权头
	headers["Authorization"] = fmt.Sprintf("LOG %s:%s", c.config.AccessKeyId, signature)

	// 构建请求 URL
	requestURL := fmt.Sprintf("https://%s.%s/logstores/%s/index", req.Project, endPointNew, req.Logstore)

	// 创建 HTTP 请求
	httpReq, err := http.NewRequest(method, requestURL, nil)
	if err != nil {
		return &GetLogsResponse{Error: err.Error()}, err
	}

	// 添加请求头
	for key, value := range headers {
		httpReq.Header.Set(key, value)
	}

	// 添加查询参数
	if req.Args != nil {
		q := httpReq.URL.Query()
		for key, value := range req.Args {
			q.Add(key, value)
		}
		httpReq.URL.RawQuery = q.Encode()
	}

	// 发送请求
	resp, err := c.client.Do(httpReq)
	if err != nil {
		return &GetLogsResponse{Error: err.Error()}, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return &GetLogsResponse{Error: err.Error()}, err
	}

	// 解析响应
	var result GetLogsResponse
	var dataObj []interface{}
	if err := json.Unmarshal(body, &dataObj); err != nil {
		return &GetLogsResponse{Error: err.Error()}, err
	}
	result.Data = dataObj

	return &result, nil
}

// buildSignString 构建签名字符串
// 对应 Python 版本中的签名字符串构建逻辑
func (c *AliyunSLSClient) buildSignString(method, dateStr string, headers map[string]string, logstore string, args map[string]string) string {
	var parts []string

	// 第一部分：HTTP 方法
	parts = append(parts, method)

	// 第二部分：空行
	parts = append(parts, "")

	// 第三部分：空行
	parts = append(parts, "")

	// 第四部分：日期
	parts = append(parts, dateStr)

	// 第五部分：x-log- 开头的头部信息
	var xLogHeaders []string
	for key, value := range headers {
		if strings.HasPrefix(key, "x-log-") {
			xLogHeaders = append(xLogHeaders, fmt.Sprintf("%s:%s", key, value))
		}
	}
	sort.Strings(xLogHeaders)
	parts = append(parts, strings.Join(xLogHeaders, "\n"))

	// 第六部分：资源路径和查询参数
	resourcePath := fmt.Sprintf("/logstores/%s/index", logstore)
	if args != nil && len(args) > 0 {
		var queryParams []string
		for key, value := range args {
			queryParams = append(queryParams, fmt.Sprintf("%s=%s", key, value))
		}
		sort.Strings(queryParams)
		resourcePath += "?" + strings.Join(queryParams, "&")
	}
	parts = append(parts, resourcePath)

	return strings.Join(parts, "\n")
}

// calculateSignature 计算 HMAC-SHA1 签名
// 对应 Python 版本中的签名计算逻辑
func (c *AliyunSLSClient) calculateSignature(signString string) string {
	h := hmac.New(sha1.New, []byte(c.config.AccessKey))
	h.Write([]byte(signString))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return signature
}

// 工具函数：URL 编码
func encodeURL(msg string) string {
	return url.QueryEscape(msg)
}

// 工具函数：构建查询字符串
func buildQueryString(args map[string]string) string {
	if args == nil || len(args) == 0 {
		return ""
	}

	var pairs []string
	for key, value := range args {
		pairs = append(pairs, fmt.Sprintf("%s=%s", key, value))
	}
	sort.Strings(pairs)
	return strings.Join(pairs, "&")
}

var client *AliyunSLSClient

// GetAliyunSLSClient 获取阿里云SLS客户端实例
func GetAliyunSLSClient() *AliyunSLSClient {
	return client
}

// 示例使用函数
func InitAliyunSlsClient(cfg config.AliyunConfig, databases []config.DbConfig) {
	// 创建配置
	config := &Config{
		AccessKeyId: cfg.AccessKeyId,
		AccessKey:   cfg.AccessKey,
	}

	// 创建客户端
	client = NewAliyunSLSClient(config)
	monitor = NewPrimeMonitor(databases)
}
