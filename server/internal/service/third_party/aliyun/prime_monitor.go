package aliyun

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"sec-flow-server/internal/config"
	"sec-flow-server/internal/utils"
	"strconv"
	"strings"
	"time"

	"github.com/tidwall/gjson"
)

// UserPhoneData 用户手机号数据
type UserPhoneData struct {
	DecodedPhones map[string]bool `json:"decoded_phones"`
	GlobalUserID  string          `json:"global_user_id"`
	Name          string          `json:"name"`
	PhoneCount    int             `json:"phone_count"`
}

type ScanResult struct {
	StartTime string                   `json:"start_time"`
	EndTime   string                   `json:"end_time"`
	Datas     []map[string]interface{} `json:"datas"`
}

// LogItem 日志项结构
type LogItem struct {
	Req  string `json:"req"`
	Resp string `json:"resp"`
}

// NoticeContent 通知内容结构
type NoticeContent struct {
	Tag     string `json:"tag"`
	Content string `json:"content"`
}

// AlarmRecord 告警记录结构
type AlarmRecord struct {
	ID             string    `json:"_id,omitempty"`
	GlobalUserID   string    `json:"global_user_id"`
	Name           string    `json:"name"`
	Date           string    `json:"date"`
	AlarmType      string    `json:"alarm_type"`
	PhoneCount     int       `json:"phone_count"`
	Threshold      int       `json:"threshold"`
	FirstAlertTime time.Time `json:"first_alert_time"`
	LastAlertTime  time.Time `json:"last_alert_time"`
}

// SensitiveField 敏感字段枚举
type SensitiveField string

const (
	SensitiveFieldMobile     SensitiveField = "mobile"
	SensitiveFieldCardNumber SensitiveField = "card_number"
	SensitiveFieldRealname   SensitiveField = "realname"
	SensitiveFieldAddress    SensitiveField = "address"
)

// UserSensitiveData 用户敏感信息聚合
type UserSensitiveData struct {
	GlobalUserID string
	Name         string
	CustomerIDs  map[string]struct{}
	Values       map[SensitiveField]map[string]struct{}
	FirstSeen    time.Time
	LastSeen     time.Time
	PathCounts   map[string]int
}

func newUserSensitiveData(uid, name string, t time.Time) *UserSensitiveData {
	return &UserSensitiveData{
		GlobalUserID: uid,
		Name:         name,
		CustomerIDs:  make(map[string]struct{}),
		Values: map[SensitiveField]map[string]struct{}{
			SensitiveFieldMobile:     {},
			SensitiveFieldCardNumber: {},
			SensitiveFieldRealname:   {},
			SensitiveFieldAddress:    {},
		},
		FirstSeen:  t,
		LastSeen:   t,
		PathCounts: make(map[string]int),
	}
}

func (u *UserSensitiveData) addCustomerID(customerID string) {
	if customerID == "" {
		return
	}
	u.CustomerIDs[customerID] = struct{}{}
}

func (u *UserSensitiveData) addValue(field SensitiveField, value string) {
	if value == "" {
		return
	}
	if _, ok := u.Values[field]; !ok {
		u.Values[field] = make(map[string]struct{})
	}
	u.Values[field][value] = struct{}{}
}

func normalizeField(key string) (SensitiveField, bool) {
	switch key {
	case "mobile", "encrypt_mobile":
		return SensitiveFieldMobile, true
	case "card_number", "id_card":
		return SensitiveFieldCardNumber, true
	case "realname":
		return SensitiveFieldRealname, true
	case "address", "contact_address":
		return SensitiveFieldAddress, true
	default:
		return "", false
	}
}

// PrimeMonitor Prime CRM 监控器
type PrimeMonitor struct {
	databases []config.DbConfig
}

var monitor *PrimeMonitor

// NewPrimeMonitor 创建新的 Prime 监控器
func NewPrimeMonitor(databases []config.DbConfig) *PrimeMonitor {
	return &PrimeMonitor{
		databases: databases,
	}
}

// decodeJWTPayload 解析 JWT token 的 payload 部分，不验证签名
// 对应 Python 版本的 decode_jwt_payload 函数
func decodeJWTPayload(token string) (map[string]interface{}, error) {
	// JWT token 格式: header.payload.signature
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("invalid JWT token format")
	}

	// 解码 payload 部分（第二部分）
	payload := parts[1]

	// 添加 padding 如果需要
	padding := len(payload) % 4
	if padding > 0 {
		payload += strings.Repeat("=", 4-padding)
	}

	// Base64 解码
	decodedBytes, err := base64.URLEncoding.DecodeString(payload)
	if err != nil {
		return nil, fmt.Errorf("base64 decode error: %v", err)
	}

	// JSON 解析
	var payloadData map[string]interface{}
	if err := json.Unmarshal(decodedBytes, &payloadData); err != nil {
		return nil, fmt.Errorf("JSON unmarshal error: %v", err)
	}

	return payloadData, nil
}

// isValidPhone 检查是否为有效的11位手机号
// 对应 Python 版本的 is_valid_phone 函数
func isValidPhone(phoneStr string) bool {
	if phoneStr == "" {
		return false
	}

	// 去除可能的空格和特殊字符，只保留数字
	phoneDigits := ""
	for _, char := range phoneStr {
		if char >= '0' && char <= '9' {
			phoneDigits += string(char)
		}
	}

	// 检查是否为11位数字且以1开头
	return len(phoneDigits) == 11 && strings.HasPrefix(phoneDigits, "1")
}

// primeUserSearchExecuteOther Prime CRM 用户解码手机号监控方法
// 对应 Python 版本的 prime_user_search_execute_other 函数
func (p *PrimeMonitor) primeUserSearchExecuteOther(startTime, endTime int64, threshold int, enableDedup bool) ([]map[string]interface{}, error) {
	// 详情模式：逐条日志生成文本
	type occurrence struct {
		GlobalUserID string
		CustomerID   string
		Realname     string
		Mobile       string
		IDCard       string
	}
	var occurrences []occurrence

	// 获取日志之前，先连上数据库，否则扫描半天日志，最后连不上，白获取了
	var dbc *config.DbConfig
	for _, db := range p.databases {
		if db.Database == "db_groupbuy" {
			dbc = &db
			break
		}
	}
	if dbc == nil {
		return nil, fmt.Errorf("db_groupbuy not found")
	}
	dbConfig := utils.SimpleConfig(dbc.Host, dbc.Username, dbc.Password, dbc.Database)
	sqlClient, err := utils.NewMySQLClient(dbConfig)
	if err != nil {
		return nil, err
	}
	defer sqlClient.Close()

	// 定义四类查询
	queries := []struct {
		name   string
		query  string
		handle func(reqStr, respStr string, now time.Time) (customerID string, values map[SensitiveField][]string)
	}{
		{ // 1) /api/v1/customer/decodeField
			name:  "customer.decodeField",
			query: `req.host:"prime-crm.soyoung.com" and req.path: "/api/v1/customer/decodeField"`,
			handle: func(reqStr, respStr string, now time.Time) (string, map[SensitiveField][]string) {
				// req.args.field in (mobile, card_number)
				field := gjson.Get(reqStr, "args.field").String()
				if field != "mobile" && field != "card_number" {
					return "", nil
				}
				// resp.body.responseData 有值
				decoded := gjson.Get(respStr, "body.responseData")
				if !decoded.Exists() || decoded.String() == "" {
					return "", nil
				}
				vals := map[SensitiveField][]string{}
				switch field {
				case "mobile":
					if isValidPhone(decoded.String()) {
						vals[SensitiveFieldMobile] = []string{decoded.String()}
					} else {
						// 非法手机号则忽略
						return "", nil
					}
				case "card_number":
					vals[SensitiveFieldCardNumber] = []string{decoded.String()}
				}
				// customerId（若有）
				cust := gjson.Get(reqStr, "args.customer_id").String()
				if cust == "" {
					cust = gjson.Get(reqStr, "body.customer_id").String()
				}
				return cust, vals
			},
		},
		{ // 2) /api/v1/template/decryptField
			name:  "template.decryptField",
			query: `req.host:"prime-crm.soyoung.com" and req.path : "/api/v1/template/decryptField"`,
			handle: func(reqStr, respStr string, now time.Time) (string, map[SensitiveField][]string) {
				// req.body.list[].key in (realname, mobile, card_number, address)
				validKeys := map[string]struct{}{"realname": {}, "mobile": {}, "card_number": {}, "address": {}}
				hasKey := false
				gjson.Get(reqStr, "body.list").ForEach(func(_, v gjson.Result) bool {
					key := v.Get("key").String()
					if _, ok := validKeys[key]; ok {
						hasKey = true
						return false
					}
					return true
				})
				if !hasKey {
					return "", nil
				}
				// resp.body.responseData 是 map 且有值
				respData := gjson.Get(respStr, "body.responseData")
				if !respData.Exists() || respData.Type != gjson.JSON {
					return "", nil
				}
				vals := map[SensitiveField][]string{}
				// 读取可能的字段：realname/mobile/id_card/address
				for _, k := range []string{"realname", "mobile", "id_card", "card_number", "address"} {
					if f, ok := normalizeField(k); ok {
						v := respData.Get(k)
						if v.Exists() && v.String() != "" {
							if f == SensitiveFieldMobile {
								if !isValidPhone(v.String()) {
									continue
								}
							}
							vals[f] = append(vals[f], v.String())
						}
					}
				}
				if len(vals) == 0 {
					return "", nil
				}
				cust := gjson.Get(reqStr, "args.customer_id").String()
				if cust == "" {
					cust = gjson.Get(reqStr, "body.customer_id").String()
				}
				return cust, vals
			},
		},
		{ // 3) /api/v1/template/batchDecryptField
			name:  "template.batchDecryptField",
			query: `req.host:"prime-crm.soyoung.com" and req.path : "/api/v1/template/batchDecryptField"`,
			handle: func(reqStr, respStr string, now time.Time) (string, map[SensitiveField][]string) {
				// req.args.id 有值
				if gjson.Get(reqStr, "args.id").String() == "" {
					return "", nil
				}
				// resp.body.responseData.list len()>0
				list := gjson.Get(respStr, "body.responseData.list")
				if !list.Exists() || list.IsArray() == false || len(list.Array()) == 0 {
					return "", nil
				}
				vals := map[SensitiveField][]string{}
				list.ForEach(func(_, item gjson.Result) bool {
					data := item.Get("data")
					if !data.Exists() {
						return true
					}
					for _, k := range []string{"realname", "mobile", "id_card", "card_number", "address"} {
						if f, ok := normalizeField(k); ok {
							v := data.Get(k)
							if v.Exists() && v.String() != "" {
								if f == SensitiveFieldMobile && !isValidPhone(v.String()) {
									continue
								}
								vals[f] = append(vals[f], v.String())
							}
						}
					}
					return true
				})
				if len(vals) == 0 {
					return "", nil
				}
				cust := gjson.Get(reqStr, "args.customer_id").String()
				if cust == "" {
					cust = gjson.Get(reqStr, "body.customer_id").String()
				}
				return cust, vals
			},
		},
		{ // 4) /api/v1/customer/getCustomerForEdit
			name:  "customer.getCustomerForEdit",
			query: `req.host:"prime-crm.soyoung.com" and req.path : "/api/v1/customer/getCustomerForEdit"`,
			handle: func(reqStr, respStr string, now time.Time) (string, map[SensitiveField][]string) {
				data := gjson.Get(respStr, "body.responseData")
				if !data.Exists() || data.Type != gjson.JSON {
					return "", nil
				}
				// 任一 encrypt_mobile、realname、card_number、address、contact_address 有值则记录
				keys := []string{"encrypt_mobile", "realname", "card_number", "address", "contact_address"}
				had := false
				vals := map[SensitiveField][]string{}
				for _, k := range keys {
					v := data.Get(k)
					if !v.Exists() || v.String() == "" {
						continue
					}
					f, ok := normalizeField(k)
					if !ok {
						continue
					}
					if f == SensitiveFieldMobile && !isValidPhone(v.String()) {
						// getCustomerForEdit 中 encrypt_mobile 字段多为明文手机号，仍进行校验
						continue
					}
					vals[f] = append(vals[f], v.String())
					had = true
				}
				if !had {
					return "", nil
				}
				cust := gjson.Get(reqStr, "args.customer_id").String()
				if cust == "" {
					cust = gjson.Get(reqStr, "body.customer_id").String()
				}
				return cust, vals
			},
		},
		{ // 5) /api/v1/trade/order/tenThousandDecodeMobile
			name:  "trade.order.tenThousandDecodeMobile",
			query: `req.host:"prime-crm.soyoung.com" and req.path: "/api/v1/trade/order/tenThousandDecodeMobile"`,
			handle: func(reqStr, respStr string, now time.Time) (string, map[SensitiveField][]string) {
				// resp.body.responseData 是手机号
				decoded := gjson.Get(respStr, "body.responseData")
				if !decoded.Exists() || decoded.String() == "" {
					return "", nil
				}
				// 验证手机号有效性
				if !isValidPhone(decoded.String()) {
					return "", nil
				}
				vals := map[SensitiveField][]string{
					SensitiveFieldMobile: {decoded.String()},
				}
				// customerId（若有）
				cust := gjson.Get(reqStr, "args.customer_id").String()
				if cust == "" {
					cust = gjson.Get(reqStr, "body.customer_id").String()
				}
				return cust, vals
			},
		},
		{ // 6) /api/v1/survey/doctorMasterSurveyAdminSee
			name:  "survey.doctorMasterSurveyAdminSee",
			query: `req.host:"prime-crm.soyoung.com" and req.path: "/api/v1/survey/doctorMasterSurveyAdminSee"`,
			handle: func(reqStr, respStr string, now time.Time) (string, map[SensitiveField][]string) {
				// resp.body.responseData.mobile 字段
				mobile := gjson.Get(respStr, "body.responseData.mobile")
				if !mobile.Exists() || mobile.String() == "" {
					return "", nil
				}
				// 验证手机号有效性
				if !isValidPhone(mobile.String()) {
					return "", nil
				}
				vals := map[SensitiveField][]string{
					SensitiveFieldMobile: {mobile.String()},
				}
				// customerId（若有）
				cust := gjson.Get(reqStr, "args.customer_id").String()
				if cust == "" {
					cust = gjson.Get(reqStr, "body.customer_id").String()
				}
				return cust, vals
			},
		},
	}

	// 逐个查询来源执行
	for _, q := range queries {
		for i := 0; i < 100; i++ {
			args := map[string]string{
				"from":   strconv.FormatInt(startTime, 10),
				"to":     strconv.FormatInt(endTime, 10),
				"query":  q.query,
				"type":   "log",
				"line":   "50",
				"offset": strconv.Itoa(i * 50),
			}
			getLogsReq := GetLogsRequest{
				Project:  "sy-security-log",
				Logstore: "event_log",
				Args:     args,
			}
			res, err := client.GetLogs(&getLogsReq)
			if err != nil {
				return nil, err
			}
			// 阿里返回为数组，无 Code 字段，错误在 Error 中
			if res.Error != "" {
				return nil, fmt.Errorf("getLogs error: %s", res.Error)
			}
			logs, ok := res.Data.([]interface{})
			if !ok || len(logs) == 0 {
				break
			}

			for _, item := range logs {
				logItem, ok := item.(map[string]interface{})
				if !ok {
					continue
				}
				reqRaw, ok := logItem["req"].(string)
				if !ok || reqRaw == "" {
					continue
				}
				respRaw, _ := logItem["resp"].(string)

				// 解析 JWT token 获取用户
				token := gjson.Get(reqRaw, "headers.cookie.prime_crm_token").String()
				if token == "" {
					continue
				}
				payload, err := decodeJWTPayload(token)
				if err != nil || payload == nil {
					continue
				}
				var globalUserId, name string
				if globalUserID, ok := payload["global_user_id"]; ok {
					globalUserId = strconv.FormatFloat(globalUserID.(float64), 'f', 0, 64)
				}
				if nameObj, ok := payload["name"]; ok {
					name = fmt.Sprintf("%v", nameObj)
				}
				if globalUserId == "" || name == "" {
					continue
				}

				// 端点特定解析
				now := time.Now()
				customerID, values := q.handle(reqRaw, respRaw, now)
				if values == nil || len(values) == 0 {
					continue
				}

				// 提取本条日志的客户信息（按条生成一条文本，不做聚合统计）
				var mobile, idCard, realname string
				if arr, ok := values[SensitiveFieldMobile]; ok && len(arr) > 0 {
					// 仅保留合法手机号
					if isValidPhone(arr[0]) {
						mobile = arr[0]
					}
				}
				if arr, ok := values[SensitiveFieldCardNumber]; ok && len(arr) > 0 {
					idCard = arr[0]
				}
				if arr, ok := values[SensitiveFieldRealname]; ok && len(arr) > 0 {
					realname = arr[0]
				}

				// address 暂不处理

				// 身份证号有效性：长度需 > 5，否则视为无效
				if idCard != "" && len([]rune(idCard)) <= 5 {
					idCard = ""
				}

				// 若手机号与身份证号均为空，则跳过（避免仅输出门店）
				if mobile == "" && idCard == "" {
					continue
				}

				// 记录一次命中
				occurrences = append(occurrences, occurrence{
					GlobalUserID: globalUserId,
					CustomerID:   customerID,
					Realname:     realname,
					Mobile:       mobile,
					IDCard:       idCard,
				})
			}
		}
	}

	// 工具: 安全字符串转换
	getString := func(val interface{}) string {
		if val == nil {
			return ""
		}
		switch v := val.(type) {
		case string:
			return v
		case []byte:
			return string(v)
		default:
			return fmt.Sprintf("%v", v)
		}
	}
	// 若没有命中，直接返回空结果
	if len(occurrences) == 0 {
		return []map[string]interface{}{}, nil
	}

	// 分批查询全量 occurrence 中的 customerId 对应租户: tb_crm_customer (customer_id -> tenant_id)
	const batchSize = 100
	allCustomerIDSet := make(map[string]struct{})
	for _, oc := range occurrences {
		if oc.CustomerID != "" {
			allCustomerIDSet[oc.CustomerID] = struct{}{}
		}
	}
	var allCustomerIDs []string
	for cid := range allCustomerIDSet {
		allCustomerIDs = append(allCustomerIDs, cid)
	}
	customerTenantMap := make(map[string]string) // customer_id -> tenant_id
	for i := 0; i < len(allCustomerIDs); i += batchSize {
		end := i + batchSize
		if end > len(allCustomerIDs) {
			end = len(allCustomerIDs)
		}
		batch := allCustomerIDs[i:end]
		if len(batch) == 0 {
			continue
		}
		placeholders := strings.Repeat("?,", len(batch))
		placeholders = strings.TrimRight(placeholders, ",")
		var argsAny []interface{}
		for _, id := range batch {
			argsAny = append(argsAny, id)
		}
		q := fmt.Sprintf("select customer_id, tenant_id from tb_crm_customer where customer_id in (%s);", placeholders)
		rows, err := sqlClient.Query(q, argsAny...)
		if err != nil {
			fmt.Printf("批量查询客户租户失败: %v\n", err)
		} else {
			for _, row := range rows {
				cid := getString(row["customer_id"])
				tid := getString(row["tenant_id"])
				if cid == "" {
					continue
				}
				customerTenantMap[cid] = tid
			}
		}
	}

	// 分批查询全量 occurrence 中的用户基础信息: tb_crm_admin_user (id -> sy_sso_username, sy_feishu_user_id)
	allUserIDSet := make(map[string]struct{})
	for _, oc := range occurrences {
		if oc.GlobalUserID != "" {
			allUserIDSet[oc.GlobalUserID] = struct{}{}
		}
	}
	var allUserIDs []string
	for uid := range allUserIDSet {
		allUserIDs = append(allUserIDs, uid)
	}
	userInfoMap := make(map[string]map[string]string) // uid -> {ssoUsername,userId}
	for i := 0; i < len(allUserIDs); i += batchSize {
		end := i + batchSize
		if end > len(allUserIDs) {
			end = len(allUserIDs)
		}
		batch := allUserIDs[i:end]
		if len(batch) == 0 {
			continue
		}
		placeholders := strings.Repeat("?,", len(batch))
		placeholders = strings.TrimRight(placeholders, ",")
		var argsAny []interface{}
		for _, id := range batch {
			argsAny = append(argsAny, id)
		}
		q := fmt.Sprintf("select id, sy_sso_username, sy_feishu_user_id from tb_crm_admin_user where id in (%s);", placeholders)
		rows, err := sqlClient.Query(q, argsAny...)
		if err != nil {
			fmt.Printf("批量查询用户基础信息失败: %v\n", err)
		} else {
			for _, row := range rows {
				uid := getString(row["id"])
				if uid == "" {
					continue
				}
				userInfoMap[uid] = map[string]string{
					"ssoUsername": getString(row["sy_sso_username"]),
					"userId":      getString(row["sy_feishu_user_id"]),
				}
			}
		}
	}

	// 分批查询租户名称: tb_crm_admin_tenant (id -> name)，并做本地缓存
	tenantNameCache := make(map[string]string)
	tenantIDSet := make(map[string]struct{})
	for _, tid := range customerTenantMap {
		if tid != "" {
			tenantIDSet[tid] = struct{}{}
		}
	}
	var tenantIDs []string
	for tid := range tenantIDSet {
		tenantIDs = append(tenantIDs, tid)
	}
	for i := 0; i < len(tenantIDs); i += batchSize {
		end := i + batchSize
		if end > len(tenantIDs) {
			end = len(tenantIDs)
		}
		batch := tenantIDs[i:end]
		if len(batch) == 0 {
			continue
		}
		placeholders := strings.Repeat("?,", len(batch))
		placeholders = strings.TrimRight(placeholders, ",")
		var argsAny []interface{}
		for _, id := range batch {
			argsAny = append(argsAny, id)
		}
		q := fmt.Sprintf("select id, name from tb_crm_admin_tenant where id in (%s);", placeholders)
		rows, err := sqlClient.Query(q, argsAny...)
		if err != nil {
			fmt.Printf("批量查询租户名称失败: %v\n", err)
		} else {
			for _, row := range rows {
				tid := getString(row["id"])
				tname := getString(row["name"])
				if tid == "" {
					continue
				}
				tenantNameCache[tid] = tname
			}
		}
	}

	// 生成文本记录（仅有值时输出对应行），分组到 global_user_id，
	// 并在每个用户内进行去重（四个原始值完全一致则去重）
	groupedRecords := make(map[string][]string)
	seenPerUser := make(map[string]map[string]struct{})
	// 额外：为每个 occurrence 计算加密/哈希
	aesKey := ""
	if cfg := config.Get(); cfg != nil {
		aesKey = cfg.Other.AesKey
	}
	// 缓存：用户的加密/哈希明细（与 records 同步长度）
	type encInfo struct{ MobileAES, IDCardAES, MobileMD5 string }
	perUserEnc := make(map[string][]encInfo)
	for _, oc := range occurrences {
		tenantName := ""
		if oc.CustomerID != "" {
			if tid := customerTenantMap[oc.CustomerID]; tid != "" {
				tenantName = tenantNameCache[tid]
			}
		}

		if oc.GlobalUserID == "" {
			// 没有用户标识则跳过，原则上不会发生
			continue
		}

		if _, ok := seenPerUser[oc.GlobalUserID]; !ok {
			seenPerUser[oc.GlobalUserID] = make(map[string]struct{})
		}
		// 去重键（脱敏前原始值，包含 realname 以便同名同客去重更精确；不影响“无姓名但手机号/证件号一致”的归并）
		dedupKey := oc.Realname + "\x1e" + oc.Mobile + "\x1e" + oc.IDCard + "\x1e" + tenantName
		if _, ok := seenPerUser[oc.GlobalUserID][dedupKey]; ok {
			continue
		}
		seenPerUser[oc.GlobalUserID][dedupKey] = struct{}{}

		maskedPhone := utils.HidePhone(oc.Mobile)
		maskedID := utils.HideIdentity(oc.IDCard)
		maskedName := utils.HideName(oc.Realname)

		// 计算加密/哈希（失败则对应字段为空字符串）
		var mobileAES, idCardAES, mobileMD5 string
		if aesKey != "" {
			if oc.Mobile != "" {
				if v, err := utils.AESEncryptBase64(oc.Mobile, aesKey); err == nil {
					mobileAES = v
				}
			}
			if oc.IDCard != "" {
				if v, err := utils.AESEncryptBase64(oc.IDCard, aesKey); err == nil {
					idCardAES = v
				}
			}
		}
		if oc.Mobile != "" {
			mobileMD5 = utils.MD5HexLower(oc.Mobile)
		}

		var lines []string
		if maskedPhone != "" {
			lines = append(lines, fmt.Sprintf("手机号：%s", maskedPhone))
		}
		if maskedID != "" {
			lines = append(lines, fmt.Sprintf("身份证号：%s", maskedID))
		}
		if maskedName != "" {
			lines = append(lines, fmt.Sprintf("客户姓名：%s", maskedName))
		}
		if tenantName != "" {
			lines = append(lines, fmt.Sprintf("客户归属门店：%s。", tenantName))
		}

		if len(lines) == 0 {
			continue
		}
		groupedRecords[oc.GlobalUserID] = append(groupedRecords[oc.GlobalUserID], strings.Join(lines, "\n"))
		perUserEnc[oc.GlobalUserID] = append(perUserEnc[oc.GlobalUserID], encInfo{MobileAES: mobileAES, IDCardAES: idCardAES, MobileMD5: mobileMD5})
	}

	var resultList []map[string]interface{}
	for uid, recs := range groupedRecords {
		if len(recs) == 0 {
			continue
		}
		item := map[string]interface{}{
			"globalUserId":  uid,
			"customerCount": len(recs),
			"records":       recs,
			"content":       strings.Join(recs, "\n\n"),
		}
		// 同步输出加密与哈希数组，与 records 对齐
		if infos, ok := perUserEnc[uid]; ok && len(infos) == len(recs) {
			var mAES, iAES, mMD5 []string
			for _, it := range infos {
				mAES = append(mAES, it.MobileAES)
				iAES = append(iAES, it.IDCardAES)
				mMD5 = append(mMD5, it.MobileMD5)
			}
			item["mobileAESList"] = mAES
			item["idCardAESList"] = iAES
			item["mobileMD5List"] = mMD5
		}
		if info, ok := userInfoMap[uid]; ok {
			if v := info["ssoUsername"]; v != "" {
				item["ssoUsername"] = v
			}
			if v := info["userId"]; v != "" {
				item["userId"] = v
			}
		}
		resultList = append(resultList, item)
	}
	return resultList, nil

}

func PrimeUserDecodeScan(startTime, endTime time.Time) (ScanResult, error) {
	// 获取 Unix 时间戳（这些时间戳现在基于正确的时区）
	start := startTime.Unix()
	end := endTime.Unix()

	// 10分钟扫描：不启用防重复告警
	content, err := monitor.primeUserSearchExecuteOther(start, end, 10, false)
	if err != nil {
		return ScanResult{}, err
	}

	return ScanResult{
		StartTime: startTime.Format("2006-01-02 15:04:05"),
		EndTime:   endTime.Format("2006-01-02 15:04:05"),
		Datas:     content,
	}, nil
}
