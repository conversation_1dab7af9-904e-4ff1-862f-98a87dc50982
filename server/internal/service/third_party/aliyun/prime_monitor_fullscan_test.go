package aliyun

import (
	"encoding/base64"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"sec-flow-server/internal/service/third_party/lark"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/tidwall/gjson"
	"sec-flow-server/internal/config"
	"sec-flow-server/internal/utils"
)

// -------- helpers duplicated for independence --------

type ScanSensitive<PERSON>ield string

const (
	ScanSensitiveFieldMobile     ScanSensitiveField = "mobile"
	ScanSensitiveFieldCardNumber ScanSensitiveField = "card_number"
	ScanSensitiveFieldRealname   ScanSensitiveField = "realname"
	ScanSensitiveFieldAddress    ScanSensitiveField = "address"
)

type ScanUserData struct {
	GlobalUserID string
	Name         string
	CustomerIDs  map[string]struct{}
	Values       map[ScanSensitiveField]map[string]struct{}
	PathCounts   map[string]int
}

func newScanUserData(uid, name string) *ScanUserData {
	return &ScanUserData{
		GlobalUserID: uid,
		Name:         name,
		CustomerIDs:  make(map[string]struct{}),
		Values: map[ScanSensitiveField]map[string]struct{}{
			ScanSensitiveFieldMobile:     {},
			ScanSensitiveFieldCardNumber: {},
			ScanSensitiveFieldRealname:   {},
			ScanSensitiveFieldAddress:    {},
		},
		PathCounts: make(map[string]int),
	}
}

func (u *ScanUserData) addCustomerID(customerID string) {
	if customerID == "" {
		return
	}
	u.CustomerIDs[customerID] = struct{}{}
}

func (u *ScanUserData) addValue(field ScanSensitiveField, value string) {
	if value == "" {
		return
	}
	if _, ok := u.Values[field]; !ok {
		u.Values[field] = make(map[string]struct{})
	}
	u.Values[field][value] = struct{}{}
}

func scanNormalizeField(key string) (ScanSensitiveField, bool) {
	switch key {
	case "mobile", "encrypt_mobile":
		return ScanSensitiveFieldMobile, true
	case "card_number", "id_card":
		return ScanSensitiveFieldCardNumber, true
	case "realname":
		return ScanSensitiveFieldRealname, true
	case "address", "contact_address":
		return ScanSensitiveFieldAddress, true
	default:
		return "", false
	}
}

func scanDecodeJWTPayload(token string) (map[string]interface{}, error) {
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("invalid JWT token format")
	}
	payload := parts[1]
	if pad := len(payload) % 4; pad > 0 {
		payload += strings.Repeat("=", 4-pad)
	}
	decodedBytes, err := base64.URLEncoding.DecodeString(payload)
	if err != nil {
		return nil, fmt.Errorf("base64 decode error: %v", err)
	}
	var payloadData map[string]interface{}
	if err := json.Unmarshal(decodedBytes, &payloadData); err != nil {
		return nil, fmt.Errorf("JSON unmarshal error: %v", err)
	}
	return payloadData, nil
}

func scanIsValidPhone(phoneStr string) bool {
	if phoneStr == "" {
		return false
	}
	digits := make([]byte, 0, len(phoneStr))
	for i := 0; i < len(phoneStr); i++ {
		if phoneStr[i] >= '0' && phoneStr[i] <= '9' {
			digits = append(digits, phoneStr[i])
		}
	}
	s := string(digits)
	return len(s) == 11 && strings.HasPrefix(s, "1")
}

// findServerRoot resolves the server module root so config files are found
func findServerRoot() (string, error) {
	_, file, _, ok := runtime.Caller(0)
	if !ok {
		return "", fmt.Errorf("unable to resolve caller path")
	}
	dir := filepath.Dir(file)
	for i := 0; i < 8; i++ {
		if filepath.Base(dir) == "server" {
			return dir, nil
		}
		parent := filepath.Dir(dir)
		if parent == dir {
			break
		}
		dir = parent
	}
	return "", fmt.Errorf("could not locate server root from %s", file)
}

type userInfo struct {
	Email       string
	SsoUsername string
	UserId      string
	Name        string
	Dept        string
}

// -------- main one-off scanner test --------

func TestPrimeFullScan30d(t *testing.T) {
	// Editable parameters
	outputCSVPath := "/Users/<USER>/Downloads/prime_cddyc_logs721.csv"
	summaryCSVPath := "/Users/<USER>/Downloads/prime_cddyc_statistic721.csv"
	//maxRowsThreshold := 1000 // 超过则提前停止
	// time windows: last 30 days, hourly steps, descending
	end, _ := time.Parse("2006-01-02 15:04:05", "2025-08-01 00:00:00")
	start, _ := time.Parse("2006-01-02 15:04:05", "2025-07-21 00:00:00")
	//start := end.Add(-10 * 24 * time.Hour)

	// Prepare environment & config
	root, err := findServerRoot()
	if err != nil {
		t.Fatalf("findServerRoot error: %v", err)
	}
	if err := os.Chdir(root); err != nil {
		t.Fatalf("chdir error: %v", err)
	}
	if os.Getenv("APP_ENV") == "" {
		_ = os.Setenv("APP_ENV", "local")
	}
	cfg, err := config.Load()
	if err != nil {
		t.Fatalf("load config error: %v", err)
	}
	InitAliyunSlsClient(cfg.Third.Aliyun, cfg.Databases)
	lark.InitLarkClient(cfg.Third.Lark)
	// DB client for user info and business queries (db_groupbuy)
	var dbc *config.DbConfig
	for i := range cfg.Databases {
		if cfg.Databases[i].Database == "db_groupbuy" {
			dbc = &cfg.Databases[i]
			break
		}
	}
	if dbc == nil {
		t.Fatalf("db_groupbuy not found in config.Databases")
	}
	sqlCfg := utils.SimpleConfig(dbc.Host, dbc.Username, dbc.Password, dbc.Database)
	sqlClient, err := utils.NewMySQLClient(sqlCfg)
	if err != nil {
		t.Fatalf("sql client init error: %v", err)
	}
	defer sqlClient.Close()

	// CSV writer
	f, err := os.Create(outputCSVPath)
	if err != nil {
		t.Fatalf("create csv error: %v", err)
	}
	defer f.Close()
	w := csv.NewWriter(f)
	// header
	_ = w.Write([]string{"time", "email", "ssoUsername", "userId", "name", "dept", "customerId", "tenantName", "mobile", "card_number", "realname", "address", "path"})
	w.Flush()

	noHide := func(phone string) string {
		return phone
	}

	userDataMap := make(map[string]*ScanUserData)
	userInfoCache := make(map[string]userInfo)
	customerTenantCache := make(map[string]string) // customer_id -> tenant_id
	tenantNameCache := make(map[string]string)     // tenant_id -> tenant_name
	getUserInfo := func(uid string) userInfo {
		if info, ok := userInfoCache[uid]; ok {
			return info
		}
		rows, err := sqlClient.Query("select email, sy_sso_username, sy_feishu_user_id from tb_crm_admin_user where id = ?;", uid)
		if err != nil || len(rows) == 0 {
			// cache empty to avoid repeated queries
			userInfoCache[uid] = userInfo{}
			return userInfo{}
		}
		r := rows[0]
		toStr := func(v interface{}) string {
			if v == nil {
				return ""
			}
			switch vv := v.(type) {
			case string:
				return vv
			case []byte:
				return string(vv)
			default:
				return fmt.Sprintf("%v", vv)
			}
		}
		info := userInfo{
			Email:       toStr(r["email"]),
			SsoUsername: toStr(r["sy_sso_username"]),
			UserId:      toStr(r["sy_feishu_user_id"]),
		}
		if info.UserId != "" {

			user, err := lark.GetLarkUser(info.UserId)
			if err == nil && user != nil {
				info.Name = user.Name
				dept, err := lark.GetDeptByLarkUser(user)
				if err == nil {
					info.Dept = dept
				}
			}
		}
		// optional: enrich Name/Dept via lark if needed (disabled in one-off test to avoid network errors)
		userInfoCache[uid] = info
		return info
	}
	// helpers to resolve tenant name by customer id with caching
	resolveTenantName := func(customerID string) (string, string) {
		if customerID == "" {
			return "", ""
		}
		if tid, ok := customerTenantCache[customerID]; ok {
			if name, ok2 := tenantNameCache[tid]; ok2 {
				return tid, name
			}
			// fetch name for tid
			rows, err := sqlClient.Query("select name from tb_crm_admin_tenant where id = ? limit 1;", tid)
			if err == nil && len(rows) > 0 {
				toStr := func(v interface{}) string {
					if v == nil {
						return ""
					}
					switch vv := v.(type) {
					case string:
						return vv
					case []byte:
						return string(vv)
					default:
						return fmt.Sprintf("%v", vv)
					}
				}
				name := toStr(rows[0]["name"])
				tenantNameCache[tid] = name
				return tid, name
			}
			return tid, ""
		}
		// fetch tenant id for customer
		rows, err := sqlClient.Query("select tenant_id from tb_crm_customer where customer_id = ? limit 1;", customerID)
		if err != nil || len(rows) == 0 {
			return "", ""
		}
		toStr := func(v interface{}) string {
			if v == nil {
				return ""
			}
			switch vv := v.(type) {
			case string:
				return vv
			case []byte:
				return string(vv)
			default:
				return fmt.Sprintf("%v", vv)
			}
		}
		tid := toStr(rows[0]["tenant_id"])
		customerTenantCache[customerID] = tid
		if tid == "" {
			return "", ""
		}
		if name, ok := tenantNameCache[tid]; ok {
			return tid, name
		}
		rows2, err2 := sqlClient.Query("select name from tb_crm_admin_tenant where id = ? limit 1;", tid)
		if err2 == nil && len(rows2) > 0 {
			name := toStr(rows2[0]["name"])
			tenantNameCache[tid] = name
			return tid, name
		}
		return tid, ""
	}

	// queries copied with handle
	type handleFn func(reqStr, respStr string) (customerID string, values map[ScanSensitiveField][]string)
	type querySpec struct {
		name, query string
		handle      handleFn
	}
	queries := []querySpec{
		{
			name:  "customer.decodeField",
			query: `req.host:"prime-crm.soyoung.com" and req.path: "/api/v1/customer/decodeField"`,
			handle: func(reqStr, respStr string) (string, map[ScanSensitiveField][]string) {
				field := gjson.Get(reqStr, "args.field").String()
				if field != "mobile" && field != "card_number" {
					return "", nil
				}
				decoded := gjson.Get(respStr, "body.responseData")
				if !decoded.Exists() || decoded.String() == "" {
					return "", nil
				}
				vals := map[ScanSensitiveField][]string{}
				switch field {
				case "mobile":
					if scanIsValidPhone(decoded.String()) {
						vals[ScanSensitiveFieldMobile] = []string{decoded.String()}
					} else {
						return "", nil
					}
				case "card_number":
					vals[ScanSensitiveFieldCardNumber] = []string{decoded.String()}
				}
				cust := gjson.Get(reqStr, "args.customer_id").String()
				if cust == "" {
					cust = gjson.Get(reqStr, "body.customer_id").String()
				}
				return cust, vals
			},
		},
		{
			name:  "template.decryptField",
			query: `req.host:"prime-crm.soyoung.com" and req.path : "/api/v1/template/decryptField"`,
			handle: func(reqStr, respStr string) (string, map[ScanSensitiveField][]string) {
				validKeys := map[string]struct{}{"realname": {}, "mobile": {}, "card_number": {}, "address": {}}
				hasKey := false
				gjson.Get(reqStr, "body.list").ForEach(func(_, v gjson.Result) bool {
					key := v.Get("key").String()
					if _, ok := validKeys[key]; ok {
						hasKey = true
						return false
					}
					return true
				})
				if !hasKey {
					return "", nil
				}
				respData := gjson.Get(respStr, "body.responseData")
				if !respData.Exists() || respData.Type != gjson.JSON {
					return "", nil
				}
				vals := map[ScanSensitiveField][]string{}
				for _, k := range []string{"realname", "mobile", "id_card", "card_number", "address"} {
					if f, ok := scanNormalizeField(k); ok {
						v := respData.Get(k)
						if v.Exists() && v.String() != "" {
							if f == ScanSensitiveFieldMobile && !scanIsValidPhone(v.String()) {
								continue
							}
							vals[f] = append(vals[f], v.String())
						}
					}
				}
				if len(vals) == 0 {
					return "", nil
				}
				cust := gjson.Get(reqStr, "args.customer_id").String()
				if cust == "" {
					cust = gjson.Get(reqStr, "body.customer_id").String()
				}
				return cust, vals
			},
		},
		{
			name:  "template.batchDecryptField",
			query: `req.host:"prime-crm.soyoung.com" and req.path : "/api/v1/template/batchDecryptField"`,
			handle: func(reqStr, respStr string) (string, map[ScanSensitiveField][]string) {
				if gjson.Get(reqStr, "args.id").String() == "" {
					return "", nil
				}
				list := gjson.Get(respStr, "body.responseData.list")
				if !list.Exists() || !list.IsArray() || len(list.Array()) == 0 {
					return "", nil
				}
				vals := map[ScanSensitiveField][]string{}
				list.ForEach(func(_, item gjson.Result) bool {
					data := item.Get("data")
					if !data.Exists() {
						return true
					}
					for _, k := range []string{"realname", "mobile", "id_card", "card_number", "address"} {
						if f, ok := scanNormalizeField(k); ok {
							v := data.Get(k)
							if v.Exists() && v.String() != "" {
								if f == ScanSensitiveFieldMobile && !scanIsValidPhone(v.String()) {
									continue
								}
								vals[f] = append(vals[f], v.String())
							}
						}
					}
					return true
				})
				if len(vals) == 0 {
					return "", nil
				}
				cust := gjson.Get(reqStr, "args.customer_id").String()
				if cust == "" {
					cust = gjson.Get(reqStr, "body.customer_id").String()
				}
				return cust, vals
			},
		},
		{
			name:  "customer.getCustomerForEdit",
			query: `req.host:"prime-crm.soyoung.com" and req.path : "/api/v1/customer/getCustomerForEdit"`,
			handle: func(reqStr, respStr string) (string, map[ScanSensitiveField][]string) {
				data := gjson.Get(respStr, "body.responseData")
				if !data.Exists() || data.Type != gjson.JSON {
					return "", nil
				}
				keys := []string{"encrypt_mobile", "realname", "card_number", "address", "contact_address"}
				vals := map[ScanSensitiveField][]string{}
				had := false
				for _, k := range keys {
					v := data.Get(k)
					if !v.Exists() || v.String() == "" {
						continue
					}
					if f, ok := scanNormalizeField(k); ok {
						if f == ScanSensitiveFieldMobile && !scanIsValidPhone(v.String()) {
							continue
						}
						vals[f] = append(vals[f], v.String())
						had = true
					}
				}
				if !had {
					return "", nil
				}
				cust := gjson.Get(reqStr, "args.customer_id").String()
				if cust == "" {
					cust = gjson.Get(reqStr, "body.customer_id").String()
				}
				return cust, vals
			},
		},
	}

	totalRows := 0

	for windowEnd := end.Truncate(time.Hour); windowEnd.After(start); windowEnd = windowEnd.Add(-1 * time.Hour) {
		windowStart := windowEnd.Add(-1 * time.Hour)
		t.Logf("scan window: %s ~ %s", windowStart.Format("2006-01-02 15:04:05"), windowEnd.Format("2006-01-02 15:04:05"))

		for _, q := range queries {
			// pagination
			for page := 0; page < 200; page++ { // up to 10k rows per window/query
				args := map[string]string{
					"from":   strconv.FormatInt(windowStart.Unix(), 10),
					"to":     strconv.FormatInt(windowEnd.Unix(), 10),
					"query":  q.query,
					"type":   "log",
					"line":   "50",
					"offset": strconv.Itoa(page * 50),
				}
				req := GetLogsRequest{Project: "sy-security-log", Logstore: "event_log", Args: args}
				res, err := client.GetLogs(&req)
				if err != nil {
					t.Fatalf("getLogs error: %v", err)
				}
				if res.Error != "" {
					t.Fatalf("getLogs server error: %s", res.Error)
				}
				logs, ok := res.Data.([]interface{})
				if !ok || len(logs) == 0 {
					break
				}

				// process page
				for _, item := range logs {
					m, ok := item.(map[string]interface{})
					if !ok {
						continue
					}
					reqRaw, _ := m["req"].(string)
					respRaw, _ := m["resp"].(string)
					if reqRaw == "" {
						continue
					}

					// user from token
					token := gjson.Get(reqRaw, "headers.cookie.prime_crm_token").String()
					if token == "" {
						continue
					}
					payload, err := scanDecodeJWTPayload(token)
					if err != nil || payload == nil {
						continue
					}
					var uid, name string
					if v, ok := payload["global_user_id"]; ok {
						uid = strconv.FormatFloat(v.(float64), 'f', 0, 64)
					}
					if v, ok := payload["name"]; ok {
						name = fmt.Sprintf("%v", v)
					}
					if uid == "" {
						continue
					}

					// handle
					customerID, values := q.handle(reqRaw, respRaw)
					if len(values) == 0 {
						continue
					}

					// enrich user info
					info := getUserInfo(uid)

					// csv row data
					tsSec := gjson.Get(reqRaw, "ts").Int()
					var tsStr string
					if tsSec > 0 {
						tsStr = time.Unix(tsSec, 0).Format("2006-01-02 15:04:05")
					} else {
						tsStr = windowStart.Format("2006-01-02 15:04:05")
					}
					path := gjson.Get(reqRaw, "path").String()

					join := func(arr []string) string {
						if len(arr) == 0 {
							return ""
						}
						return strings.Join(arr, "|")
					}
					maskJoin := func(arr []string, mask func(string) string) string {
						if len(arr) == 0 {
							return ""
						}
						masked := make([]string, 0, len(arr))
						for _, v := range arr {
							masked = append(masked, mask(v))
						}
						return strings.Join(masked, "|")
					}
					mobileVals := maskJoin(values[ScanSensitiveFieldMobile], noHide)
					cardVals := maskJoin(values[ScanSensitiveFieldCardNumber], utils.HideIdentity)
					realVals := maskJoin(values[ScanSensitiveFieldRealname], utils.HideName)
					addrVals := join(values[ScanSensitiveFieldAddress])
					tid, tenantName := resolveTenantName(customerID)
					if tid != "10044" {
						continue
					}

					_ = w.Write([]string{
						tsStr,
						info.Email,
						info.SsoUsername,
						info.UserId,
						info.Name,
						info.Dept,
						customerID,
						tenantName,
						mobileVals,
						cardVals,
						realVals,
						addrVals,
						path,
					})

					totalRows++

					// aggregate
					usd, exists := userDataMap[uid]
					if !exists {
						usd = newScanUserData(uid, name)
						userDataMap[uid] = usd
					}
					if customerID != "" {
						usd.addCustomerID(customerID)
					}
					for f, arr := range values {
						for _, v := range arr {
							if f == ScanSensitiveFieldMobile && !scanIsValidPhone(v) {
								continue
							}
							usd.addValue(f, v)
						}
					}
					if path != "" {
						usd.PathCounts[path] = usd.PathCounts[path] + 1
					}
				}

				// flush per page
				w.Flush()
				if err := w.Error(); err != nil {
					t.Fatalf("csv flush error: %v", err)
				}
			}
		}

		// threshold check per window
		//if totalRows >= maxRowsThreshold {
		//	t.Logf("threshold reached: %d rows, stop further scanning", totalRows)
		//	break
		//}
	}

	// build summary with risk customers
	// collect all user ids and all customer ids
	var allUserIDs []string
	allCustomerSet := make(map[string]struct{})
	for uid, usd := range userDataMap {
		allUserIDs = append(allUserIDs, uid)
		for cid := range usd.CustomerIDs {
			allCustomerSet[cid] = struct{}{}
		}
	}

	// user tenants
	userTenantMap := make(map[string]map[string]struct{})
	const batchSize = 100
	for i := 0; i < len(allUserIDs); i += batchSize {
		end := i + batchSize
		if end > len(allUserIDs) {
			end = len(allUserIDs)
		}
		batch := allUserIDs[i:end]
		if len(batch) == 0 {
			continue
		}
		placeholders := strings.TrimRight(strings.Repeat("?,", len(batch)), ",")
		var argsAny []interface{}
		for _, id := range batch {
			argsAny = append(argsAny, id)
		}
		q := fmt.Sprintf("select user_id, tenant_id from tb_crm_admin_tenant_user where status=1 and user_id in (%s);", placeholders)
		rows, err := sqlClient.Query(q, argsAny...)
		if err == nil {
			toStr := func(v interface{}) string {
				if v == nil {
					return ""
				}
				switch vv := v.(type) {
				case string:
					return vv
				case []byte:
					return string(vv)
				default:
					return fmt.Sprintf("%v", vv)
				}
			}
			for _, r := range rows {
				uid := toStr(r["user_id"])
				tid := toStr(r["tenant_id"])
				if uid == "" || tid == "" {
					continue
				}
				if _, ok := userTenantMap[uid]; !ok {
					userTenantMap[uid] = make(map[string]struct{})
				}
				userTenantMap[uid][tid] = struct{}{}
			}
		}
	}

	// customer tenants
	var allCustomerIDs []string
	for cid := range allCustomerSet {
		allCustomerIDs = append(allCustomerIDs, cid)
	}
	customerTenantMap := make(map[string]string)
	for i := 0; i < len(allCustomerIDs); i += batchSize {
		end := i + batchSize
		if end > len(allCustomerIDs) {
			end = len(allCustomerIDs)
		}
		batch := allCustomerIDs[i:end]
		if len(batch) == 0 {
			continue
		}
		placeholders := strings.TrimRight(strings.Repeat("?,", len(batch)), ",")
		var argsAny []interface{}
		for _, id := range batch {
			argsAny = append(argsAny, id)
		}
		q := fmt.Sprintf("select customer_id, tenant_id from tb_crm_customer where customer_id in (%s);", placeholders)
		rows, err := sqlClient.Query(q, argsAny...)
		if err == nil {
			toStr := func(v interface{}) string {
				if v == nil {
					return ""
				}
				switch vv := v.(type) {
				case string:
					return vv
				case []byte:
					return string(vv)
				default:
					return fmt.Sprintf("%v", vv)
				}
			}
			for _, r := range rows {
				cid := toStr(r["customer_id"])
				tid := toStr(r["tenant_id"])
				if cid == "" {
					continue
				}
				customerTenantMap[cid] = tid
			}
		}
	}

	// write summary CSV
	sf, err := os.Create(summaryCSVPath)
	if err != nil {
		t.Fatalf("create summary csv error: %v", err)
	}
	defer sf.Close()
	sw := csv.NewWriter(sf)
	_ = sw.Write([]string{"globalUserId", "name", "customerCount", "mobileCount", "cardNumberCount", "realnameCount", "addressCount", "email", "ssoUsername", "userId", "riskCustomerIds"})

	for uid, usd := range userDataMap {
		tenants := userTenantMap[uid]
		var risk []string
		for cid := range usd.CustomerIDs {
			tid := customerTenantMap[cid]
			if tid == "" {
				risk = append(risk, cid)
				continue
			}
			if len(tenants) == 0 {
				risk = append(risk, cid)
				continue
			}
			if _, ok := tenants[tid]; !ok {
				risk = append(risk, cid)
			}
		}
		info := getUserInfo(uid)
		_ = sw.Write([]string{
			uid,
			usd.Name,
			strconv.Itoa(len(usd.CustomerIDs)),
			strconv.Itoa(len(usd.Values[ScanSensitiveFieldMobile])),
			strconv.Itoa(len(usd.Values[ScanSensitiveFieldCardNumber])),
			strconv.Itoa(len(usd.Values[ScanSensitiveFieldRealname])),
			strconv.Itoa(len(usd.Values[ScanSensitiveFieldAddress])),
			info.Email,
			info.SsoUsername,
			info.UserId,
			strings.Join(risk, "|"),
		})
	}
	sw.Flush()
	if err := sw.Error(); err != nil {
		t.Fatalf("summary csv flush error: %v", err)
	}

	t.Logf("done. rows=%d, out=%s, summary=%s", totalRows, outputCSVPath, summaryCSVPath)
}
