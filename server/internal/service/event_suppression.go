package service

import (
	"fmt"
	"log"
	"sec-flow-server/internal/database"
	"sec-flow-server/internal/model"
	"sec-flow-server/internal/utils"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// EventSuppressionService 事件去重服务
type EventSuppressionService struct {
	db *gorm.DB
}

// NewEventSuppressionService 创建事件去重服务
func NewEventSuppressionService() *EventSuppressionService {
	return &EventSuppressionService{
		db: database.GetDB(),
	}
}

// CheckAndProcessSuppression 检查并处理事件去重
func (s *EventSuppressionService) CheckAndProcessSuppression(
	config *model.SuppressionConfig,
	eventData map[string]interface{},
	flowData map[string]interface{}) (*SuppressionResult, error) {

	// 1. 生成去重键
	dedupKey, err := s.generateDedupKey(config.DedupKeyTemplate, eventData, flowData)
	if err != nil {
		return nil, fmt.Errorf("failed to generate dedup key: %v", err)
	}

	// 2. 解析时间窗口
	windowDuration, err := s.parseTimeWindow(config.TimeWindow, config.CustomTimeWindow)
	if err != nil {
		return nil, fmt.Errorf("failed to parse time window: %v", err)
	}

	// 3. 查询现有记录
	now := time.Now()
	windowEndTime := now.Add(windowDuration)

	var existingRecord model.EventSuppressionRecord
	err = s.db.Where("dedup_key = ? AND window_end_time > ?", dedupKey, now).
		First(&existingRecord).Error

	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to query existing record: %v", err)
	}

	// 4. 如果没有现有记录，创建新记录
	if err == gorm.ErrRecordNotFound {
		return s.createNewRecord(dedupKey, windowEndTime, eventData, config)
	}

	// 5. 处理现有记录
	return s.processExistingRecord(&existingRecord, eventData, flowData, config)
}

// generateDedupKey 生成去重键
func (s *EventSuppressionService) generateDedupKey(template string, eventData, flowData map[string]interface{}) (string, error) {
	// 合并数据源
	combinedData := make(map[string]interface{})
	for k, v := range flowData {
		combinedData[k] = v
	}
	for k, v := range eventData {
		combinedData[k] = v
	}

	// 使用现有的变量替换功能
	return utils.ReplaceVariablesInText(template, combinedData), nil
}

// parseTimeWindow 解析时间窗口
func (s *EventSuppressionService) parseTimeWindow(timeWindow, customTimeWindow string) (time.Duration, error) {
	if timeWindow == "custom" {
		return time.ParseDuration(customTimeWindow)
	}

	switch timeWindow {
	case "5m":
		return 5 * time.Minute, nil
	case "15m":
		return 15 * time.Minute, nil
	case "1h":
		return time.Hour, nil
	case "6h":
		return 6 * time.Hour, nil
	case "24h":
		return 24 * time.Hour, nil
	default:
		return 0, fmt.Errorf("unsupported time window: %s", timeWindow)
	}
}

// createNewRecord 创建新记录
func (s *EventSuppressionService) createNewRecord(
	dedupKey string,
	windowEndTime time.Time,
	eventData map[string]interface{},
	config *model.SuppressionConfig) (*SuppressionResult, error) {

	now := time.Now()
	record := model.EventSuppressionRecord{
		ID:                  uuid.New().String(),
		DedupKey:            dedupKey,
		FirstOccurrenceTime: now,
		LastOccurrenceTime:  now,
		OccurrenceCount:     1,
		WindowEndTime:       windowEndTime,
		FieldValues:         s.extractCurrentFieldValues(eventData),
		CreatedAt:           now,
		UpdatedAt:           now,
	}

	// 如果事件数据中有事件ID，记录下来
	if eventID, ok := eventData["eventId"].(string); ok {
		record.OriginalEventID = eventID
	}

	err := s.db.Create(&record).Error
	if err != nil {
		return nil, fmt.Errorf("failed to create dedup record: %v", err)
	}

	log.Printf("创建新的压制记录: %s, 压制键: %s", record.ID, dedupKey)

	return &SuppressionResult{
		IsNew:            true,
		ShouldContinue:   true,
		Record:           &record,
		UpdatedEventData: eventData,
	}, nil
}

// processExistingRecord 处理现有记录
func (s *EventSuppressionService) processExistingRecord(
	record *model.EventSuppressionRecord,
	eventData, flowData map[string]interface{},
	config *model.SuppressionConfig) (*SuppressionResult, error) {

	log.Printf("找到现有压制记录: %s, 压制键: %s", record.ID, record.DedupKey)

	// 1. 提取当前字段值
	currentFieldValues := s.extractCurrentFieldValues(eventData)

	// 2. 检查是否需要突破压制
	shouldBreak, err := s.evaluateBreakConditions(config.BreakConditions, currentFieldValues, record.FieldValues, flowData)
	if err != nil {
		log.Printf("评估突破条件失败: %v", err)
		shouldBreak = false // 默认不突破
	}

	if shouldBreak {
		log.Printf("满足突破压制条件，继续执行后续节点")
		// 突破压制，更新记录但继续执行
		return s.updateRecordAndContinue(record, currentFieldValues, eventData)
	}

	// 3. 执行压制（跳过后续节点）
	return s.suppressEvent(record, currentFieldValues, eventData)
}

// extractCurrentFieldValues 提取当前字段值（提取所有字段）
func (s *EventSuppressionService) extractCurrentFieldValues(data map[string]interface{}) model.FieldValuesMap {
	fieldValues := make(model.FieldValuesMap)

	// 直接复制所有字段
	for k, v := range data {
		fieldValues[k] = v
	}

	return fieldValues
}

// evaluateBreakConditions 评估突破条件列表
func (s *EventSuppressionService) evaluateBreakConditions(
	conditions []model.BreakCondition,
	currentValues, previousValues model.FieldValuesMap,
	flowData map[string]interface{}) (bool, error) {

	if len(conditions) == 0 {
		return false, nil
	}

	// 构建评估数据
	evalData := make(map[string]interface{})

	// 添加当前值
	for k, v := range currentValues {
		evalData[k] = v
	}

	// 添加前一次的值（以previous.前缀）
	previousData := make(map[string]interface{})
	for k, v := range previousValues {
		previousData[k] = v
	}
	evalData["previous"] = previousData

	// 添加流程数据
	for k, v := range flowData {
		if _, exists := evalData[k]; !exists {
			evalData[k] = v
		}
	}

	// 评估每个条件，任一条件满足即突破压制
	for _, condition := range conditions {
		result, err := utils.EvaluateCondition(evalData, condition.Field, condition.Operator, condition.Value)
		if err != nil {
			log.Printf("评估条件失败: %v", err)
			continue
		}
		if result {
			log.Printf("突破条件满足: %s %s %s", condition.Field, condition.Operator, condition.Value)
			return true, nil
		}
	}

	return false, nil
}

// updateRecordAndContinue 更新记录并继续执行
func (s *EventSuppressionService) updateRecordAndContinue(
	record *model.EventSuppressionRecord,
	currentFieldValues model.FieldValuesMap,
	eventData map[string]interface{}) (*SuppressionResult, error) {

	// 保存前一次的值
	record.PreviousFieldValues = record.FieldValues
	record.FieldValues = currentFieldValues

	// 更新统计信息
	record.LastOccurrenceTime = time.Now()
	record.OccurrenceCount++
	record.UpdatedAt = time.Now()

	// 保存更新
	err := s.db.Save(record).Error
	if err != nil {
		return nil, fmt.Errorf("failed to update dedup record: %v", err)
	}

	log.Printf("更新压制记录并继续执行: %s, 发生次数: %d", record.ID, record.OccurrenceCount)

	return &SuppressionResult{
		IsNew:            false,
		ShouldContinue:   true,
		Record:           record,
		UpdatedEventData: eventData,
		Message:          fmt.Sprintf("突破压制条件，继续执行，发生次数: %d", record.OccurrenceCount),
	}, nil
}

// suppressEvent 压制事件（跳过后续节点）
func (s *EventSuppressionService) suppressEvent(
	record *model.EventSuppressionRecord,
	currentFieldValues model.FieldValuesMap,
	eventData map[string]interface{}) (*SuppressionResult, error) {

	// 保存前一次的值
	record.PreviousFieldValues = record.FieldValues
	record.FieldValues = currentFieldValues

	// 更新统计信息
	record.LastOccurrenceTime = time.Now()
	record.OccurrenceCount++
	record.SuppressionCount++
	record.UpdatedAt = time.Now()

	// 保存更新
	err := s.db.Save(record).Error
	if err != nil {
		return nil, fmt.Errorf("failed to update dedup record: %v", err)
	}

	log.Printf("压制事件: %s, 发生次数: %d, 压制次数: %d", record.ID, record.OccurrenceCount, record.SuppressionCount)

	return &SuppressionResult{
		IsNew:            false,
		ShouldContinue:   false,
		Record:           record,
		UpdatedEventData: eventData,
		Message:          fmt.Sprintf("事件被压制，发生次数: %d，压制次数: %d", record.OccurrenceCount, record.SuppressionCount),
	}, nil
}

// SuppressionResult 去重处理结果
type SuppressionResult struct {
	IsNew            bool                          `json:"isNew"`            // 是否是新事件
	ShouldContinue   bool                          `json:"shouldContinue"`   // 是否应该继续执行后续节点
	Record           *model.EventSuppressionRecord `json:"record"`           // 去重记录
	UpdatedEventData map[string]interface{}        `json:"updatedEventData"` // 更新后的事件数据
	Message          string                        `json:"message"`          // 处理消息
}
