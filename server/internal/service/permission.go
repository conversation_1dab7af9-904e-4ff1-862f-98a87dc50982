package service

import (
	"fmt"
	"sec-flow-server/internal/database"
	"sec-flow-server/internal/model"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PermissionService 权限服务
type PermissionService struct {
	db *gorm.DB
}

// NewPermissionService 创建权限服务
func NewPermissionService() *PermissionService {
	return &PermissionService{
		db: database.GetDB(),
	}
}

// CreatePermissionRequest 创建权限请求
type CreatePermissionRequest struct {
	Name        string `json:"name" binding:"required"`
	DisplayName string `json:"displayName" binding:"required"`
	Description string `json:"description"`
	Resource    string `json:"resource" binding:"required"`
	Action      string `json:"action" binding:"required"`
}

// UpdatePermissionRequest 更新权限请求
type UpdatePermissionRequest struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	Description string `json:"description"`
	Resource    string `json:"resource"`
	Action      string `json:"action"`
}

// PermissionListRequest 权限列表请求
type PermissionListRequest struct {
	Page     int    `form:"page"`
	PageSize int    `form:"pageSize"`
	Keyword  string `form:"keyword"`
	Resource string `form:"resource"`
	Action   string `form:"action"`
}

// GetPermissions 获取权限列表
func (s *PermissionService) GetPermissions(req *PermissionListRequest) ([]model.Permission, int64, error) {
	var permissions []model.Permission
	var total int64

	query := s.db.Model(&model.Permission{})

	// 添加搜索条件
	if req.Keyword != "" {
		query = query.Where("name LIKE ? OR display_name LIKE ? OR description LIKE ?",
			"%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	if req.Resource != "" {
		query = query.Where("resource = ?", req.Resource)
	}

	if req.Action != "" {
		query = query.Where("action = ?", req.Action)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	offset := (req.Page - 1) * req.PageSize
	err = query.Offset(offset).Limit(req.PageSize).Find(&permissions).Error

	return permissions, total, err
}

// GetPermission 获取单个权限
func (s *PermissionService) GetPermission(permissionID string) (*model.Permission, error) {
	var permission model.Permission
	err := s.db.Preload("Roles").Where("id = ?", permissionID).First(&permission).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("权限不存在")
		}
		return nil, fmt.Errorf("查询权限失败: %v", err)
	}

	return &permission, nil
}

// CreatePermission 创建权限
func (s *PermissionService) CreatePermission(req *CreatePermissionRequest) (*model.Permission, error) {
	// 检查权限名是否已存在
	var count int64
	s.db.Model(&model.Permission{}).Where("name = ?", req.Name).Count(&count)
	if count > 0 {
		return nil, fmt.Errorf("权限名已存在")
	}

	// 检查资源:操作组合是否已存在
	s.db.Model(&model.Permission{}).Where("resource = ? AND action = ?", req.Resource, req.Action).Count(&count)
	if count > 0 {
		return nil, fmt.Errorf("该资源的此操作权限已存在")
	}

	permission := &model.Permission{
		ID:          uuid.New().String(),
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		Resource:    req.Resource,
		Action:      req.Action,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err := s.db.Create(permission).Error
	if err != nil {
		return nil, fmt.Errorf("创建权限失败: %v", err)
	}

	return permission, nil
}

// UpdatePermission 更新权限
func (s *PermissionService) UpdatePermission(permissionID string, req *UpdatePermissionRequest) (*model.Permission, error) {
	var permission model.Permission
	err := s.db.Where("id = ?", permissionID).First(&permission).Error
	if err != nil {
		return nil, fmt.Errorf("权限不存在")
	}

	// 检查权限名是否已存在
	if req.Name != "" && req.Name != permission.Name {
		var count int64
		s.db.Model(&model.Permission{}).Where("name = ? AND id != ?", req.Name, permissionID).Count(&count)
		if count > 0 {
			return nil, fmt.Errorf("权限名已存在")
		}
	}

	// 检查资源:操作组合是否已存在
	resource := req.Resource
	action := req.Action
	if resource == "" {
		resource = permission.Resource
	}
	if action == "" {
		action = permission.Action
	}

	if (req.Resource != "" && req.Resource != permission.Resource) ||
		(req.Action != "" && req.Action != permission.Action) {
		var count int64
		s.db.Model(&model.Permission{}).Where("resource = ? AND action = ? AND id != ?",
			resource, action, permissionID).Count(&count)
		if count > 0 {
			return nil, fmt.Errorf("该资源的此操作权限已存在")
		}
	}

	// 更新权限信息
	if req.Name != "" {
		permission.Name = req.Name
	}
	if req.DisplayName != "" {
		permission.DisplayName = req.DisplayName
	}
	if req.Description != "" {
		permission.Description = req.Description
	}
	if req.Resource != "" {
		permission.Resource = req.Resource
	}
	if req.Action != "" {
		permission.Action = req.Action
	}
	permission.UpdatedAt = time.Now()

	err = s.db.Save(&permission).Error
	if err != nil {
		return nil, fmt.Errorf("更新权限失败: %v", err)
	}

	return &permission, nil
}

// DeletePermission 删除权限
func (s *PermissionService) DeletePermission(permissionID string) error {
	// 检查权限是否存在
	var permission model.Permission
	err := s.db.Where("id = ?", permissionID).First(&permission).Error
	if err != nil {
		return fmt.Errorf("权限不存在")
	}

	// 检查是否有角色使用该权限
	var roleCount int64
	s.db.Model(&model.RolePermission{}).Where("permission_id = ?", permissionID).Count(&roleCount)
	if roleCount > 0 {
		return fmt.Errorf("该权限正在被使用，无法删除")
	}

	// 删除权限
	err = s.db.Delete(&permission).Error
	if err != nil {
		return fmt.Errorf("删除权限失败: %v", err)
	}

	return nil
}

// GetAllPermissions 获取所有权限
func (s *PermissionService) GetAllPermissions() ([]model.Permission, error) {
	var permissions []model.Permission
	err := s.db.Find(&permissions).Error
	return permissions, err
}

// GetPermissionsByResource 根据资源获取权限
func (s *PermissionService) GetPermissionsByResource(resource string) ([]model.Permission, error) {
	var permissions []model.Permission
	err := s.db.Where("resource = ?", resource).Find(&permissions).Error
	return permissions, err
}

// GetPermissionsByIDs 根据ID列表获取权限
func (s *PermissionService) GetPermissionsByIDs(permissionIDs []string) ([]model.Permission, error) {
	var permissions []model.Permission
	err := s.db.Where("id IN ?", permissionIDs).Find(&permissions).Error
	return permissions, err
}

// CreatePermissionsFromKeys 根据权限键创建权限
func (s *PermissionService) CreatePermissionsFromKeys(permissionKeys []string) error {
	for _, key := range permissionKeys {
		// 解析权限键 (resource:action)
		parts := make([]string, 2)
		colonIndex := -1
		for i, r := range key {
			if r == ':' {
				colonIndex = i
				break
			}
		}

		if colonIndex == -1 {
			continue // 跳过格式不正确的权限键
		}

		parts[0] = key[:colonIndex]   // resource
		parts[1] = key[colonIndex+1:] // action

		resource := parts[0]
		action := parts[1]

		// 检查权限是否已存在
		var count int64
		s.db.Model(&model.Permission{}).Where("resource = ? AND action = ?", resource, action).Count(&count)
		if count > 0 {
			continue // 权限已存在，跳过
		}

		// 创建权限
		permission := &model.Permission{
			ID:          uuid.New().String(),
			Name:        key,
			DisplayName: getPermissionDisplayName(resource, action),
			Description: fmt.Sprintf("%s的%s权限", resource, action),
			Resource:    resource,
			Action:      action,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		s.db.Create(permission)
	}

	return nil
}

// getPermissionDisplayName 获取权限显示名称
func getPermissionDisplayName(resource, action string) string {
	resourceNames := map[string]string{
		"flows":       "流程",
		"templates":   "模板",
		"users":       "用户",
		"roles":       "角色",
		"permissions": "权限",
		"executions":  "执行记录",
		"system":      "系统",
	}

	actionNames := map[string]string{
		"read":    "查看",
		"write":   "编辑",
		"delete":  "删除",
		"execute": "执行",
		"manage":  "管理",
	}

	resourceName := resourceNames[resource]
	if resourceName == "" {
		resourceName = resource
	}

	actionName := actionNames[action]
	if actionName == "" {
		actionName = action
	}

	return resourceName + ":" + actionName
}
