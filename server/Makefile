# Sec Flow Server Makefile

# 变量定义
APP_NAME=sec-flow-server
BUILD_DIR=build
MAIN_FILE=main.go

# Go相关变量
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# 默认目标
.PHONY: all
all: clean deps build

# 安装依赖
.PHONY: deps
deps:
	@echo "📦 Installing dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# 构建应用
.PHONY: build
build:
	@echo "🔨 Building application..."
	$(GOBUILD) -o $(BUILD_DIR)/$(APP_NAME) $(MAIN_FILE)

# 运行应用
.PHONY: run
run:
	@echo "🚀 Running application..."
	$(GOCMD) run $(MAIN_FILE)

# 开发模式运行（带热重载）
.PHONY: dev
dev:
	@echo "🔥 Running in development mode..."
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "Installing air for hot reload..."; \
		$(GOGET) -u github.com/cosmtrek/air; \
		air; \
	fi

# 测试
.PHONY: test
test:
	@echo "🧪 Running tests..."
	$(GOTEST) -v ./...

# 测试覆盖率
.PHONY: test-coverage
test-coverage:
	@echo "📊 Running tests with coverage..."
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html

# 清理
.PHONY: clean
clean:
	@echo "🧹 Cleaning..."
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)
	rm -f coverage.out coverage.html

# 格式化代码
.PHONY: fmt
fmt:
	@echo "💅 Formatting code..."
	$(GOCMD) fmt ./...

# 代码检查
.PHONY: lint
lint:
	@echo "🔍 Linting code..."
	@if command -v golangci-lint > /dev/null; then \
		golangci-lint run; \
	else \
		echo "Installing golangci-lint..."; \
		curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.54.2; \
		golangci-lint run; \
	fi

# 生成API文档
.PHONY: docs
docs:
	@echo "📚 Generating API documentation..."
	@if command -v swag > /dev/null; then \
		swag init; \
	else \
		echo "Installing swag..."; \
		$(GOGET) -u github.com/swaggo/swag/cmd/swag; \
		swag init; \
	fi

# Docker构建
.PHONY: docker-build
docker-build:
	@echo "🐳 Building Docker image..."
	docker build -t $(APP_NAME):latest .

# Docker运行
.PHONY: docker-run
docker-run:
	@echo "🐳 Running Docker container..."
	docker run -p 8080:8080 $(APP_NAME):latest

# 创建发布版本
.PHONY: release
release: clean deps test build
	@echo "🎉 Release build completed!"

# 显示帮助
.PHONY: help
help:
	@echo "Sec Flow Server - Available commands:"
	@echo ""
	@echo "  deps          - Install dependencies"
	@echo "  build         - Build the application"
	@echo "  run           - Run the application"
	@echo "  dev           - Run in development mode with hot reload"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage report"
	@echo "  clean         - Clean build files"
	@echo "  fmt           - Format code"
	@echo "  lint          - Lint code"
	@echo "  docs          - Generate API documentation"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run Docker container"
	@echo "  release       - Create release build"
	@echo "  help          - Show this help message"
	@echo ""
