stages:
  - package
  - deploy
  
package:
  stage: package
  tags:
    - security
  only:
    - release
    - main
  script:
    - export CI_IMAGE_TAG=${CI_COMMIT_SHA:0:8}
    - docker build -t sec-flow:$CI_IMAGE_TAG .
    - docker login --username=maxia<PERSON>n@1266397640687477 --password $REGISTRY_KEY soyoung-registry-vpc.cn-beijing.cr.aliyuncs.com/sy-sec/sec-flow
    - docker tag sec-flow:$CI_IMAGE_TAG soyoung-registry-vpc.cn-beijing.cr.aliyuncs.com/sy-sec/sec-flow:$CI_IMAGE_TAG
    - docker push soyoung-registry-vpc.cn-beijing.cr.aliyuncs.com/sy-sec/sec-flow:$CI_IMAGE_TAG

deploy:
  stage: deploy
  tags:
    - security
  only:
    - main
  script:
    - export CI_IMAGE_TAG=${CI_COMMIT_SHA:0:8}
    - kubectl --context prod -n sy-prod-sec set image deployment.apps/sec-flow sec-flow=soyoung-registry-vpc.cn-beijing.cr.aliyuncs.com/sy-sec/sec-flow:$CI_IMAGE_TAG

deployRelase:
  stage: deploy
  tags:
    - security
  only:
    - release
  script:
    - export CI_IMAGE_TAG=${CI_COMMIT_SHA:0:8}
    - kubectl --context pre -n sy-prod-sec set image deployment.apps/sec-flow sec-flow=soyoung-registry-vpc.cn-beijing.cr.aliyuncs.com/sy-sec/sec-flow:$CI_IMAGE_TAG
