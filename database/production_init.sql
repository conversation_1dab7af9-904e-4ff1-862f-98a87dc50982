-- =====================================================
-- Sec Flow 生产环境数据库初始化脚本
-- 版本: 1.0.0
-- 创建时间: 2025-01-28
-- 说明: 生产环境专用，不包含示例数据
-- 使用方法: mysql -u root -p < production_init.sql
-- =====================================================

-- 创建数据库（如果不存在）
-- CREATE DATABASE IF NOT EXISTS sec_flow CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- USE sec_flow;

-- =====================================================
-- 1. 用户权限相关表
-- =====================================================

-- 用户表
CREATE TABLE IF NOT EXISTS `tb_users` (
  `seq` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `id` VARCHAR(36) NOT NULL COMMENT '用户ID',
  `username` VARCHAR(100) NOT NULL COMMENT '用户名',
  `email` VARCHAR(255) DEFAULT NULL COMMENT '邮箱',
  `name` VARCHAR(100) DEFAULT NULL COMMENT '姓名',
  `avatar` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
  `status` VARCHAR(20) DEFAULT 'active' COMMENT '状态: active, inactive',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uniq_users_id` (`id`),
  UNIQUE KEY `uniq_users_username` (`username`),
  UNIQUE KEY `uniq_users_email` (`email`)
) COMMENT='用户表';

-- 角色表
CREATE TABLE IF NOT EXISTS `tb_roles` (
  `seq` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `id` VARCHAR(36) NOT NULL COMMENT '角色ID',
  `name` VARCHAR(100) NOT NULL COMMENT '角色名称',
  `display_name` VARCHAR(100) NOT NULL COMMENT '显示名称',
  `description` TEXT DEFAULT NULL COMMENT '描述',
  `is_default` BOOLEAN DEFAULT FALSE COMMENT '是否为默认角色',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uniq_roles_id` (`id`),
  UNIQUE KEY `uniq_roles_name` (`name`)
) COMMENT='角色表';

-- 权限表
CREATE TABLE IF NOT EXISTS `tb_permissions` (
  `seq` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `id` VARCHAR(36) NOT NULL COMMENT '权限ID',
  `name` VARCHAR(100) NOT NULL COMMENT '权限名称',
  `display_name` VARCHAR(100) NOT NULL COMMENT '显示名称',
  `description` TEXT DEFAULT NULL COMMENT '描述',
  `resource` VARCHAR(100) NOT NULL COMMENT '资源名称',
  `action` VARCHAR(50) NOT NULL COMMENT '操作名称',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uniq_permissions_id` (`id`),
  UNIQUE KEY `uniq_permissions_name` (`name`),
  KEY `idx_permissions_resource` (`resource`),
  KEY `idx_permissions_action` (`action`)
) COMMENT='权限表';

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS `tb_user_roles` (
  `seq` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `user_id` VARCHAR(36) NOT NULL COMMENT '用户ID',
  `role_id` VARCHAR(36) NOT NULL COMMENT '角色ID',
  KEY `idx_user_roles_user_id` (`user_id`),
  KEY `idx_user_roles_role_id` (`role_id`)
) COMMENT='用户角色关联表';

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS `tb_role_permissions` (
  `seq` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `role_id` VARCHAR(36) NOT NULL COMMENT '角色ID',
  `permission_id` VARCHAR(36) NOT NULL COMMENT '权限ID',
  KEY `idx_role_permissions_role_id` (`role_id`),
  KEY `idx_role_permissions_permission_id` (`permission_id`)
) COMMENT='角色权限关联表';

-- =====================================================
-- 2. 流程相关表
-- =====================================================

-- 流程表
CREATE TABLE IF NOT EXISTS `tb_flows` (
  `seq` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `id` VARCHAR(36) NOT NULL COMMENT '流程ID',
  `name` VARCHAR(255) NOT NULL COMMENT '流程名称',
  `execution_time` VARCHAR(255) DEFAULT NULL COMMENT '执行时机',
  `description` TEXT DEFAULT NULL COMMENT '描述',
  `data` LONGTEXT DEFAULT NULL COMMENT '流程数据JSON',
  `execution_retention_days` INT DEFAULT 30 COMMENT '执行记录保存天数，0表示永久保存',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` VARCHAR(36) DEFAULT NULL COMMENT '创建者ID',
  `status` VARCHAR(20) DEFAULT 'draft' COMMENT '流程状态: draft, published, archived',
  UNIQUE KEY `uniq_flows_id` (`id`),
  KEY `idx_flows_name` (`name`),
  KEY `idx_flows_status` (`status`),
  KEY `idx_flows_created_by` (`created_by`),
  KEY `idx_flows_created_at` (`created_at`),
  KEY `idx_flows_status_created_at` (`status`, `created_at`)
) COMMENT='流程表';

-- 流程执行记录表
CREATE TABLE IF NOT EXISTS `tb_flow_executions` (
  `seq` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `id` VARCHAR(36) NOT NULL COMMENT '执行ID',
  `flow_id` VARCHAR(36) NOT NULL COMMENT '流程ID',
  `flow_name` VARCHAR(255) NOT NULL COMMENT '流程名称',
  `flow_snapshot` LONGTEXT DEFAULT NULL COMMENT '执行时的流程配置快照',
  `trigger_type` VARCHAR(20) NOT NULL COMMENT '触发类型: manual, cron, http',
  `trigger_source` VARCHAR(255) DEFAULT NULL COMMENT '触发来源',
  `status` VARCHAR(20) NOT NULL COMMENT '执行状态: running, success, failed, cancelled',
  `start_time` DATETIME NOT NULL COMMENT '开始时间',
  `end_time` DATETIME NULL DEFAULT NULL COMMENT '结束时间',
  `duration` BIGINT DEFAULT NULL COMMENT '执行时长（毫秒）',
  `input_data` TEXT DEFAULT NULL COMMENT '输入数据JSON',
  `output_data` LONGTEXT DEFAULT NULL COMMENT '输出数据JSON',
  `execution_log` LONGTEXT DEFAULT NULL COMMENT '执行日志JSON',
  `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uniq_flow_executions_id` (`id`),
  KEY `idx_flow_executions_flow_id` (`flow_id`),
  KEY `idx_flow_executions_status` (`status`),
  KEY `idx_flow_executions_trigger_type` (`trigger_type`),
  KEY `idx_flow_executions_start_time` (`start_time`),
  KEY `idx_flow_executions_created_at` (`created_at`),
  KEY `idx_flow_executions_flow_status` (`flow_id`, `status`),
  KEY `idx_flow_executions_trigger_start_time` (`trigger_type`, `start_time`)
) COMMENT='流程执行记录表';

-- 模板表
CREATE TABLE IF NOT EXISTS `tb_templates` (
  `seq` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `id` VARCHAR(36) NOT NULL COMMENT '模板ID',
  `name` VARCHAR(255) NOT NULL COMMENT '模板名称',
  `description` TEXT DEFAULT NULL COMMENT '描述',
  `category` VARCHAR(50) NOT NULL COMMENT '分类: basic, advanced, custom',
  `node_type` VARCHAR(50) NOT NULL COMMENT '节点类型: rect, circle, diamond',
  `icon` VARCHAR(100) DEFAULT NULL COMMENT '图标',
  `config` TEXT DEFAULT NULL COMMENT '配置JSON',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY `uniq_templates_id` (`id`),
  KEY `idx_templates_name` (`name`),
  KEY `idx_templates_category` (`category`),
  KEY `idx_templates_node_type` (`node_type`)
) COMMENT='模板表';

-- =====================================================
-- 3. 系统管理相关表
-- =====================================================

-- 分布式锁表
CREATE TABLE IF NOT EXISTS `tb_distributed_locks` (
  `seq` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `id` VARCHAR(100) NOT NULL COMMENT '锁ID',
  `lock_type` VARCHAR(50) NOT NULL COMMENT '锁类型',
  `resource_id` VARCHAR(100) NOT NULL COMMENT '资源ID',
  `instance_id` VARCHAR(100) NOT NULL COMMENT '实例ID',
  `expires_at` DATETIME NOT NULL COMMENT '过期时间',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `metadata` TEXT DEFAULT NULL COMMENT '元数据JSON',
  UNIQUE KEY `uniq_distributed_locks_id` (`id`),
  KEY `idx_distributed_locks_expires_at` (`expires_at`),
  KEY `idx_distributed_locks_lock_type` (`lock_type`),
  KEY `idx_distributed_locks_resource_id` (`resource_id`)
) COMMENT='分布式锁表';

-- =====================================================
-- 4. 事件管理相关表
-- =====================================================

-- 事件分类表
CREATE TABLE IF NOT EXISTS `tb_event_categories` (
  `seq` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `id` VARCHAR(36) NOT NULL COMMENT '分类ID',
  `name` VARCHAR(100) NOT NULL COMMENT '分类名称',
  `description` VARCHAR(500) DEFAULT NULL COMMENT '描述',
  `owner_id` VARCHAR(36) NOT NULL COMMENT '负责人ID',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` DATETIME NULL DEFAULT NULL COMMENT '删除时间',
  UNIQUE KEY `uniq_event_categories_id` (`id`),
  UNIQUE KEY `uniq_event_categories_name` (`name`),
  KEY `idx_event_categories_owner_id` (`owner_id`),
  KEY `idx_event_categories_deleted_at` (`deleted_at`)
) COMMENT='事件分类表';

-- 事件运营表
CREATE TABLE IF NOT EXISTS `tb_event_operations` (
  `seq` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `id` VARCHAR(36) NOT NULL COMMENT '事件ID',
  `occurred_at` DATETIME NOT NULL COMMENT '发生时间',
  `occurred_condition` VARCHAR(1000) NOT NULL COMMENT '发生条件',
  `category_id` VARCHAR(36) NOT NULL COMMENT '分类ID',
  `description` VARCHAR(2000) DEFAULT NULL COMMENT '描述',
  `owner_id` VARCHAR(36) NOT NULL COMMENT '负责人ID',
  `related_employee_ids` VARCHAR(1000) DEFAULT NULL COMMENT '相关员工ID列表JSON',
  `process_type` VARCHAR(20) NOT NULL DEFAULT 'unprocessed' COMMENT '处理类型: unprocessed, processed, suppressed',
  `process_description` VARCHAR(2000) DEFAULT NULL COMMENT '处理描述',
  `process_completed_at` DATETIME NULL DEFAULT NULL COMMENT '处理完成时间',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` DATETIME NULL DEFAULT NULL COMMENT '删除时间',
  UNIQUE KEY `uniq_event_operations_id` (`id`),
  KEY `idx_event_operations_occurred_at` (`occurred_at`),
  KEY `idx_event_operations_category_id` (`category_id`),
  KEY `idx_event_operations_owner_id` (`owner_id`),
  KEY `idx_event_operations_process_type` (`process_type`),
  KEY `idx_event_operations_process_completed_at` (`process_completed_at`),
  KEY `idx_event_operations_deleted_at` (`deleted_at`),
  KEY `idx_event_operations_category_process` (`category_id`, `process_type`)
) COMMENT='事件运营表';

-- 事件抑制记录表
CREATE TABLE IF NOT EXISTS `tb_event_suppression_records` (
  `seq` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `id` VARCHAR(36) NOT NULL COMMENT '记录ID',
  `category_id` VARCHAR(36) NOT NULL COMMENT '分类ID',
  `suppression_condition` VARCHAR(1000) NOT NULL COMMENT '抑制条件',
  `suppression_duration` INT NOT NULL COMMENT '抑制时长（分钟）',
  `suppressed_at` DATETIME NOT NULL COMMENT '抑制时间',
  `expires_at` DATETIME NOT NULL COMMENT '过期时间',
  `created_by` VARCHAR(36) NOT NULL COMMENT '创建者ID',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` DATETIME NULL DEFAULT NULL COMMENT '删除时间',
  UNIQUE KEY `uniq_event_suppression_records_id` (`id`),
  KEY `idx_event_suppression_category_id` (`category_id`),
  KEY `idx_event_suppression_expires_at` (`expires_at`),
  KEY `idx_event_suppression_created_by` (`created_by`),
  KEY `idx_event_suppression_deleted_at` (`deleted_at`)
) COMMENT='事件抑制记录表';

-- =====================================================
-- 5. 数据库配置优化
-- =====================================================

-- 设置字符集和排序规则
-- ALTER DATABASE sec_flow CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 完成初始化
-- SELECT '✅ Sec Flow 生产环境数据库初始化完成！' AS message;
-- SELECT CONCAT('📊 共创建 ', COUNT(*), ' 张表') AS table_count
-- FROM information_schema.tables
-- WHERE table_schema = 'sec_flow';

-- 注意：生产环境不包含示例数据，请手动创建管理员用户和权限配置
