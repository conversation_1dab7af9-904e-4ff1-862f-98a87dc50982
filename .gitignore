# Sec Flow Project .gitignore

# ===== Go Backend =====
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
server/sec-flow-server
server/build/
server/bin/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out
coverage.html
coverage.out

# Go workspace file
go.work
go.work.sum

# Dependency directories (remove the comment below to include it)
vendor/

# Go mod cache
server/go.sum.backup

# ===== Frontend (React/Vite) =====
# Dependencies
web/node_modules/
node_modules/

# Production builds
web/dist/
web/dist-ssr/
web/build/
dist/
build/

# Local env files
web/.env.local
web/.env.development.local
web/.env.test.local
web/.env.production.local
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# ===== IDE and Editor =====
# VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

# GoLand
.idea/

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== System Files =====
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== Application Specific =====
# Log files
server/logs/
logs/
*.log

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Database files (if using local SQLite)
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
server/conf/config-local-override.yaml
server/conf/config-dev-override.yaml
server/conf/config-prod-override.yaml
.env

# Backup files
*.bak
*.backup
*.orig

# Test output
test-results/
test-output/

# Documentation build
docs/_build/
docs/build/

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ===== Project Specific =====
# Sec Flow specific ignores
server/sec-flow-server.exe
server/main.exe
*.prof

# Hot reload files
.air/
tmp/

# Swagger/OpenAPI generated files
docs/swagger/

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
k8s/secrets/
k8s/local/

.cursor/

server/build
server/bin