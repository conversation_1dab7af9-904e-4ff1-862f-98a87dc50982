# 多阶段构建 Dockerfile
# 阶段1: 构建前端
FROM soyoung-registry-vpc.cn-beijing.cr.aliyuncs.com/sy-sec/node:20-alpine AS frontend-builder

WORKDIR /app/web
# 复制前端依赖文件
COPY web/package*.json ./
RUN npm config set registry https://registry.npmmirror.com --global \
 && npm ci
COPY web/ ./
RUN npm run build

# 阶段2: 构建后端
FROM soyoung-registry-vpc.cn-beijing.cr.aliyuncs.com/sy-sec/golang:1.23-alpine AS backend-builder
WORKDIR /app/server
# 使用阿里云 apk 源，加装仅构建期必须的依赖（git用于 go mod 下载；ca-certificates用于 HTTPS 证书校验）
RUN sed -i 's|https://dl-cdn.alpinelinux.org/alpine|https://mirrors.aliyun.com/alpine|g' /etc/apk/repositories \
 && apk add --no-cache git ca-certificates
COPY server/go.mod server/go.sum ./
ENV GOPROXY=https://goproxy.cn,direct
RUN go mod download
COPY server/ ./
RUN CGO_ENABLED=0 GOOS=linux go build -o sec-flow-server main.go

# 阶段3: 最终运行镜像（无nginx、无supervisor）
FROM soyoung-registry-vpc.cn-beijing.cr.aliyuncs.com/sy-sec/alpine:3.20
WORKDIR /app/server
# 使用阿里云 apk 源，安装依赖并设置时区为 Asia/Shanghai
RUN sed -i 's|https://dl-cdn.alpinelinux.org/alpine|https://mirrors.aliyun.com/alpine|g' /etc/apk/repositories \
 && apk add --no-cache ca-certificates tzdata \
 && ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
 && echo "Asia/Shanghai" > /etc/timezone \
 && addgroup -S app && adduser -S app -G app
ENV TZ=Asia/Shanghai

# 复制后端与配置
COPY --from=backend-builder /app/server/sec-flow-server /app/server/
COPY --from=backend-builder /app/server/conf /app/server/conf/

# 复制前端静态产物到 /app/web
COPY --from=frontend-builder /app/web/dist /app/web/

# 运行时环境
ENV APP_ENV=prod \
    STATIC_DIR=/app/web

EXPOSE 8080
USER app
ENTRYPOINT ["/app/server/sec-flow-server"]
