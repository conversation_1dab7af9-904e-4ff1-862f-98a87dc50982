import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Tag,
  Popconfirm,
  Card,
  Row,
  Col,
  Switch,
  Descriptions,
  Drawer,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  TeamOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { roleApi, permissionApi } from '../services/authApi';
import { usePermission } from '../contexts/UserContext';
import type {
  Role,
  Permission,
  CreateRoleRequest,
  UpdateRoleRequest,
  RoleListRequest,
  PaginatedResponse,
} from '../types/auth';

const { Option } = Select;
const { TextArea } = Input;

const RoleManagement: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchKeyword, setSearchKeyword] = useState('');

  // 模态框状态
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [form] = Form.useForm();

  // 详情抽屉
  const [detailVisible, setDetailVisible] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);

  // 权限检查
  const canCreate = usePermission('roles:write');
  const canEdit = usePermission('roles:write');
  const canDelete = usePermission('roles:delete');

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      setLoading(true);
      const params: RoleListRequest = {
        page: current,
        pageSize,
        keyword: searchKeyword || undefined,
      };

      const response = await roleApi.getRoles(params);
      if (response.data.code === 200) {
        const data: PaginatedResponse<Role> = response.data.data;
        setRoles(data.list);
        setTotal(data.total);
      }
    } catch (error) {
      message.error('获取角色列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取所有权限
  const fetchPermissions = async () => {
    try {
      const response = await permissionApi.getAllPermissions();
      if (response.data.code === 200) {
        setPermissions(response.data.data);
      }
    } catch (error) {
      message.error('获取权限列表失败');
    }
  };

  useEffect(() => {
    fetchRoles();
  }, [current, pageSize, searchKeyword]);

  useEffect(() => {
    fetchPermissions();
  }, []);

  // 处理搜索
  const handleSearch = () => {
    setCurrent(1);
    fetchRoles();
  };

  // 重置搜索
  const handleResetSearch = () => {
    setSearchKeyword('');
    setCurrent(1);
  };

  // 创建角色
  const handleCreate = () => {
    setEditingRole(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 编辑角色
  const handleEdit = (role: Role) => {
    setEditingRole(role);
    form.setFieldsValue({
      name: role.name,
      displayName: role.displayName,
      description: role.description,
      isDefault: role.isDefault,
      permissionIds: role.permissions.map((permission) => permission.id),
    });
    setModalVisible(true);
  };

  // 查看角色详情
  const handleViewDetail = (role: Role) => {
    setSelectedRole(role);
    setDetailVisible(true);
  };

  // 删除角色
  const handleDelete = async (roleId: string) => {
    try {
      await roleApi.deleteRole(roleId);
      message.success('角色删除成功');
      fetchRoles();
    } catch (error: any) {
      message.error(error.response?.data?.message || '角色删除失败');
    }
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    try {
      if (editingRole) {
        // 更新角色
        const updateData: UpdateRoleRequest = {
          name: values.name,
          displayName: values.displayName,
          description: values.description,
          isDefault: values.isDefault,
          permissionIds: values.permissionIds,
        };
        await roleApi.updateRole(editingRole.id, updateData);
        message.success('角色更新成功');
      } else {
        // 创建角色
        const createData: CreateRoleRequest = {
          name: values.name,
          displayName: values.displayName,
          description: values.description,
          isDefault: values.isDefault,
          permissionIds: values.permissionIds,
        };
        await roleApi.createRole(createData);
        message.success('角色创建成功');
      }
      setModalVisible(false);
      fetchRoles();
    } catch (error: any) {
      message.error(error.response?.data?.message || '操作失败');
    }
  };

  // 按资源分组权限
  const groupPermissionsByResource = (permissions: Permission[]) => {
    const grouped: { [key: string]: Permission[] } = {};
    permissions.forEach((permission) => {
      if (!grouped[permission.resource]) {
        grouped[permission.resource] = [];
      }
      grouped[permission.resource].push(permission);
    });
    return grouped;
  };

  const groupedPermissions = groupPermissionsByResource(permissions);

  // 表格列定义
  const columns = [
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Role) => (
        <Space>
          <TeamOutlined />
          {text}
          {record.isDefault && <Tag color="orange">默认</Tag>}
        </Space>
      ),
    },
    {
      title: '显示名称',
      dataIndex: 'displayName',
      key: 'displayName',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '权限数量',
      dataIndex: 'permissions',
      key: 'permissionCount',
      render: (permissions: Permission[]) => (
        <Tag color="blue">{permissions.length} 个权限</Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Role) => (
        <Space>
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          {canEdit && (
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
          )}
          {canDelete && !record.isDefault && (
            <Popconfirm
              title="确定要删除这个角色吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button danger size="small" icon={<DeleteOutlined />}>
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card title="角色管理" style={{ marginBottom: 16 }}>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Input
              placeholder="搜索角色名称或描述"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={16}>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleResetSearch}>重置</Button>
              {canCreate && (
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                  新建角色
                </Button>
              )}
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={roles}
          rowKey="id"
          loading={loading}
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, size) => {
              setCurrent(page);
              setPageSize(size || 10);
            },
          }}
        />
      </Card>

      {/* 创建/编辑角色模态框 */}
      <Modal
        title={editingRole ? '编辑角色' : '新建角色'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            isDefault: false,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="角色名称"
                name="name"
                rules={[{ required: true, message: '请输入角色名称' }]}
              >
                <Input placeholder="请输入角色名称（英文标识）" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="显示名称"
                name="displayName"
                rules={[{ required: true, message: '请输入显示名称' }]}
              >
                <Input placeholder="请输入显示名称（中文）" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="描述" name="description">
            <TextArea rows={3} placeholder="请输入角色描述" />
          </Form.Item>

          <Form.Item label="默认角色" name="isDefault" valuePropName="checked">
            <Switch checkedChildren="是" unCheckedChildren="否" />
          </Form.Item>

          <Form.Item
            label="权限"
            name="permissionIds"
            rules={[{ required: true, message: '请选择至少一个权限' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择权限"
              optionFilterProp="children"
              showSearch
              style={{ width: '100%' }}
            >
              {Object.entries(groupedPermissions).map(([resource, perms]) => (
                <Select.OptGroup key={resource} label={`${resource} 相关权限`}>
                  {perms.map((permission) => (
                    <Option key={permission.id} value={permission.id}>
                      {permission.displayName}
                    </Option>
                  ))}
                </Select.OptGroup>
              ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                {editingRole ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 角色详情抽屉 */}
      <Drawer
        title="角色详情"
        placement="right"
        width={600}
        onClose={() => setDetailVisible(false)}
        open={detailVisible}
      >
        {selectedRole && (
          <div>
            <Descriptions bordered column={1}>
              <Descriptions.Item label="角色名称">
                <Space>
                  {selectedRole.name}
                  {selectedRole.isDefault && <Tag color="orange">默认角色</Tag>}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="显示名称">
                {selectedRole.displayName}
              </Descriptions.Item>
              <Descriptions.Item label="描述">
                {selectedRole.description || '无'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(selectedRole.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {new Date(selectedRole.updatedAt).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>

            <Card title="权限列表" style={{ marginTop: 16 }}>
              {Object.entries(groupPermissionsByResource(selectedRole.permissions)).map(
                ([resource, perms]) => (
                  <div key={resource} style={{ marginBottom: 16 }}>
                    <h4>{resource} 相关权限：</h4>
                    <Space wrap>
                      {perms.map((permission) => (
                        <Tag key={permission.id} color="blue">
                          {permission.displayName}
                        </Tag>
                      ))}
                    </Space>
                  </div>
                )
              )}
            </Card>

            {selectedRole.users && selectedRole.users.length > 0 && (
              <Card title="关联用户" style={{ marginTop: 16 }}>
                <Space wrap>
                  {selectedRole.users.map((user) => (
                    <Tag key={user.id} color="green">
                      {user.username}
                    </Tag>
                  ))}
                </Space>
              </Card>
            )}
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default RoleManagement; 