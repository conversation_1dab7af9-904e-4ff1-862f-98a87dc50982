import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Avatar,
  Descriptions,
  Tag,
  Space,
  message,
  Row,
  Col,
  Divider,
} from 'antd';
import { UserOutlined, EditOutlined, SaveOutlined } from '@ant-design/icons';
import { useUser } from '../contexts/UserContext';
import { userAuthApi } from '../services/authApi';
import type { UpdateProfileRequest } from '../types/auth';

const Profile: React.FC = () => {
  const { user, refreshUser } = useUser();
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  // 开始编辑
  const handleStartEdit = () => {
    if (user) {
      form.setFieldsValue({
        username: user.username,
        email: user.email,
        avatar: user.avatar,
      });
      setEditing(true);
    }
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditing(false);
    form.resetFields();
  };

  // 保存修改
  const handleSave = async (values: any) => {
    try {
      setLoading(true);
      const updateData: UpdateProfileRequest = {
        username: values.username,
        email: values.email,
        avatar: values.avatar,
      };

      await userAuthApi.updateProfile(updateData);
      message.success('个人信息更新成功');
      setEditing(false);
      await refreshUser(); // 刷新用户信息
    } catch (error: any) {
      message.error(error.response?.data?.message || '更新失败');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <UserOutlined style={{ fontSize: '48px', color: '#ccc' }} />
          <p style={{ marginTop: '16px', color: '#999' }}>用户信息加载中...</p>
        </div>
      </Card>
    );
  }

  return (
    <div>
      <Row gutter={24}>
        <Col span={8}>
          {/* 用户头像和基本信息 */}
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Avatar
                size={100}
                src={user.avatar || undefined}
                icon={<UserOutlined />}
                style={{ marginBottom: '16px' }}
              />
              <h2 style={{ marginBottom: '8px' }}>{user.username}</h2>
              <p style={{ color: '#666', marginBottom: '16px' }}>
                {user.email || '未设置邮箱'}
              </p>
              <Space wrap>
                {user.roles.map((role) => (
                  <Tag key={role.id} color="blue">
                    {role.displayName}
                  </Tag>
                ))}
              </Space>
            </div>
          </Card>

          {/* 账户状态信息 */}
          <Card title="账户信息" style={{ marginTop: '16px' }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="用户ID">
                <code>{user.id}</code>
              </Descriptions.Item>
              <Descriptions.Item label="账户状态">
                <Tag color={user.status === 'active' ? 'green' : 'red'}>
                  {user.status === 'active' ? '正常' : '禁用'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(user.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {new Date(user.updatedAt).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col span={16}>
          {/* 个人信息编辑 */}
          <Card 
            title="个人信息" 
            extra={
              !editing && (
                <Button 
                  type="primary" 
                  icon={<EditOutlined />} 
                  onClick={handleStartEdit}
                >
                  编辑信息
                </Button>
              )
            }
          >
            {editing ? (
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSave}
                style={{ maxWidth: '500px' }}
              >
                <Form.Item
                  label="用户名"
                  name="username"
                  rules={[{ required: true, message: '请输入用户名' }]}
                >
                  <Input placeholder="请输入用户名" />
                </Form.Item>

                <Form.Item
                  label="邮箱"
                  name="email"
                  rules={[
                    { type: 'email', message: '请输入有效的邮箱地址' },
                  ]}
                >
                  <Input placeholder="请输入邮箱地址" />
                </Form.Item>

                <Form.Item
                  label="头像链接"
                  name="avatar"
                >
                  <Input placeholder="请输入头像链接" />
                </Form.Item>

                <Form.Item>
                  <Space>
                    <Button 
                      type="primary" 
                      htmlType="submit" 
                      loading={loading}
                      icon={<SaveOutlined />}
                    >
                      保存
                    </Button>
                    <Button onClick={handleCancelEdit}>
                      取消
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            ) : (
              <Descriptions bordered column={1}>
                <Descriptions.Item label="用户名">
                  {user.username}
                </Descriptions.Item>
                <Descriptions.Item label="邮箱">
                  {user.email || '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="头像链接">
                  {user.avatar ? (
                    <a href={user.avatar} target="_blank" rel="noopener noreferrer">
                      {user.avatar}
                    </a>
                  ) : (
                    '未设置'
                  )}
                </Descriptions.Item>
              </Descriptions>
            )}
          </Card>

          {/* 角色权限信息 */}
          <Card title="角色权限" style={{ marginTop: '16px' }}>
            <div>
              <h4>我的角色：</h4>
              <Space wrap style={{ marginBottom: '16px' }}>
                {user.roles.map((role) => (
                  <Tag key={role.id} color="blue" style={{ padding: '4px 8px' }}>
                    <strong>{role.displayName}</strong>
                    {role.isDefault && <span style={{ marginLeft: '4px' }}>(默认)</span>}
                  </Tag>
                ))}
              </Space>

              <Divider />

              <h4>角色描述：</h4>
              {user.roles.map((role) => (
                <div key={role.id} style={{ marginBottom: '12px' }}>
                  <Tag color="blue">{role.displayName}</Tag>
                  <span style={{ marginLeft: '8px', color: '#666' }}>
                    {role.description || '无描述'}
                  </span>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Profile; 