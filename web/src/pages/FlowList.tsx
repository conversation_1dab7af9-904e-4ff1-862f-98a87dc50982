import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Table, Button, Space, Card, Input, Popconfirm, message, Tag, Select } from 'antd';
import { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, HistoryOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import type { FlowWithLastExecution, ExecutionStatus } from '../types/execution';
import PermissionGuard from '../components/PermissionGuard';
import { config } from '../config';

const API_URL = config.apiBaseUrl;

const FlowList: React.FC = () => {
  const [flows, setFlows] = useState<FlowWithLastExecution[]>([]);
  const [loading, setLoading] = useState(false);
  const [keyword, setKeyword] = useState('');
  const [description, setDescription] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [updatingStatus, setUpdatingStatus] = useState<string | null>(null);
  const navigate = useNavigate();
  const updatingRef = useRef<Set<string>>(new Set());

  const fetchFlows = useCallback(async () => {
    setLoading(true);
    try {
      const params: any = {};
      if (keyword.trim()) params.keyword = keyword.trim();
      if (description.trim()) params.description = description.trim();
      if (statusFilter) params.status = statusFilter;

      const response = await axios.get(`${API_URL}/flows/with-executions`, {
        params
      });
      const flowsData = response.data.data.data || [];
      console.log('Fetched flows data:', flowsData);
      setFlows(flowsData);
    } catch (error) {
      message.error('获取流程列表失败');
      console.error('Failed to fetch flows:', error);
    } finally {
      setLoading(false);
    }
  }, [keyword, description, statusFilter]);

  useEffect(() => {
    fetchFlows();
  }, []);

  const handleDelete = async (id: string) => {
    try {
      await axios.delete(`${API_URL}/flows/${id}`);
      message.success('删除成功');
      fetchFlows();
    } catch (error) {
      message.error('删除失败');
      console.error('Failed to delete flow:', error);
    }
  };

  const handleStatusChange = useCallback(async (id: string, currentStatus: string) => {
    console.log('handleStatusChange called with:', { id, currentStatus });

    // 防止重复请求
    if (updatingRef.current.has(id)) {
      console.log('Already updating this flow, ignoring request');
      return;
    }

    // 状态循环：草稿 → 已发布 → 已归档 → 草稿
    let newStatus: string;
    let statusText: string;

    switch (currentStatus) {
      case 'draft':
        newStatus = 'published';
        statusText = '已发布';
        break;
      case 'published':
        newStatus = 'archived';
        statusText = '已归档';
        break;
      case 'archived':
        newStatus = 'draft';
        statusText = '草稿';
        break;
      default:
        newStatus = 'published';
        statusText = '已发布';
    }

    updatingRef.current.add(id);
    setUpdatingStatus(id);

    try {
      console.log('Updating flow status:', { id, currentStatus, newStatus });
      const response = await axios.put(`${API_URL}/flows/${id}`, {
        status: newStatus
      });
      console.log('Update response:', response.data);

      // 直接更新本地状态，避免重新获取数据
      setFlows(prevFlows =>
        prevFlows.map(flow =>
          flow.id === id ? { ...flow, status: newStatus } : flow
        )
      );

      message.success(`状态已更新为${statusText}`);
    } catch (error) {
      message.error('状态更新失败');
      console.error('Failed to update status:', error);
    } finally {
      updatingRef.current.delete(id);
      setUpdatingStatus(null);
    }
  }, []);

  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'published':
        return <Tag color="green">已发布</Tag>;
      case 'draft':
        return <Tag color="orange">草稿</Tag>;
      case 'archived':
        return <Tag color="gray">已归档</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const getExecutionButtonProps = (lastExecution?: FlowWithLastExecution['lastExecution']) => {
    if (!lastExecution) {
      return { type: 'default' as const };
    }

    switch (lastExecution.status as ExecutionStatus) {
      case 'completed':
        return { type: 'primary' as const, style: { backgroundColor: '#52c41a', borderColor: '#52c41a' } };
      case 'failed':
        return { type: 'primary' as const, style: { backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' } };
      case 'running':
        return { type: 'primary' as const };
      default:
        return { type: 'default' as const };
    }
  };

  const getStatusButtonProps = (status: string) => {
    switch (status) {
      case 'draft':
        return {
          type: 'default' as const,
          children: '发布',
          title: '点击发布流程'
        };
      case 'published':
        return {
          type: 'primary' as const,
          children: '归档',
          title: '点击归档流程'
        };
      case 'archived':
        return {
          type: 'default' as const,
          style: { backgroundColor: '#f0f0f0', borderColor: '#d9d9d9', color: '#666' },
          children: '激活',
          title: '点击激活流程'
        };
      default:
        return {
          type: 'default' as const,
          children: '发布',
          title: '点击发布流程'
        };
    }
  };

  const handleViewExecutions = (flowId: string) => {
    navigate(`/flow/${flowId}/executions`);
  };



  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '执行时机',
      dataIndex: 'executionTime',
      key: 'executionTime',
      width: 200,
      render: (executionTime: string) => executionTime || '-',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => getStatusDisplay(status),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 160,
      render: (updatedAt: string) => {
        if (!updatedAt) return '-';
        const date = new Date(updatedAt);
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
        }).replace(/\//g, '-');
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 320,
      render: (_: any, record: FlowWithLastExecution) => {
        const flowId = record.id;
        const flowStatus = record.status;
        const executionButtonProps = getExecutionButtonProps(record.lastExecution);
        const statusButtonProps = getStatusButtonProps(flowStatus);

        return (
          <Space size="small">
            <PermissionGuard permission="flows:write">
              <Button
                {...statusButtonProps}
                size="small"
                onClick={() => {
                  console.log('Status button clicked:', { flowId, flowStatus });
                  // 防止重复点击
                  if (updatingStatus === flowId) {
                    console.log('Already updating, ignoring click');
                    return;
                  }
                  handleStatusChange(flowId, flowStatus);
                }}
                loading={updatingStatus === flowId}
                disabled={updatingStatus !== null && updatingStatus !== flowId}
              />
            </PermissionGuard>
            <PermissionGuard permission="flows:write">
              <Button
                type="primary"
                size="small"
                icon={<EditOutlined />}
                onClick={() => navigate(`/flow/${record.id}`)}
              >
                编辑
              </Button>
            </PermissionGuard>
            <PermissionGuard permission="executions:read">
              <Button
                {...executionButtonProps}
                size="small"
                icon={<HistoryOutlined />}
                onClick={() => handleViewExecutions(record.id)}
              >
                执行记录
              </Button>
            </PermissionGuard>
            <PermissionGuard permission="flows:delete">
              <Popconfirm
                title="确定要删除这个流程吗?"
                onConfirm={() => handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button danger size="small" icon={<DeleteOutlined />}>删除</Button>
              </Popconfirm>
            </PermissionGuard>
          </Space>
        );
      },
    },
  ];

  return (
    <Card title="流程图列表" extra={
      <PermissionGuard permission="flows:write">
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={() => navigate('/flow/new')}
        >
          新建流程
        </Button>
      </PermissionGuard>
    }>
      <Space style={{ marginBottom: 16 }} direction="horizontal" wrap>
        <Input
          placeholder="搜索流程名称"
          value={keyword}
          onChange={e => setKeyword(e.target.value)}
          style={{ width: 200 }}
          allowClear
        />
        <Input
          placeholder="搜索描述内容"
          value={description}
          onChange={e => setDescription(e.target.value)}
          style={{ width: 200 }}
          allowClear
        />
        <Select
          placeholder="选择状态"
          value={statusFilter}
          onChange={setStatusFilter}
          style={{ width: 120 }}
          allowClear
        >
          <Select.Option value="draft">草稿</Select.Option>
          <Select.Option value="published">已发布</Select.Option>
          <Select.Option value="archived">已归档</Select.Option>
        </Select>
        <Button
          type="primary"
          icon={<SearchOutlined />}
          onClick={fetchFlows}
        >
          搜索
        </Button>
      </Space>
      
      <Table
        columns={columns}
        dataSource={flows}
        rowKey={(record) => `${record.id}-${record.status}-${record.updatedAt}`}
        loading={loading}
      />
    </Card>
  );
};

export default FlowList;