import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Tag,
  Popconfirm,
  Card,
  Row,
  Col,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  KeyOutlined,
} from '@ant-design/icons';
import { permissionApi } from '../services/authApi';
import { usePermission } from '../contexts/UserContext';
import type {
  Permission,
  CreatePermissionRequest,
  UpdatePermissionRequest,
  PermissionListRequest,
  PaginatedResponse,
} from '../types/auth';

const { Option } = Select;
const { TextArea } = Input;

const PermissionManagement: React.FC = () => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedResource, setSelectedResource] = useState<string>('');
  const [selectedAction, setSelectedAction] = useState<string>('');

  // 模态框状态
  const [modalVisible, setModalVisible] = useState(false);
  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);
  const [form] = Form.useForm();

  // 权限检查
  const canCreate = usePermission('permissions:write');
  const canEdit = usePermission('permissions:write');
  const canDelete = usePermission('permissions:delete');

  // 资源和操作选项
  const resourceOptions = [
    { value: 'flows', label: '流程' },
    { value: 'templates', label: '模板' },
    { value: 'users', label: '用户' },
    { value: 'roles', label: '角色' },
    { value: 'permissions', label: '权限' },
    { value: 'executions', label: '执行记录' },
    { value: 'system', label: '系统' },
  ];

  const actionOptions = [
    { value: 'read', label: '查看' },
    { value: 'write', label: '编辑' },
    { value: 'delete', label: '删除' },
    { value: 'execute', label: '执行' },
    { value: 'manage', label: '管理' },
  ];

  // 获取权限列表
  const fetchPermissions = async () => {
    try {
      setLoading(true);
      const params: PermissionListRequest = {
        page: current,
        pageSize,
        keyword: searchKeyword || undefined,
        resource: selectedResource || undefined,
        action: selectedAction || undefined,
      };

      const response = await permissionApi.getPermissions(params);
      if (response.data.code === 200) {
        const data: PaginatedResponse<Permission> = response.data.data;
        setPermissions(data.list);
        setTotal(data.total);
      }
    } catch (error) {
      message.error('获取权限列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPermissions();
  }, [current, pageSize, searchKeyword, selectedResource, selectedAction]);

  // 处理搜索
  const handleSearch = () => {
    setCurrent(1);
    fetchPermissions();
  };

  // 重置搜索
  const handleResetSearch = () => {
    setSearchKeyword('');
    setSelectedResource('');
    setSelectedAction('');
    setCurrent(1);
  };

  // 创建权限
  const handleCreate = () => {
    setEditingPermission(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 编辑权限
  const handleEdit = (permission: Permission) => {
    setEditingPermission(permission);
    form.setFieldsValue({
      name: permission.name,
      displayName: permission.displayName,
      description: permission.description,
      resource: permission.resource,
      action: permission.action,
    });
    setModalVisible(true);
  };

  // 删除权限
  const handleDelete = async (permissionId: string) => {
    try {
      await permissionApi.deletePermission(permissionId);
      message.success('权限删除成功');
      fetchPermissions();
    } catch (error: any) {
      message.error(error.response?.data?.message || '权限删除失败');
    }
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    try {
      if (editingPermission) {
        // 更新权限
        const updateData: UpdatePermissionRequest = {
          name: values.name,
          displayName: values.displayName,
          description: values.description,
          resource: values.resource,
          action: values.action,
        };
        await permissionApi.updatePermission(editingPermission.id, updateData);
        message.success('权限更新成功');
      } else {
        // 创建权限
        const createData: CreatePermissionRequest = {
          name: values.name,
          displayName: values.displayName,
          description: values.description,
          resource: values.resource,
          action: values.action,
        };
        await permissionApi.createPermission(createData);
        message.success('权限创建成功');
      }
      setModalVisible(false);
      fetchPermissions();
    } catch (error: any) {
      message.error(error.response?.data?.message || '操作失败');
    }
  };

  // 根据资源和操作自动生成名称和显示名称
  const handleResourceActionChange = () => {
    const resource = form.getFieldValue('resource');
    const action = form.getFieldValue('action');
    
    if (resource && action) {
      // 自动生成权限名称
      const name = `${resource}:${action}`;
      form.setFieldValue('name', name);
      
      // 自动生成显示名称
      const resourceLabel = resourceOptions.find(opt => opt.value === resource)?.label || resource;
      const actionLabel = actionOptions.find(opt => opt.value === action)?.label || action;
      const displayName = `${resourceLabel}:${actionLabel}`;
      form.setFieldValue('displayName', displayName);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '权限名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => (
        <Space>
          <KeyOutlined />
          <code>{text}</code>
        </Space>
      ),
    },
    {
      title: '显示名称',
      dataIndex: 'displayName',
      key: 'displayName',
    },
    {
      title: '资源',
      dataIndex: 'resource',
      key: 'resource',
      render: (text: string) => {
        const resourceLabel = resourceOptions.find(opt => opt.value === text)?.label || text;
        return <Tag color="blue">{resourceLabel}</Tag>;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      render: (text: string) => {
        const actionLabel = actionOptions.find(opt => opt.value === text)?.label || text;
        const colorMap: { [key: string]: string } = {
          read: 'green',
          write: 'orange',
          delete: 'red',
          execute: 'purple',
          manage: 'gold',
        };
        return <Tag color={colorMap[text] || 'default'}>{actionLabel}</Tag>;
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Permission) => (
        <Space>
          {canEdit && (
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
          )}
          {canDelete && (
            <Popconfirm
              title="确定要删除这个权限吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button danger size="small" icon={<DeleteOutlined />}>
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card title="权限管理" style={{ marginBottom: 16 }}>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Input
              placeholder="搜索权限名称或描述"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择资源"
              value={selectedResource}
              onChange={setSelectedResource}
              allowClear
              style={{ width: '100%' }}
            >
              {resourceOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择操作"
              value={selectedAction}
              onChange={setSelectedAction}
              allowClear
              style={{ width: '100%' }}
            >
              {actionOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={10}>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleResetSearch}>重置</Button>
              {canCreate && (
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                  新建权限
                </Button>
              )}
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={permissions}
          rowKey="id"
          loading={loading}
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, size) => {
              setCurrent(page);
              setPageSize(size || 10);
            },
          }}
        />
      </Card>

      {/* 创建/编辑权限模态框 */}
      <Modal
        title={editingPermission ? '编辑权限' : '新建权限'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="资源"
                name="resource"
                rules={[{ required: true, message: '请选择资源' }]}
              >
                <Select
                  placeholder="请选择资源"
                  onChange={handleResourceActionChange}
                >
                  {resourceOptions.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="操作"
                name="action"
                rules={[{ required: true, message: '请选择操作' }]}
              >
                <Select
                  placeholder="请选择操作"
                  onChange={handleResourceActionChange}
                >
                  {actionOptions.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="权限名称"
            name="name"
            rules={[{ required: true, message: '请输入权限名称' }]}
          >
            <Input placeholder="权限名称（建议格式：resource:action）" />
          </Form.Item>

          <Form.Item
            label="显示名称"
            name="displayName"
            rules={[{ required: true, message: '请输入显示名称' }]}
          >
            <Input placeholder="权限显示名称（中文）" />
          </Form.Item>

          <Form.Item label="描述" name="description">
            <TextArea rows={3} placeholder="请输入权限描述" />
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                {editingPermission ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PermissionManagement; 