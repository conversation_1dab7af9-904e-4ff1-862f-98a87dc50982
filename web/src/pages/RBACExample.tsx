import React from 'react';
import { Card, Button, Space, Alert, Divider, Typography, Tag } from 'antd';
import { UserOutlined, EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import PermissionGuard from '../components/PermissionGuard';
import { useUser } from '../contexts/UserContext';

const { Title, Paragraph, Text } = Typography;

const RBACExample: React.FC = () => {
  const { user, permissions, hasPermission } = useUser();

  // 检查是否有系统管理相关权限
  if (!hasPermission('users:read') && !hasPermission('roles:read') && !hasPermission('permissions:read')) {
    return (
      <Card title="权限不足">
        <Alert
          message="访问受限"
          description="您没有权限访问此页面。需要用户管理、角色管理或权限管理中的任一权限。"
          type="warning"
          showIcon
        />
      </Card>
    );
  }

  return (
    <div>
      <Card title="RBAC权限控制使用示例" style={{ marginBottom: 16 }}>
        <Alert
          message="权限控制示例"
          description="本页面展示了如何在React组件中使用RBAC权限控制，根据用户权限动态显示/隐藏内容。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Title level={4}>当前用户信息</Title>
        <Paragraph>
          <Text strong>用户名:</Text> {user?.username}<br />
          <Text strong>角色:</Text> {user?.roles.map(role => role.displayName).join(', ')}<br />
          <Text strong>权限数量:</Text> {permissions.length}
        </Paragraph>

        <Title level={4}>用户权限列表</Title>
        <Space wrap>
          {permissions.map(permission => (
            <Tag key={permission} color="blue">
              {permission}
            </Tag>
          ))}
        </Space>

        <Divider />

        <Title level={4}>权限控制示例</Title>

        {/* 用户管理权限示例 */}
        <Card title="用户管理操作" size="small" style={{ marginBottom: 16 }}>
          <Space wrap>
            <PermissionGuard permission="users:read">
              <Button icon={<UserOutlined />}>查看用户</Button>
            </PermissionGuard>
            
            <PermissionGuard permission="users:write">
              <Button type="primary" icon={<PlusOutlined />}>创建用户</Button>
            </PermissionGuard>
            
            <PermissionGuard permission="users:write">
              <Button icon={<EditOutlined />}>编辑用户</Button>
            </PermissionGuard>
            
            <PermissionGuard permission="users:delete">
              <Button danger icon={<DeleteOutlined />}>删除用户</Button>
            </PermissionGuard>
            
            <PermissionGuard 
              permission="users:manage"
              fallback={<Text type="secondary">需要用户管理权限</Text>}
            >
              <Button type="dashed">高级用户管理</Button>
            </PermissionGuard>
          </Space>
        </Card>

        {/* 角色管理权限示例 */}
        <Card title="角色管理操作" size="small" style={{ marginBottom: 16 }}>
          <Space wrap>
            <PermissionGuard permission="roles:read">
              <Button>查看角色</Button>
            </PermissionGuard>
            
            <PermissionGuard permission="roles:write">
              <Button type="primary">创建角色</Button>
            </PermissionGuard>
            
            <PermissionGuard permission="roles:delete">
              <Button danger>删除角色</Button>
            </PermissionGuard>
            
            <PermissionGuard 
              permission="roles:manage"
              fallback={<Text type="secondary">需要角色管理权限</Text>}
            >
              <Button type="dashed">高级角色管理</Button>
            </PermissionGuard>
          </Space>
        </Card>

        {/* 权限管理权限示例 */}
        <Card title="权限管理操作" size="small" style={{ marginBottom: 16 }}>
          <Space wrap>
            <PermissionGuard permission="permissions:read">
              <Button>查看权限</Button>
            </PermissionGuard>
            
            <PermissionGuard permission="permissions:write">
              <Button type="primary">创建权限</Button>
            </PermissionGuard>
            
            <PermissionGuard permission="permissions:delete">
              <Button danger>删除权限</Button>
            </PermissionGuard>
            
            <PermissionGuard 
              permission="permissions:manage"
              fallback={<Text type="secondary">需要权限管理权限</Text>}
            >
              <Button type="dashed">高级权限管理</Button>
            </PermissionGuard>
          </Space>
        </Card>

        {/* 角色基础权限示例 */}
        <Card title="角色权限示例" size="small" style={{ marginBottom: 16 }}>
          <Space wrap>
            <PermissionGuard role="admin">
              <Button type="primary">管理员专用功能</Button>
            </PermissionGuard>
            
            <PermissionGuard role="user">
              <Button>普通用户功能</Button>
            </PermissionGuard>
            
            <PermissionGuard role="viewer">
              <Button>查看者功能</Button>
            </PermissionGuard>
            
            <PermissionGuard 
              role="super_admin"
              fallback={<Text type="secondary">需要超级管理员角色</Text>}
            >
              <Button danger>超级管理员功能</Button>
            </PermissionGuard>
          </Space>
        </Card>

        {/* 复合权限示例 */}
        <Card title="复合权限示例" size="small">
          <Space direction="vertical">
            <div>
              <Text strong>同时需要角色和权限（requireAll=true）:</Text>
              <PermissionGuard 
                role="admin" 
                permission="system:manage" 
                requireAll={true}
                fallback={<Text type="secondary">需要admin角色且有system:manage权限</Text>}
              >
                <Button type="primary" style={{ marginLeft: 8 }}>系统管理</Button>
              </PermissionGuard>
            </div>
            
            <div>
              <Text strong>满足角色或权限其中之一（requireAll=false）:</Text>
              <PermissionGuard 
                role="admin" 
                permission="flows:manage" 
                requireAll={false}
                fallback={<Text type="secondary">需要admin角色或flows:manage权限</Text>}
              >
                <Button style={{ marginLeft: 8 }}>流程管理</Button>
              </PermissionGuard>
            </div>
          </Space>
        </Card>
      </Card>

      <Card title="代码示例">
        <Typography>
          <Title level={5}>基本用法</Title>
          <Paragraph>
            <pre style={{ background: '#f6f8fa', padding: '12px', borderRadius: '4px' }}>
{`// 基于权限控制
<PermissionGuard permission="users:write">
  <Button type="primary">创建用户</Button>
</PermissionGuard>

// 基于角色控制
<PermissionGuard role="admin">
  <Button type="primary">管理员功能</Button>
</PermissionGuard>

// 带回退内容
<PermissionGuard 
  permission="users:delete"
  fallback={<Text type="secondary">无权限</Text>}
>
  <Button danger>删除用户</Button>
</PermissionGuard>

// 复合权限控制
<PermissionGuard 
  role="admin" 
  permission="system:manage" 
  requireAll={true}
>
  <Button>系统管理</Button>
</PermissionGuard>`}
            </pre>
          </Paragraph>

          <Title level={5}>Hook用法</Title>
          <Paragraph>
            <pre style={{ background: '#f6f8fa', padding: '12px', borderRadius: '4px' }}>
{`import { usePermission, useRole } from '../contexts/UserContext';

function MyComponent() {
  const canEdit = usePermission('users:write');
  const isAdmin = useRole('admin');
  
  return (
    <div>
      {canEdit && <Button>编辑</Button>}
      {isAdmin && <Button>管理员功能</Button>}
    </div>
  );
}`}
            </pre>
          </Paragraph>
        </Typography>
      </Card>
    </div>
  );
};

export default RBACExample; 