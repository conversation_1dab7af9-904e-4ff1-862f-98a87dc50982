import React, { useEffect, useState, useRef } from 'react';
import { Card, Descriptions, Tag, Button, Space, message, Breadcrumb, Tabs, Spin, Modal } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import LogicFlow from '@logicflow/core';
import '@logicflow/core/dist/style/index.css';
import { executionApi } from '../services/api';
import type { FlowExecutionDetail, TriggerType, ExecutionStatus } from '../types/execution';
import { TriggerTypeDisplayNames, ExecutionStatusDisplayNames, ExecutionStatusColors } from '../types/execution';
import { CustomHtmlPresetNodeDefinition, presetNodeStyles } from '../components/CustomHtmlPresetNode';



const ExecutionDetail: React.FC = () => {
  const [execution, setExecution] = useState<FlowExecutionDetail | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('flow');
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [selectedNodeConfig, setSelectedNodeConfig] = useState<any>(null);
  const [selectedNodeName, setSelectedNodeName] = useState<string>('');
  const navigate = useNavigate();
  const { executionId } = useParams<{ executionId: string }>();
  const containerRef = useRef<HTMLDivElement>(null);
  const lfRef = useRef<LogicFlow | null>(null);

  const fetchExecution = async () => {
    if (!executionId) return;
    
    setLoading(true);
    try {
      const response = await executionApi.getExecution(executionId);
      setExecution(response.data.data);
    } catch (error) {
      message.error('获取执行记录详情失败');
      console.error('Failed to fetch execution detail:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchExecution();
  }, [executionId]);

  const initLogicFlow = () => {
    if (!containerRef.current) return;

    console.log('Initializing LogicFlow for ExecutionDetail...');

    // 清除之前的实例
    if (lfRef.current && typeof lfRef.current.destroy === 'function') {
      try {
        lfRef.current.destroy();
      } catch (error) {
        console.warn('Failed to destroy LogicFlow instance:', error);
      }
    }

    // 创建新实例
    const lf = new LogicFlow({
      container: containerRef.current,
      width: containerRef.current.offsetWidth || 800,
      height: containerRef.current.offsetHeight || 600,
      grid: {
        size: 20,
        visible: true,
      },
      // 禁用编辑功能
      editable: false,
      keyboard: {
        enabled: false,
      },
      style: {
        rect: {
          rx: 5,
          ry: 5,
          strokeWidth: 2,
        },
        circle: {
          fill: '#f5f5f5',
          stroke: '#666',
        },
        diamond: {
          fill: '#ffe6cc',
          stroke: '#d79b00',
        },
        text: {
          color: '#333',
          fontSize: 12,
        }
      }
    });

    console.log('LogicFlow instance created:', lf);

    // 注册自定义 HTML 预置节点
    lf.register(CustomHtmlPresetNodeDefinition);

    // 添加自定义样式
    const styleElement = document.createElement('style');
    styleElement.textContent = presetNodeStyles;
    document.head.appendChild(styleElement);

    lfRef.current = lf;

    // 监听节点配置查看事件
    lf.on('custom:edit-preset-node', ({ nodeId }: { nodeId: string }) => {
      console.log('View node config triggered for node:', nodeId);
      const nodeData = lf.getNodeModelById(nodeId);
      if (nodeData && nodeData.properties?.isPresetNode) {
        handleNodeConfigView(nodeData);
      }
    });

    // 渲染流程图
    if (execution && execution.flowSnapshot) {
      console.log('Rendering flow snapshot:', execution.flowSnapshot);
      lf.render(execution.flowSnapshot);
      // 调整视图以适应所有节点
      setTimeout(() => {
        lf.fitView();
      }, 100);
    } else {
      console.log('No flow snapshot available');
      // 渲染空画布
      lf.render({
        nodes: [],
        edges: []
      });
    }
  };

  useEffect(() => {
    if (execution && containerRef.current && activeTab === 'flow') {
      // 延迟初始化，确保容器已经渲染
      setTimeout(() => {
        initLogicFlow();
      }, 100);
    }

    return () => {
      if (lfRef.current && typeof lfRef.current.destroy === 'function') {
        try {
          lfRef.current.destroy();
        } catch (error) {
          console.warn('Failed to destroy LogicFlow instance on cleanup:', error);
        }
        lfRef.current = null;
      }
    };
  }, [execution, activeTab]);

  // 处理节点配置查看
  const handleNodeConfigView = (nodeData: any) => {
    console.log('Viewing node config:', nodeData);

    if (nodeData.properties?.isPresetNode) {
      const presetNodeName = nodeData.properties.presetNodeName || '未知节点';
      const formData = nodeData.properties.formData || {};

      setSelectedNodeName(presetNodeName);
      setSelectedNodeConfig(formData);
      setConfigModalVisible(true);
    }
  };

  const formatDuration = (duration: number) => {
    if (duration < 1000) {
      return `${duration}ms`;
    } else if (duration < 60000) {
      return `${(duration / 1000).toFixed(1)}s`;
    } else {
      return `${(duration / 60000).toFixed(1)}min`;
    }
  };

  const getTriggerTypeDisplay = (triggerType: TriggerType) => {
    const colors = {
      manual: 'blue',
      cron: 'green',
      http: 'orange',
    };
    return (
      <Tag color={colors[triggerType]}>
        {TriggerTypeDisplayNames[triggerType]}
      </Tag>
    );
  };

  const getStatusDisplay = (status: ExecutionStatus) => {
    return (
      <Tag color={ExecutionStatusColors[status]}>
        {ExecutionStatusDisplayNames[status]}
      </Tag>
    );
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  if (!execution) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          执行记录不存在
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <Breadcrumb>
          <Breadcrumb.Item>
            <Button
              type="link"
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/flows')}
              style={{
                padding: 0,
                height: 'auto',
                lineHeight: 'inherit',
                display: 'inline-flex',
                alignItems: 'center'
              }}
            >
              流程列表
            </Button>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <Button
              type="link"
              onClick={() => navigate(`/flow/${execution.flowId}/executions`)}
              style={{
                padding: 0,
                height: 'auto',
                lineHeight: 'inherit',
                display: 'inline-flex',
                alignItems: 'center'
              }}
            >
              {execution.flowName}
            </Button>
          </Breadcrumb.Item>
          <Breadcrumb.Item>执行记录详情</Breadcrumb.Item>
        </Breadcrumb>
      }
      extra={
        <Space>
          <Button 
            onClick={() => navigate(`/flow/${execution.flowId}/executions`)}
          >
            返回执行记录
          </Button>
          <Button 
            type="primary"
            onClick={() => navigate(`/flow/${execution.flowId}`)}
          >
            编辑流程
          </Button>
        </Space>
      }
    >
      <Descriptions title="执行信息" bordered column={2} style={{ marginBottom: 24 }}>
        <Descriptions.Item label="执行ID" span={2}>
          <span style={{ fontFamily: 'monospace' }}>{execution.id}</span>
        </Descriptions.Item>
        <Descriptions.Item label="流程名称">{execution.flowName}</Descriptions.Item>
        <Descriptions.Item label="触发类型">{getTriggerTypeDisplay(execution.triggerType)}</Descriptions.Item>
        <Descriptions.Item label="触发来源">{execution.triggerSource}</Descriptions.Item>
        <Descriptions.Item label="执行状态">{getStatusDisplay(execution.status)}</Descriptions.Item>
        <Descriptions.Item label="开始时间">
          {new Date(execution.startTime).toLocaleString('zh-CN')}
        </Descriptions.Item>
        <Descriptions.Item label="结束时间">
          {execution.endTime ? new Date(execution.endTime).toLocaleString('zh-CN') : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="执行时长">
          {execution.duration > 0 ? formatDuration(execution.duration) : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="错误信息" span={2}>
          {execution.errorMessage || '-'}
        </Descriptions.Item>
      </Descriptions>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'flow',
            label: '流程图',
            children: (
              <div
                ref={containerRef}
                style={{
                  width: '100%',
                  height: '600px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '6px',
                }}
              />
            ),
          },
          {
            key: 'log',
            label: '执行日志',
            children: (
              <div>
                <Card size="small" title="执行摘要" style={{ marginBottom: 16 }}>
                  <Descriptions size="small" column={4}>
                    <Descriptions.Item label="总步骤">{execution.executionLog.summary.totalSteps}</Descriptions.Item>
                    <Descriptions.Item label="成功步骤">{execution.executionLog.summary.successSteps}</Descriptions.Item>
                    <Descriptions.Item label="失败步骤">{execution.executionLog.summary.failedSteps}</Descriptions.Item>
                    <Descriptions.Item label="跳过步骤">{execution.executionLog.summary.skippedSteps}</Descriptions.Item>
                    <Descriptions.Item label="执行路径" span={4}>
                      {execution.executionLog.summary.executionPath}
                    </Descriptions.Item>
                  </Descriptions>
                </Card>

                <Card size="small" title="执行步骤">
                  {execution.executionLog.steps.map((step, index) => (
                    <Card
                      key={step.stepId}
                      size="small"
                      type="inner"
                      title={`步骤 ${index + 1}: ${step.nodeName}`}
                      extra={
                        <Tag color={step.status === 'completed' ? 'green' : step.status === 'failed' ? 'red' : 'blue'}>
                          {step.status}
                        </Tag>
                      }
                      style={{ marginBottom: 8 }}
                    >
                      <Descriptions size="small" column={3}>
                        <Descriptions.Item label="节点ID">{step.nodeId}</Descriptions.Item>
                        <Descriptions.Item label="节点类型">{step.nodeType}</Descriptions.Item>
                        <Descriptions.Item label="执行时长">
                          {step.duration > 0 ? formatDuration(step.duration) : '-'}
                        </Descriptions.Item>
                      </Descriptions>

                      {step.errorMessage && (
                        <div style={{ marginTop: 8 }}>
                          <strong>错误信息：</strong>
                          <div style={{
                            background: '#fff2f0',
                            border: '1px solid #ffccc7',
                            padding: '8px',
                            borderRadius: '4px',
                            marginTop: '4px',
                            fontFamily: 'monospace',
                            fontSize: '12px'
                          }}>
                            {step.errorMessage}
                          </div>
                        </div>
                      )}

                      {step.flowData && Object.keys(step.flowData).length > 0 && (
                        <div style={{ marginTop: 8 }}>
                          <strong>流程数据：</strong>
                          <pre style={{
                            background: '#f6f8fa',
                            border: '1px solid #e1e4e8',
                            padding: '8px',
                            borderRadius: '4px',
                            marginTop: '4px',
                            fontSize: '12px',
                            maxHeight: '200px',
                            overflow: 'auto'
                          }}>
                            {JSON.stringify(step.flowData, null, 2)}
                          </pre>
                        </div>
                      )}
                    </Card>
                  ))}
                </Card>
              </div>
            ),
          },
          {
            key: 'input',
            label: '输入数据',
            children: (
              <pre style={{
                background: '#f6f8fa',
                border: '1px solid #e1e4e8',
                padding: '16px',
                borderRadius: '6px',
                fontSize: '12px',
                maxHeight: '500px',
                overflow: 'auto'
              }}>
                {JSON.stringify(execution.inputData, null, 2)}
              </pre>
            ),
          },
          {
            key: 'output',
            label: '输出数据',
            children: (
              <pre style={{
                background: '#f6f8fa',
                border: '1px solid #e1e4e8',
                padding: '16px',
                borderRadius: '6px',
                fontSize: '12px',
                maxHeight: '500px',
                overflow: 'auto'
              }}>
                {JSON.stringify(execution.outputData, null, 2)}
              </pre>
            ),
          },
        ]}
      />

      {/* 节点配置查看弹窗 */}
      <Modal
        title={`节点配置详情 - ${selectedNodeName}`}
        open={configModalVisible}
        onCancel={() => {
          setConfigModalVisible(false);
          setSelectedNodeConfig(null);
          setSelectedNodeName('');
        }}
        footer={[
          <Button key="close" onClick={() => {
            setConfigModalVisible(false);
            setSelectedNodeConfig(null);
            setSelectedNodeName('');
          }}>
            关闭
          </Button>
        ]}
        width={600}
        zIndex={1100}
      >
        <div style={{ marginBottom: 16 }}>
          <div style={{
            color: '#666',
            fontSize: '14px',
            marginBottom: 8
          }}>
            节点配置参数（只读）：
          </div>
          <pre style={{
            background: '#f5f5f5',
            padding: '12px',
            borderRadius: '6px',
            fontSize: '12px',
            maxHeight: '400px',
            overflow: 'auto',
            margin: 0,
            border: '1px solid #d9d9d9'
          }}>
            {selectedNodeConfig ? JSON.stringify(selectedNodeConfig, null, 2) : '暂无配置'}
          </pre>
        </div>
      </Modal>
    </Card>
  );
};

export default ExecutionDetail;
