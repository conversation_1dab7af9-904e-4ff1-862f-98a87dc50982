import React, { useEffect, useState } from 'react';
import { Table, Button, Card, message, Tag, Breadcrumb } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import PermissionGuard from '../components/PermissionGuard';
import { executionApi } from '../services/api';
import type { FlowExecutionListItem, TriggerType, ExecutionStatus } from '../types/execution';
import { TriggerTypeDisplayNames, ExecutionStatusDisplayNames, ExecutionStatusColors } from '../types/execution';

const ExecutionList: React.FC = () => {
  const [executions, setExecutions] = useState<FlowExecutionListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [flowName, setFlowName] = useState('');
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const navigate = useNavigate();
  const { flowId } = useParams<{ flowId: string }>();

  const fetchExecutions = async (p: number = page, ps: number = pageSize) => {
    if (!flowId) return;
    
    setLoading(true);
    try {
      const response = await executionApi.getFlowExecutions(flowId, { page: p, pageSize: ps });
      const data = response.data.data;
      setExecutions(data.data || []);
      setTotal(data.total || 0);
      setPage(data.page || p);
      setPageSize(data.pageSize || ps);
      
      // 从第一条记录获取流程名称
      if (data.data && data.data.length > 0) {
        setFlowName(data.data[0].flowName);
      }
    } catch (error) {
      message.error('获取执行记录失败');
      console.error('Failed to fetch executions:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!flowId) return;
    // 流程变更时重置到第1页
    setPage(1);
    fetchExecutions(1, pageSize);
  }, [flowId]);

  const handleTableChange = (pagination: any) => {
    const nextPage = pagination.current || 1;
    const nextPageSize = pagination.pageSize || pageSize;
    setPage(nextPage);
    setPageSize(nextPageSize);
    fetchExecutions(nextPage, nextPageSize);
  };

  const formatDuration = (duration: number) => {
    if (duration < 1000) {
      return `${duration}ms`;
    } else if (duration < 60000) {
      return `${(duration / 1000).toFixed(1)}s`;
    } else {
      return `${(duration / 60000).toFixed(1)}min`;
    }
  };

  const getTriggerTypeDisplay = (triggerType: TriggerType) => {
    const colors = {
      manual: 'blue',
      cron: 'green',
      http: 'orange',
    };
    return (
      <Tag color={colors[triggerType]}>
        {TriggerTypeDisplayNames[triggerType]}
      </Tag>
    );
  };

  const getStatusDisplay = (status: ExecutionStatus) => {
    return (
      <Tag color={ExecutionStatusColors[status]}>
        {ExecutionStatusDisplayNames[status]}
      </Tag>
    );
  };

  const columns = [
    {
      title: '执行ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (id: string) => (
        <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>
          {id.substring(0, 8)}...
        </span>
      ),
    },
    {
      title: '触发类型',
      dataIndex: 'triggerType',
      key: 'triggerType',
      width: 100,
      render: (triggerType: TriggerType) => getTriggerTypeDisplay(triggerType),
    },
    {
      title: '触发来源',
      dataIndex: 'triggerSource',
      key: 'triggerSource',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: ExecutionStatus) => getStatusDisplay(status),
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 160,
      render: (startTime: string) => {
        const date = new Date(startTime);
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        }).replace(/\//g, '-');
      },
    },
    {
      title: '执行时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (duration: number) => duration > 0 ? formatDuration(duration) : '-',
    },
    {
      title: '错误信息',
      dataIndex: 'errorMessage',
      key: 'errorMessage',
      ellipsis: true,
      render: (errorMessage: string) => errorMessage || '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_: any, record: FlowExecutionListItem) => (
        <PermissionGuard permission="executions:read">
          <Button
            type="primary"
            size="small"
            onClick={() => navigate(`/execution/${record.id}`)}
          >
            查看详情
          </Button>
        </PermissionGuard>
      ),
    },
  ];

  return (
    <Card
      title={
        <Breadcrumb>
          <Breadcrumb.Item>
            <Button
              type="link"
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/flows')}
              style={{
                padding: 0,
                height: 'auto',
                lineHeight: 'inherit',
                display: 'inline-flex',
                alignItems: 'center'
              }}
            >
              流程列表
            </Button>
          </Breadcrumb.Item>
          <Breadcrumb.Item>{flowName || '流程'}</Breadcrumb.Item>
          <Breadcrumb.Item>执行记录</Breadcrumb.Item>
        </Breadcrumb>
      }
      extra={
        <PermissionGuard permission="flows:write">
          <Button 
            onClick={() => navigate(`/flow/${flowId}`)}
          >
            编辑流程
          </Button>
        </PermissionGuard>
      }
    >
      <Table
        columns={columns}
        dataSource={executions}
        rowKey="id"
        loading={loading}
        pagination={{
          current: page,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (t, range) => `第 ${range[0]}-${range[1]} 条，共 ${t} 条`,
        }}
        onChange={handleTableChange}
      />
    </Card>
  );
};

export default ExecutionList;
