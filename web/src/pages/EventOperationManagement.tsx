import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Spin,
  message,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Popconfirm,
  Row,
  Col,
  DatePicker,
  Tag,
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { eventOperationService, eventCategoryService, userService } from '../services/api';
import dayjs from 'dayjs';

// const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface EventOperation {
  id: string;
  occurredAt: string;
  occurredCondition: string;
  categoryId: string;
  category?: {
    id: string;
    name: string;
  };
  description: string;
  ownerId: string;
  owner?: {
    id: string;
    username: string;
    email: string;
  };
  relatedEmployeeIds: string;
  relatedEmployees?: any[];
  processType: string;
  processDescription: string;
  processCompletedAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface EventCategory {
  id: string;
  name: string;
}

interface User {
  id: string;
  username: string;
  email: string;
}

interface Employee {
  user_id: string;
  name: string;
  email: string;
  username: string;
}

const EventOperationManagement: React.FC = () => {
  const [operations, setOperations] = useState<EventOperation[]>([]);
  const [categories, setCategories] = useState<EventCategory[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 筛选条件
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState('');
  const [selectedOwnerId, setSelectedOwnerId] = useState('');
  const [selectedProcessType, setSelectedProcessType] = useState('');

  // 模态框状态
  const [modalVisible, setModalVisible] = useState(false);
  const [editingOperation, setEditingOperation] = useState<EventOperation | null>(null);
  const [form] = Form.useForm();

  // 选择状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  // 员工搜索状态
  const [employeeSearchResults, setEmployeeSearchResults] = useState<Employee[]>([]);
  const [employeeSearchLoading, setEmployeeSearchLoading] = useState(false);
  
  // 防抖定时器引用
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 获取事件运营列表
  const fetchOperations = async () => {
    setLoading(true);
    try {
      const params: any = {
        page: current,
        pageSize,
      };

      if (searchKeyword) params.keyword = searchKeyword;
      if (selectedCategoryId) params.categoryId = selectedCategoryId;
      if (selectedOwnerId) params.ownerId = selectedOwnerId;
      if (selectedProcessType) params.processType = selectedProcessType;

      const response = await eventOperationService.getOperations(params);
      console.log('API Response:', response);
      setOperations(response.data.data.data || []);
      setTotal(response.data.data.total || 0);
    } catch (error) {
      console.error('获取事件运营失败:', error);
      message.error('获取事件运营列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await eventCategoryService.getCategoryOptions();
      console.log('Categories API Response:', response);
      setCategories(response.data.data.options || []);
    } catch (error) {
      console.error('获取分类列表失败:', error);
      message.error('获取分类列表失败');
    }
  };

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      const response = await userService.getUsers({ page: 1, pageSize: 100 });
      console.log('Users API Response:', response);
      console.log('Users data:', response.data);
      setUsers(response.data.data.list || []);
    } catch (error: any) {
      console.error('获取用户列表失败:', error);
      console.error('Error response:', error.response);
      if (error.response?.status === 403) {
        message.error('没有权限获取用户列表');
      } else {
        message.error('获取用户列表失败');
      }
    }
  };

  useEffect(() => {
    fetchOperations();
  }, [current, pageSize, searchKeyword, selectedCategoryId, selectedOwnerId, selectedProcessType]);

  useEffect(() => {
    fetchCategories();
    fetchUsers();
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // 搜索员工（带防抖）
  const searchEmployees = async (keyword: string) => {
    if (!keyword.trim()) {
      setEmployeeSearchResults([]);
      return;
    }

    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 设置新的防抖定时器（500ms延迟）
    searchTimeoutRef.current = setTimeout(async () => {
      setEmployeeSearchLoading(true);
      try {
        const response = await eventOperationService.searchEmployees(keyword, 20);
        console.log('Employee search response:', response);
        
        // 确保数据路径正确 - 根据后端响应格式调整
        const employees = response.data?.data?.employees || response.data?.employees || [];
        console.log('Final employees array:', employees);
        
        setEmployeeSearchResults(employees);
      } catch (error) {
        console.error('搜索员工失败:', error);
        message.error('搜索员工失败');
        setEmployeeSearchResults([]);
      } finally {
        setEmployeeSearchLoading(false);
      }
    }, 500);
  };

  // 处理类型映射
  const processTypeMap: { [key: string]: { text: string; color: string } } = {
    whitelist: { text: '加白', color: 'blue' },
    false_alarm: { text: '误报', color: 'orange' },
    ignore: { text: '忽略', color: 'gray' },
    pending: { text: '暂不处理', color: 'yellow' },
    completed: { text: '已完成', color: 'green' },
    unprocessed: { text: '未处理', color: 'red' },
  };

  // 搜索处理
  const handleSearch = () => {
    setCurrent(1);
    fetchOperations();
  };

  // 重置搜索
  const handleResetSearch = () => {
    setSearchKeyword('');
    setSelectedCategoryId('');
    setSelectedOwnerId('');
    setSelectedProcessType('');
    setCurrent(1);
  };

  // 新建运营
  const handleCreate = () => {
    setEditingOperation(null);
    form.resetFields();
    setEmployeeSearchResults([]); // 清空员工搜索结果
    setModalVisible(true);
  };

  // 编辑运营
  const handleEdit = (operation: EventOperation) => {
    setEditingOperation(operation);

    // 解析关联员工IDs
    let relatedEmployeeIds: string[] = [];
    if (operation.relatedEmployeeIds) {
      try {
        relatedEmployeeIds = JSON.parse(operation.relatedEmployeeIds);
      } catch (error) {
        console.error('解析关联员工IDs失败:', error);
      }
    }

    form.setFieldsValue({
      occurredAt: operation.occurredAt ? dayjs(operation.occurredAt) : null,
      occurredCondition: operation.occurredCondition,
      categoryId: operation.categoryId,
      description: operation.description,
      ownerId: operation.ownerId,
      relatedEmployeeIds: relatedEmployeeIds,
      processType: operation.processType,
      processDescription: operation.processDescription,
      processCompletedAt: operation.processCompletedAt ? dayjs(operation.processCompletedAt) : null,
    });

    // 如果有关联员工，设置到搜索结果中以便显示
    if (operation.relatedEmployees && operation.relatedEmployees.length > 0) {
      setEmployeeSearchResults(operation.relatedEmployees);
    } else {
      setEmployeeSearchResults([]);
    }

    setModalVisible(true);
  };

  // 删除运营
  const handleDelete = async (id: string) => {
    try {
      await eventOperationService.deleteOperation(id);
      message.success('删除事件运营成功');
      fetchOperations();
    } catch (error: any) {
      message.error(error.response?.data?.message || '删除失败');
    }
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    try {
      const submitData = {
        ...values,
        occurredAt: values.occurredAt?.toISOString(),
        processCompletedAt: values.processCompletedAt?.toISOString(),
      };

      if (editingOperation) {
        await eventOperationService.updateOperation(editingOperation.id, submitData);
        message.success('更新事件运营成功');
      } else {
        await eventOperationService.createOperation(submitData);
        message.success('创建事件运营成功');
      }
      setModalVisible(false);
      fetchOperations();
    } catch (error: any) {
      message.error(error.response?.data?.message || '操作失败');
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录');
      return;
    }

    try {
      await eventOperationService.batchDeleteOperations(selectedRowKeys);
      message.success('批量删除成功');
      setSelectedRowKeys([]);
      fetchOperations();
    } catch (error: any) {
      message.error(error.response?.data?.message || '批量删除失败');
    }
  };

  // 选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys as string[]);
    },
    onSelectAll: (selected: boolean, selectedRows: EventOperation[], changeRows: EventOperation[]) => {
      console.log('Select all:', selected, selectedRows, changeRows);
    },
    onSelect: (record: EventOperation, selected: boolean, selectedRows: EventOperation[]) => {
      console.log('Select:', record, selected, selectedRows);
    },
  };

  const columns = [
    {
      title: '发生时间',
      dataIndex: 'occurredAt',
      key: 'occurredAt',
      render: (text: string) => new Date(text).toLocaleString(),
      width: 150,
    },
    {
      title: '发生条件',
      dataIndex: 'occurredCondition',
      key: 'occurredCondition',
      ellipsis: true,
      width: 200,
    },
    {
      title: '事件分类',
      dataIndex: 'category',
      key: 'category',
      render: (category: any) => category?.name || '-',
      width: 120,
    },
    {
      title: '事件描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      width: 200,
    },
    {
      title: '负责人',
      dataIndex: 'owner',
      key: 'owner',
      render: (owner: any) => owner?.username || '-',
      width: 100,
    },
    {
      title: '关联员工',
      dataIndex: 'relatedEmployees',
      key: 'relatedEmployees',
      render: (employees: any[]) => {
        if (!employees || employees.length === 0) return '-';
        return employees.map(emp => emp.name).join(', ');
      },
      width: 150,
    },
    {
      title: '处理状态',
      dataIndex: 'processType',
      key: 'processType',
      render: (processType: string) => {
        const typeInfo = processTypeMap[processType] || { text: processType, color: 'default' };
        return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>;
      },
      width: 100,
    },
    {
      title: '处理描述',
      dataIndex: 'processDescription',
      key: 'processDescription',
      ellipsis: true,
      width: 200,
    },
    {
      title: '处理完成时间',
      dataIndex: 'processCompletedAt',
      key: 'processCompletedAt',
      render: (text: string) => text ? new Date(text).toLocaleString() : '-',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right' as const,
      width: 150,
      render: (_: any, record: EventOperation) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个运营记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button danger size="small" icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card title="事件运营管理">
        {/* 搜索筛选区域 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={5}>
            <Input
              placeholder="搜索关键词"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择分类"
              value={selectedCategoryId}
              onChange={setSelectedCategoryId}
              allowClear
              style={{ width: '100%' }}
            >
              {categories.map((category) => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择负责人"
              value={selectedOwnerId}
              onChange={setSelectedOwnerId}
              allowClear
              style={{ width: '100%' }}
            >
              {users.map((user) => (
                <Option key={user.id} value={user.id}>
                  {user.username}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="处理状态"
              value={selectedProcessType}
              onChange={setSelectedProcessType}
              allowClear
              style={{ width: '100%' }}
            >
              {Object.entries(processTypeMap).map(([key, value]) => (
                <Option key={key} value={key}>
                  {value.text}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleResetSearch}>
                重置
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                新建运营
              </Button>
            </Space>
          </Col>
        </Row>

        {selectedRowKeys.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Space>
              <span>已选择 {selectedRowKeys.length} 项</span>
              <Popconfirm
                title="确定要删除选中的记录吗？"
                onConfirm={handleBatchDelete}
                okText="确定"
                cancelText="取消"
              >
                <Button danger icon={<DeleteOutlined />}>
                  批量删除
                </Button>
              </Popconfirm>
            </Space>
          </div>
        )}

        <Table
          columns={columns}
          dataSource={operations}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          scroll={{ x: 1600 }}
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, size) => {
              setCurrent(page);
              setPageSize(size || 10);
            },
          }}
        />
      </Card>

      {/* 创建/编辑运营模态框 */}
      <Modal
        title={editingOperation ? '编辑事件运营' : '新建事件运营'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="occurredAt"
                label="发生时间"
                rules={[{ required: true, message: '请选择发生时间' }]}
              >
                <DatePicker
                  showTime
                  style={{ width: '100%' }}
                  placeholder="请选择发生时间"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="categoryId"
                label="事件分类"
                rules={[{ required: true, message: '请选择事件分类' }]}
              >
                <Select placeholder="请选择事件分类">
                  {categories.map((category) => (
                    <Option key={category.id} value={category.id}>
                      {category.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="occurredCondition"
            label="发生条件"
            rules={[
              { required: true, message: '请输入发生条件' },
              { max: 1000, message: '发生条件不能超过1000个字符' },
            ]}
          >
            <TextArea
              rows={3}
              placeholder="请输入发生条件"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="事件描述"
            rules={[
              { max: 2000, message: '事件描述不能超过2000个字符' },
            ]}
          >
            <TextArea
              rows={4}
              placeholder="请输入事件描述"
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="ownerId"
                label="负责人"
                rules={[{ required: true, message: '请选择负责人' }]}
              >
                <Select placeholder="请选择负责人">
                  {users.map((user) => (
                    <Option key={user.id} value={user.id}>
                      {user.username} ({user.email})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="processType"
                label="处理状态"
                rules={[{ required: true, message: '请选择处理状态' }]}
              >
                <Select placeholder="请选择处理状态">
                  {Object.entries(processTypeMap).map(([key, value]) => (
                    <Option key={key} value={key}>
                      {value.text}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="relatedEmployeeIds"
            label="关联员工"
          >
            <Select
              mode="multiple"
              placeholder="搜索并选择关联员工"
              showSearch
              filterOption={false}
              loading={employeeSearchLoading}
              onSearch={searchEmployees}
              notFoundContent={employeeSearchLoading ? <Spin size="small" /> : '暂无数据'}
              style={{ width: '100%' }}
              onDropdownVisibleChange={(open) => {
                // 当下拉框打开时，如果还没有搜索结果，可以触发一次搜索
                if (open && employeeSearchResults.length === 0) {
                  // 可以在这里添加默认搜索逻辑，比如搜索最近使用的员工
                }
              }}
            >
              {employeeSearchResults.map((employee) => (
                <Option key={employee.username} value={employee.username}>
                  {employee.name} ({employee.email})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="processDescription"
            label="处理描述"
            rules={[
              { max: 2000, message: '处理描述不能超过2000个字符' },
            ]}
          >
            <TextArea
              rows={3}
              placeholder="请输入处理描述"
            />
          </Form.Item>

          <Form.Item
            name="processCompletedAt"
            label="处理完成时间"
          >
            <DatePicker
              showTime
              style={{ width: '100%' }}
              placeholder="请选择处理完成时间"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingOperation ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default EventOperationManagement;
