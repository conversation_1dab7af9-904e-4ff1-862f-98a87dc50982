import React, { useEffect, useState, useRef } from 'react';
import { Card, Button, Space, Input, message, Modal, Form, Select, Tag, DatePicker } from 'antd';
import { SaveOutlined, RollbackOutlined, PlayCircleOutlined, NodeIndexOutlined, AppstoreAddOutlined } from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import PermissionGuard from '../components/PermissionGuard';
import LogicFlow from '@logicflow/core';
import '@logicflow/core/dist/style/index.css';
import PresetNodeConfigForm from '../components/PresetNodeConfigForm';
import { CustomHtmlPresetNodeDefinition, presetNodeStyles } from '../components/CustomHtmlPresetNode';
import axios from 'axios';
import { config } from '../config';
// 本地类型定义
interface FormField {
  key: string;
  label: string;
  type: 'input' | 'select' | 'textarea' | 'condition_array';
  required: boolean;
  defaultValue: any;
  options?: { label: string; value: string }[];
  placeholder?: string;
  validation?: Record<string, any>;
}

interface PresetNode {
  id: string;
  name: string;
  description: string;
  category: string;
  nodeType: string;
  icon: string;
  formFields: FormField[];
  style: {
    fill: string;
    stroke: string;
    strokeWidth: number;
    fontSize: number;
    fontColor: string;
    borderRadius?: number;
  };
}



const API_URL = config.apiBaseUrl;

interface FlowData {
  id?: string;
  name: string;
  executionTime?: string;
  description: string;
  data: {
    nodes: any[];
    edges: any[];
  };
  status?: string;
  executionRetentionDays?: number;
}

const FlowEditor: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isNew = id === 'new';
  const [flowData, setFlowData] = useState<FlowData>({
    name: '',
    executionTime: '',
    description: '',
    data: { nodes: [], edges: [] },
    executionRetentionDays: 30
  });
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [presetNodeSelectorVisible, setPresetNodeSelectorVisible] = useState(false);
  const [presetNodeConfigVisible, setPresetNodeConfigVisible] = useState(false);
  const [selectedPresetNode, setSelectedPresetNode] = useState<PresetNode | null>(null);
  const [editingNodeId, setEditingNodeId] = useState<string | null>(null);
  const [editingNodeConfig, setEditingNodeConfig] = useState<Record<string, any> | null>(null);
  const [selectedNodeCategory, setSelectedNodeCategory] = useState<string>('common');
  const [, setSelectedNode] = useState<any>(null);
  const [,] = useState<{x: number, y: number} | null>(null);
  const [] = Form.useForm();

  // 示例数据相关状态
  const [exampleData, setExampleData] = useState<any>(null);
  const [isGeneratingExample, setIsGeneratingExample] = useState(false);
  const [showExampleModal, setShowExampleModal] = useState(false);

  // 执行参数相关状态
  const [showExecuteModal, setShowExecuteModal] = useState(false);
  const [executeForm] = Form.useForm();

  // 获取起始节点类型
  const getStartNodeType = () => {
    if (!lfRef.current) return null;

    const graphData = lfRef.current.getGraphData();
    if (!graphData.nodes || graphData.nodes.length === 0) return null;

    // 查找起始节点（预置节点中的 cron_start 或 http_start）
    const startNode = graphData.nodes.find((node: any) => {
      if (node.properties?.isPresetNode) {
        const presetNodeId = node.properties.presetNodeId;
        return presetNodeId === 'cron_start' || presetNodeId === 'http_start';
      }
      return false;
    });

    return startNode ? startNode.properties.presetNodeId : null;
  };

  // API调用函数


  const executeFlow = async (inputData: Record<string, any> = {}) => {
    if (!id || isNew) {
      message.warning('请先保存流程');
      return;
    }

    try {
      setLoading(true);
      const executeData = {
        // 不指定 startNodeId，让后端自动查找开始节点
        inputData: {
          timestamp: new Date().toISOString(),
          ...inputData
        }
      };

      const response = await axios.post(`${API_URL}/flows/${id}/execute`, executeData);
      message.success('流程执行成功');
      console.log('Execution result:', response.data);

      // 显示执行结果
      if (response.data.data) {
        const result = response.data.data;
        Modal.info({
          title: '执行结果',
          width: 800,
          content: (
            <div>
              <p><strong>执行ID:</strong> {result.executionId}</p>
              <p><strong>状态:</strong> {result.status}</p>
              <p><strong>步骤数:</strong> {result.steps?.length || 0}</p>
              <p><strong>执行时间:</strong> {result.endTime - result.startTime}ms</p>

              {result.steps && result.steps.length > 0 && (
                <div style={{ marginTop: 16 }}>
                  <h4>执行步骤:</h4>
                  <div style={{ maxHeight: 300, overflowY: 'auto' }}>
                    {result.steps.map((step: any, index: number) => (
                      <div key={step.stepId} style={{
                        padding: 8,
                        margin: '4px 0',
                        backgroundColor: step.status === 'completed' ? '#f6ffed' : '#fff2f0',
                        border: `1px solid ${step.status === 'completed' ? '#b7eb8f' : '#ffccc7'}`,
                        borderRadius: 4
                      }}>
                        <div><strong>步骤 {index + 1}:</strong> {step.nodeName} ({step.nodeType})</div>
                        <div><strong>状态:</strong> {step.status}</div>
                        <div><strong>耗时:</strong> {step.duration}ms</div>
                        {step.output && (
                          <div><strong>输出:</strong> {JSON.stringify(step.output, null, 2)}</div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {result.finalOutput && (
                <div style={{ marginTop: 16 }}>
                  <h4>最终输出:</h4>
                  <pre style={{ backgroundColor: '#f5f5f5', padding: 8, borderRadius: 4 }}>
                    {JSON.stringify(result.finalOutput, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          ),
        });
      }
    } catch (error: any) {
      const errorMsg = error.response?.data?.message || error.message || '流程执行失败';
      message.error(errorMsg);
      console.error('Failed to execute flow:', error);
    } finally {
      setLoading(false);
    }
  };

  // 预览流程数据
  const generateExample = async () => {
    if (!lfRef.current) {
      message.warning('流程编辑器未初始化');
      return;
    }

    // 获取当前编辑器中的流程数据
    const currentFlowData = lfRef.current.getGraphData();

    if (!currentFlowData.nodes || currentFlowData.nodes.length === 0) {
      message.warning('请先添加节点后再预览');
      return;
    }

    setIsGeneratingExample(true);
    try {
      const response = await fetch(`${API_URL}/flows/preview`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          flowData: currentFlowData
        })
      });

      if (!response.ok) {
        throw new Error('预览流程失败');
      }

      const result = await response.json();
      if (result.code === 200) {
        setExampleData(result.data);
        setShowExampleModal(true);
        message.success('流程预览生成成功');
      } else {
        throw new Error(result.message || '预览流程失败');
      }
    } catch (error) {
      console.error('预览流程失败:', error);
      message.error('预览流程失败');
    } finally {
      setIsGeneratingExample(false);
    }
  };

  const lfRef = useRef<LogicFlow | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isNew && id) {
      fetchFlowData(id);
    } else if (isNew) {
      // 新建流程时，先设置空数据，等LogicFlow初始化完成后再设置默认节点
      setFlowData({
        name: '',
        executionTime: '',
        description: '',
        data: {
          nodes: [],
          edges: []
        },
        executionRetentionDays: 30
      });
    }
  }, [id, isNew]);

  useEffect(() => {
    console.log('Container effect triggered, containerRef.current:', containerRef.current);
    console.log('FlowData:', flowData);

    if (containerRef.current && flowData) {
      // 延迟初始化，确保容器完全渲染且数据已加载
      setTimeout(() => {
        initLogicFlow();
      }, 100);
    }
  }, [containerRef.current, flowData]);

  // 监听自定义编辑事件
  useEffect(() => {
    const handleNodeEdit = (event: any) => {
      const { nodeId, nodeData } = event.detail;
      console.log('Edit button clicked for node:', nodeId, nodeData);

      // 调用编辑处理函数
      if (nodeData.properties?.isPresetNode) {
        handleNodeEditInternal(nodeData);
      }
    };

    window.addEventListener('node-edit', handleNodeEdit);
    return () => {
      window.removeEventListener('node-edit', handleNodeEdit);
    };
  }, []);

  // 监听 flowData 变化，重新渲染流程图
  useEffect(() => {
    console.log('FlowData useEffect triggered:', {
      hasLfRef: !!lfRef.current,
      hasFlowData: !!flowData,
      hasData: !!flowData.data,
      flowDataStructure: flowData,
      nodesLength: flowData.data?.nodes?.length,
      edgesLength: flowData.data?.edges?.length
    });

    if (lfRef.current && flowData.data && (flowData.data.nodes?.length > 0 || flowData.data.edges?.length > 0)) {
      console.log('FlowData changed, re-rendering:', flowData.data);

      // 转换后端数据格式为 LogicFlow 期望的格式
      const transformedData = {
        nodes: flowData.data.nodes.map((node: any) => {
          const nodeData: any = {
            id: node.id,
            type: node.type,
            x: node.x,
            y: node.y,
            properties: node.properties || {}
          };

          // 对于HTML预置节点，不设置text属性，内容由组件内部生成
          if (node.type !== 'CustomHtmlPresetNode' && node.text) {
            nodeData.text = typeof node.text === 'string' ? {
              x: node.x,
              y: node.y,
              value: node.text
            } : node.text;
          }

          return nodeData;
        }),
        edges: flowData.data.edges.map((edge: any) => ({
          id: edge.id,
          type: edge.type,
          sourceNodeId: edge.sourceNodeId,
          targetNodeId: edge.targetNodeId,
          text: typeof edge.text === 'string' ? {
            value: edge.text
          } : edge.text,
          startPoint: edge.startPoint,
          endPoint: edge.endPoint,
          pointsList: edge.pointsList || [],
          properties: edge.properties || {}
        }))
      };

      console.log('Re-rendering with transformed data:', transformedData);
      lfRef.current.render(transformedData);
    }
  }, [flowData]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (lfRef.current && typeof lfRef.current.destroy === 'function') {
        try {
          lfRef.current.destroy();
        } catch (error) {
          console.warn('Failed to destroy LogicFlow instance on unmount:', error);
        }
      }
    };
  }, []);

  const fetchFlowData = async (flowId: string) => {
    setLoading(true);
    try {
      const response = await axios.get(`${API_URL}/flows/${flowId}`);
      const data = response.data.data;
      console.log('Fetched flow data from backend:', data);

      // 处理后端返回的数据格式
      let parsedData = data;
      if (typeof data.data === 'string') {
        try {
          parsedData = {
            ...data,
            data: JSON.parse(data.data)
          };
          console.log('Parsed JSON data:', parsedData.data);
        } catch (parseError) {
          console.error('Failed to parse flow data JSON:', parseError);
          parsedData = {
            ...data,
            data: { nodes: [], edges: [] }
          };
        }
      }

      console.log('Final flow data structure:', {
        hasData: !!parsedData.data,
        dataType: typeof parsedData.data,
        nodes: parsedData.data?.nodes,
        edges: parsedData.data?.edges,
        nodesLength: parsedData.data?.nodes?.length,
        edgesLength: parsedData.data?.edges?.length
      });

      setFlowData(parsedData);
    } catch (error) {
      message.error('获取流程数据失败');
      console.error('Failed to fetch flow data:', error);
    } finally {
      setLoading(false);
    }
  };

  const initLogicFlow = () => {
    if (!containerRef.current) return;

    console.log('Initializing LogicFlow...');

    // 清除之前的实例
    if (lfRef.current && typeof lfRef.current.destroy === 'function') {
      try {
        lfRef.current.destroy();
      } catch (error) {
        console.warn('Failed to destroy LogicFlow instance:', error);
      }
    }

    // 创建新实例
    const lf = new LogicFlow({
      container: containerRef.current,
      width: containerRef.current.offsetWidth || 800,
      height: containerRef.current.offsetHeight || 600,
      grid: {
        size: 20,
        visible: true,
      },
      keyboard: {
        enabled: true,
      },
      // 启用边的编辑
      edgeType: 'polyline',
      // 允许调整边
      allowResize: true,
      // 允许旋转
      allowRotate: true,
      // 启用多选
      multipleSelectKey: 'meta',
      // 启用右键菜单
      style: {
        rect: {
          rx: 5,
          ry: 5,
          strokeWidth: 2,
        },
        circle: {
          fill: '#f5f5f5',
          stroke: '#666',
        },
        diamond: {
          fill: '#ffe6cc',
          stroke: '#d79b00',
        },
        text: {
          color: '#333',
          fontSize: 12,
        }
      }
    });

    console.log('LogicFlow instance created:', lf);

    // 注册自定义 HTML 预置节点
    lf.register(CustomHtmlPresetNodeDefinition);

    // 添加自定义样式
    const styleElement = document.createElement('style');
    styleElement.textContent = presetNodeStyles;
    document.head.appendChild(styleElement);

    // 添加右键菜单事件
    lf.on('blank:contextmenu', ({ position }) => {
      console.log('Right click at:', position);
      // 可以在这里添加右键菜单逻辑
    });

    // 添加节点点击事件
    lf.on('node:click', ({ data }) => {
      console.log('Node clicked:', data);
    });

    // 禁用预置节点的文本编辑
    lf.on('node:text-edit', ({ data }) => {
      if (data.properties?.isPresetNode) {
        return false; // 阻止文本编辑
      }
    });

    // 监听节点选择事件（简化版，不需要浮动按钮）
    lf.on('node:click', ({ data }) => {
      console.log('Node clicked:', data);
      setSelectedNode(data.properties?.isPresetNode ? data : null);
    });

    // 监听画布点击事件（取消选择）
    lf.on('blank:click', () => {
      setSelectedNode(null);
    });

    // 监听自定义编辑事件
    lf.on('custom:edit-preset-node', ({ nodeId }: { nodeId: string }) => {
      console.log('Custom edit event triggered for node:', nodeId);
      const nodeData = lf.getNodeModelById(nodeId);
      if (nodeData && nodeData.properties?.isPresetNode) {
        handleNodeEditInternal(nodeData);
      }
    });

    // 添加连线完成事件
    lf.on('connection:not-allowed', ({ msg }) => {
      console.log('Connection not allowed:', msg);
    });

    // 如果有数据则渲染
    console.log('Checking flow data for rendering:', {
      hasFlowData: !!flowData,
      hasData: !!flowData.data,
      dataStructure: flowData.data,
      nodesLength: flowData.data?.nodes?.length,
      edgesLength: flowData.data?.edges?.length
    });

    if (flowData.data && (flowData.data.nodes?.length > 0 || flowData.data.edges?.length > 0)) {
      console.log('Rendering flow data:', flowData.data);

      // 转换后端数据格式为 LogicFlow 期望的格式
      const transformedData = {
        nodes: flowData.data.nodes.map((node: any) => ({
          id: node.id,
          type: node.type,
          x: node.x,
          y: node.y,
          text: typeof node.text === 'string' ? {
            x: node.x,
            y: node.y,
            value: node.text
          } : node.text,
          properties: node.properties || {}
        })),
        edges: flowData.data.edges.map((edge: any) => ({
          id: edge.id,
          type: edge.type,
          sourceNodeId: edge.sourceNodeId,
          targetNodeId: edge.targetNodeId,
          text: typeof edge.text === 'string' ? {
            value: edge.text
          } : edge.text,
          startPoint: edge.startPoint,
          endPoint: edge.endPoint,
          pointsList: edge.pointsList || [],
          properties: edge.properties || {}
        }))
      };

      console.log('Transformed data for LogicFlow:', transformedData);
      lf.render(transformedData);
    } else {
      console.log('Rendering empty flow');
      // 如果是新建流程，添加默认的 cron_start 节点
      if (isNew) {
        console.log('Adding default cron_start node for new flow');
        const defaultNode = {
          id: 'cron_start_default',
          type: 'CustomHtmlPresetNode',
          x: 200,
          y: 150,
          text: '定时开始',
          properties: {
            isPresetNode: true,
            presetNodeId: 'cron_start',
            presetNodeName: '定时开始',
            width: 160,
            height: 160,
            formData: {
              cronExpression: '0 9 * * *',
              description: '每天上午9点执行'
            },
            config: {
              cronExpression: '0 9 * * *',
              description: '每天上午9点执行'
            }
          }
        };

        lf.render({
          nodes: [defaultNode],
          edges: []
        });
      } else {
        // 渲染空画布
        lf.render({
          nodes: [],
          edges: []
        });
      }
    }

    lfRef.current = lf;
    console.log('LogicFlow initialization complete, lfRef.current set:', !!lfRef.current);

    // LogicFlow 创建后，检查是否有待渲染的数据
    if (flowData.data && (flowData.data.nodes?.length > 0 || flowData.data.edges?.length > 0)) {
      console.log('Found existing flow data after LogicFlow creation, rendering now:', flowData.data);

      // 转换后端数据格式为 LogicFlow 期望的格式
      const transformedData = {
        nodes: flowData.data.nodes.map((node: any) => ({
          id: node.id,
          type: node.type,
          x: node.x,
          y: node.y,
          text: typeof node.text === 'string' ? {
            x: node.x,
            y: node.y,
            value: node.text
          } : node.text,
          properties: node.properties || {}
        })),
        edges: flowData.data.edges.map((edge: any) => ({
          id: edge.id,
          type: edge.type,
          sourceNodeId: edge.sourceNodeId,
          targetNodeId: edge.targetNodeId,
          text: typeof edge.text === 'string' ? {
            value: edge.text
          } : edge.text,
          startPoint: edge.startPoint,
          endPoint: edge.endPoint,
          pointsList: edge.pointsList || [],
          properties: edge.properties || {}
        }))
      };

      console.log('Post-init rendering with transformed data:', transformedData);
      lf.render(transformedData);
    }
  };





  // 处理节点编辑
  const handleNodeEditInternal = async (nodeData: any) => {
    console.log('Editing node:', nodeData);

    // 检查是否是预置节点
    if (nodeData.properties?.isPresetNode) {
      const presetNodeId = nodeData.properties.presetNodeId;
      const currentConfig = nodeData.properties.formData || {};

      try {
        // 获取预置节点详情
        const response = await axios.get(`${API_URL}/preset-nodes/${presetNodeId}`);
        const presetNode = response.data.data;

        if (presetNode) {
          setSelectedPresetNode(presetNode);
          setEditingNodeId(nodeData.id);
          setEditingNodeConfig(currentConfig);
          setPresetNodeConfigVisible(true);
        } else {
          message.error('无法找到预置节点配置');
        }
      } catch (error) {
        console.error('Failed to load preset node details:', error);
        message.error('加载节点配置失败');
      }
    } else {
      message.info('该节点不支持编辑');
    }
  };

  // 处理预置节点选择
  const handlePresetNodeSelect = (node: PresetNode) => {
    setSelectedPresetNode(node);
    setPresetNodeConfigVisible(true);
  };

  // 格式化显示数据，高亮变动字段
  const renderFormattedData = (data: any, changedFields: Set<string>) => {
    if (!data || typeof data !== 'object') {
      return <span style={{ color: '#666' }}>{JSON.stringify(data)}</span>;
    }

    // 将JSON字符串按行分割，然后高亮变动的字段
    const jsonString = JSON.stringify(data, null, 2);
    const lines = jsonString.split('\n');

    return (
      <div style={{ fontFamily: 'Monaco, Consolas, monospace', fontSize: '13px', lineHeight: '1.6' }}>
        {changedFields.size > 0 && (
          <div style={{
            color: '#fa8c16',
            fontSize: '12px',
            marginBottom: '8px',
            fontWeight: 'bold',
            padding: '4px 8px',
            backgroundColor: '#fff7e6',
            borderRadius: '4px',
            border: '1px solid #ffd591'
          }}>
            🔄 {changedFields.size} 个字段发生变化: {Array.from(changedFields).join(', ')}
          </div>
        )}
        <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
          {lines.map((line, index) => {
            // 检查这一行是否包含变动的字段
            const hasChangedField = Array.from(changedFields).some(field =>
              line.includes(`"${field}"`)
            );

            return (
              <div
                key={index}
                style={{
                  backgroundColor: hasChangedField ? '#fff7e6' : 'transparent',
                  padding: hasChangedField ? '2px 4px' : '0',
                  borderRadius: hasChangedField ? '3px' : '0',
                  margin: hasChangedField ? '1px 0' : '0',
                  border: hasChangedField ? '1px solid #ffd591' : 'none',
                  color: hasChangedField ? '#d46b08' : '#333',
                  fontWeight: hasChangedField ? 'bold' : 'normal'
                }}
              >
                {line}
              </div>
            );
          })}
        </pre>
      </div>
    );
  };

  // 处理预置节点配置提交
  const handlePresetNodeConfigSubmit = (formData: Record<string, any>) => {
    if (!lfRef.current || !selectedPresetNode) return;

    // 生成节点显示文本，包含配置信息


    if (editingNodeId) {
      // 编辑现有节点
      const nodeData = lfRef.current.getNodeModelById(editingNodeId);
      if (nodeData) {
        lfRef.current.setProperties(editingNodeId, {
          presetNodeId: selectedPresetNode.id,
          presetNodeName: selectedPresetNode.name,
          formData: formData,
          isPresetNode: true,
          nodeCategory: selectedPresetNode.category,
          nodeDescription: selectedPresetNode.description,
        });
        // 对于HTML节点，不需要调用updateText，数据通过properties传递
        // HTML节点会自动根据properties中的formData重新渲染
        message.success(`已更新节点配置: ${selectedPresetNode.name}`);
      }
    } else {
      // 添加新节点
      const nodeId = `preset_${selectedPresetNode.id}_${Date.now()}`;
      const centerX = (lfRef.current.graphModel.width || 800) / 2;
      const centerY = (lfRef.current.graphModel.height || 600) / 2;

      // 创建预置节点配置
      const nodeConfig: any = {
        id: nodeId,
        type: 'CustomHtmlPresetNode', // 使用自定义 HTML 节点
        x: centerX + Math.random() * 100 - 50,
        y: centerY + Math.random() * 100 - 50,
        // text: displayText, // HTML 节点不需要 text 属性
        properties: {
          presetNodeId: selectedPresetNode.id,
          presetNodeName: selectedPresetNode.name,
          formData: formData,
          isPresetNode: true,
          nodeCategory: selectedPresetNode.category,
          nodeDescription: selectedPresetNode.description,
        },
      };

      // HTML 节点不需要额外的样式设置，由组件内部处理

      lfRef.current.addNode(nodeConfig);
      message.success(`已添加预置节点: ${selectedPresetNode.name}`);
    }

    // 关闭弹窗并重置状态
    setPresetNodeConfigVisible(false);
    setSelectedPresetNode(null);
    setEditingNodeId(null);
    setEditingNodeConfig(null);
  };

  const handleSave = async () => {
    if (!lfRef.current) return;

    // 获取当前流程图数据
    const graphData = lfRef.current.getGraphData();

    // 打开保存对话框
    setIsModalVisible(true);
    setFlowData(prev => ({
      ...prev,
      data: graphData
    }));
  };

  const handleSubmit = async (values: { name: string; executionTime: string; description: string; executionRetentionDays?: number }) => {
    if (!lfRef.current) return;

    setLoading(true);
    try {
      const graphData = lfRef.current.getGraphData();

      // 转换数据格式以匹配后端期望的格式
      const transformedData = {
        nodes: graphData.nodes.map((node: any) => {
          const nodeData: any = {
            id: node.id,
            type: node.type,
            x: node.x,
            y: node.y,
            properties: node.properties || {}
          };

          // 对于HTML预置节点，保存空的text，内容由properties中的数据决定
          if (node.type === 'CustomHtmlPresetNode') {
            nodeData.text = '';
          } else {
            nodeData.text = typeof node.text === 'object' ? (node.text?.value || '') : (node.text || '');
          }

          return nodeData;
        }),
        edges: graphData.edges.map((edge: any) => ({
          id: edge.id,
          type: edge.type,
          sourceNodeId: edge.sourceNodeId || edge.source,
          targetNodeId: edge.targetNodeId || edge.target,
          text: typeof edge.text === 'object' ? (edge.text?.value || '') : (edge.text || ''),
          startPoint: edge.startPoint,
          endPoint: edge.endPoint,
          pointsList: edge.pointsList || [],
          properties: edge.properties || {}
        }))
      };

      const saveData = {
        name: values.name,
        executionTime: values.executionTime,
        description: values.description,
        data: transformedData,
        status: 'draft',
        executionRetentionDays: values.executionRetentionDays
      };

      console.log('Saving data:', saveData);
      console.log('Original graphData:', graphData);

      let response;
      if (isNew) {
        response = await axios.post(`${API_URL}/flows`, saveData);
      } else {
        response = await axios.put(`${API_URL}/flows/${id}`, saveData);
      }

      message.success('保存成功');
      setIsModalVisible(false);

      if (isNew && response.data.data.id) {
        navigate(`/flow/${response.data.data.id}`);
      }
    } catch (error) {
      message.error('保存失败');
      console.error('Failed to save flow:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExecute = async () => {
    // 检查起始节点类型
    const startNodeType = getStartNodeType();

    if (!startNodeType) {
      message.warning('未找到起始节点（cron_start 或 http_start）');
      return;
    }

    if (startNodeType === 'http_start' || startNodeType === 'cron_start') {
      // 需要输入参数，显示参数输入框
      setShowExecuteModal(true);
//       executeForm.resetFields();
    } else {
      // 直接执行
      await executeFlow();
    }
  };

  // 处理执行参数提交
  const handleExecuteSubmit = async (values: any) => {
    const startNodeType = getStartNodeType();
    let inputData = {};

    if (startNodeType === 'http_start') {
      try {
        // 解析JSON数据
        const requestData = JSON.parse(values.requestData);
        inputData = { requestData };
      } catch (error) {
        message.error('请输入有效的JSON格式数据');
        return;
      }
    } else if (startNodeType === 'cron_start') {
      // 使用输入的时间作为currentTime，格式化为字符串
      const currentTime = values.currentTime.format('YYYY-MM-DD HH:mm');
      inputData = { currentTime };
    }

    setShowExecuteModal(false);
    await executeFlow(inputData);
  };

  return (
    <Card 
      title={isNew ? "创建新流程" : `编辑流程: ${flowData.name}`}
      extra={
        <Space>
          <Button 
            icon={<RollbackOutlined />} 
            onClick={() => navigate('/flows')}
          >
            返回列表
          </Button>
          <PermissionGuard permission="flows:write">
            <Button 
              type="primary" 
              icon={<SaveOutlined />} 
              onClick={handleSave}
              loading={loading}
            >
              保存
            </Button>
          </PermissionGuard>
          {!isNew && (
            <>
              <PermissionGuard permission="flows:execute">
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={handleExecute}
                >
                  执行
                </Button>
              </PermissionGuard>
              <PermissionGuard permission="flows:read">
                <Button
                  icon={<NodeIndexOutlined />}
                  onClick={generateExample}
                  loading={isGeneratingExample}
                >
                  预览
                </Button>
              </PermissionGuard>
            </>
          )}
        </Space>
      }
      style={{ height: 'calc(100vh - 100px)' }}
    >
      {/* 节点工具栏 */}
      <div style={{ marginBottom: '16px', padding: '12px', background: '#fafafa', borderRadius: '6px' }}>
        <Space>
          <span style={{ fontWeight: 'bold' }}>添加节点:</span>
          <Button
            size="small"
            type="primary"
            icon={<AppstoreAddOutlined />}
            onClick={() => {
              setSelectedNodeCategory('common');
              setPresetNodeSelectorVisible(true);
            }}
          >
            通用节点
          </Button>
          <Button
            size="small"
            type="primary"
            icon={<AppstoreAddOutlined />}
            style={{
              backgroundColor: '#52c41a',
              borderColor: '#52c41a',
              color: '#fff'
            }}
            onClick={() => {
              setSelectedNodeCategory('business');
              setPresetNodeSelectorVisible(true);
            }}
          >
            业务节点
          </Button>
          <span style={{ marginLeft: '20px', color: '#666' }}>
            💡 提示: 拖拽节点的锚点可以创建连线
          </span>
        </Space>
      </div>

      <div
        ref={containerRef}
        style={{
          width: '100%',
          height: 'calc(100% - 80px)',
          minHeight: '500px',
          border: '1px solid #d9d9d9',
          borderRadius: '4px',
          position: 'relative'
        }}
      />
      
      <Modal
        title="保存流程"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        zIndex={800}
      >
        <Form
          initialValues={{
            name: flowData.name,
            executionTime: flowData.executionTime,
            description: flowData.description,
            executionRetentionDays: flowData.executionRetentionDays || 30
          }}
          onFinish={handleSubmit}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="流程名称"
            rules={[{ required: true, message: '请输入流程名称' }]}
          >
            <Input placeholder="请输入流程名称" />
          </Form.Item>

          <Form.Item
            name="executionTime"
            label="执行时机"
          >
            <Input placeholder="请输入执行时机" />
          </Form.Item>

          <Form.Item
            name="description"
            label="流程描述"
          >
            <Input.TextArea placeholder="请输入流程描述" rows={4} />
          </Form.Item>

          <Form.Item
            name="executionRetentionDays"
            label="执行记录保存期限"
            tooltip="设置执行记录的保存天数，0表示永久保存"
            rules={[{ required: true, message: '请选择保存期限' }]}
          >
            <Select placeholder="请选择保存期限">
              <Select.Option value={0}>永久保存</Select.Option>
              <Select.Option value={7}>7天</Select.Option>
              <Select.Option value={15}>15天</Select.Option>
              <Select.Option value={30}>30天</Select.Option>
              <Select.Option value={60}>60天</Select.Option>
              <Select.Option value={90}>90天</Select.Option>
              <Select.Option value={180}>180天</Select.Option>
              <Select.Option value={365}>1年</Select.Option>
            </Select>
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 预置节点选择器 */}
      <Modal
        title={`选择${selectedNodeCategory === 'common' ? '通用' : '业务'}节点`}
        open={presetNodeSelectorVisible}
        onCancel={() => setPresetNodeSelectorVisible(false)}
        footer={null}
        width={800}
        zIndex={1000}
      >
        <PresetNodeSelector
          category={selectedNodeCategory}
          onSelect={handlePresetNodeSelect}
          onCancel={() => setPresetNodeSelectorVisible(false)}
        />
      </Modal>

      {/* 预置节点配置表单 */}
      <Modal
        title={`${editingNodeId ? '编辑' : '配置'} ${selectedPresetNode?.name || '节点'}`}
        open={presetNodeConfigVisible}
        onCancel={() => {
          setPresetNodeConfigVisible(false);
          setSelectedPresetNode(null);
          setEditingNodeId(null);
          setEditingNodeConfig(null);
        }}
        footer={null}
        width={600}
        zIndex={1100}
      >
        {selectedPresetNode && (
          <PresetNodeConfigForm
            node={selectedPresetNode}
            onSubmit={handlePresetNodeConfigSubmit}
            onCancel={() => {
              setPresetNodeConfigVisible(false);
              setSelectedPresetNode(null);
              setEditingNodeId(null);
              setEditingNodeConfig(null);
            }}
            initialValues={editingNodeConfig || undefined}
          />
        )}
      </Modal>

      {/* 执行参数输入框 */}
      <Modal
        title="执行参数设置"
        open={showExecuteModal}
        onCancel={() => setShowExecuteModal(false)}
        footer={null}
        width={600}
        zIndex={1200}
      >
        <Form
          form={executeForm}
          onFinish={handleExecuteSubmit}
          layout="vertical"
        >
          {getStartNodeType() === 'http_start' && (
            <Form.Item
              name="requestData"
              label="请求数据 (JSON格式)"
              rules={[
                { required: true, message: '请输入请求数据' },
                {
                  validator: (_, value) => {
                    if (!value) return Promise.resolve();
                    try {
                      JSON.parse(value);
                      return Promise.resolve();
                    } catch {
                      return Promise.reject(new Error('请输入有效的JSON格式'));
                    }
                  }
                }
              ]}
            >
              <Input.TextArea
                rows={8}
                placeholder='请输入JSON格式的请求数据，例如：&#10;{&#10;  "user_id": "123",&#10;  "name": "张三",&#10;  "email": "<EMAIL>"&#10;}'
              />
            </Form.Item>
          )}

          {getStartNodeType() === 'cron_start' && (
            <Form.Item
              name="currentTime"
              label="执行时间"
              rules={[
                { required: true, message: '请选择执行时间' }
              ]}
            >
              <DatePicker
                showTime
                format="YYYY-MM-DD HH:mm"
                placeholder="选择执行时间"
                style={{ width: '100%' }}
              />
            </Form.Item>
          )}

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setShowExecuteModal(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                执行流程
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 流程预览模态框 */}
      <Modal
        title="流程预览"
        open={showExampleModal}
        onCancel={() => setShowExampleModal(false)}
        footer={[
          <Button key="close" onClick={() => setShowExampleModal(false)}>
            关闭
          </Button>
        ]}
        width={1000}
        style={{ top: 20 }}
        zIndex={900}
      >
        {exampleData && (
          <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
            <div style={{
              marginBottom: 20,
              padding: 16,
              backgroundColor: '#f0f2f5',
              borderRadius: 8,
              border: '1px solid #d9d9d9'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <span style={{ fontSize: 16, fontWeight: 'bold' }}>📊 流程概览</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                  <div>
                    <strong>状态:</strong>
                    <Tag color={exampleData.success ? 'green' : 'red'} style={{ marginLeft: 8 }}>
                      {exampleData.success ? '✅ 成功' : '❌ 失败'}
                    </Tag>
                  </div>
                  <div>
                    <strong>节点数量:</strong>
                    <Tag color="blue" style={{ marginLeft: 8 }}>
                      {Object.keys(exampleData.nodeResults || {}).length} 个
                    </Tag>
                  </div>
                </div>
              </div>
            </div>

            {exampleData.nodeResults && Object.keys(exampleData.nodeResults).length > 0 && (
              <div>
                <h4>数据流转过程</h4>
                <div style={{ position: 'relative' }}>
                  {(exampleData.executionOrder || Object.keys(exampleData.nodeResults)).map((nodeId: string, index: number, array: string[]) => {
                    const nodeResult = exampleData.nodeResults[nodeId];
                    if (!nodeResult) return null;

                    // 获取变动字段
                    const changedFields = new Set(
                      (nodeResult.changes || []).map((change: any) => change.field)
                    );

                    return (
                      <div key={nodeId}>
                        <Card
                          size="small"
                          style={{
                            marginBottom: index < array.length - 1 ? 8 : 16,
                            border: '1px solid #d9d9d9'
                          }}
                          title={
                            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                              <Tag color="blue">{nodeResult.nodeType}</Tag>
                              {nodeResult.presetNodeId && (
                                <Tag color="orange">{nodeResult.presetNodeId}</Tag>
                              )}
                              <span style={{ fontSize: 14, fontWeight: 'bold' }}>
                                {nodeResult.description}
                              </span>
                            </div>
                          }
                        >
                          <div>
                            <strong>流程数据:</strong>
                            <div style={{
                              backgroundColor: '#fafafa',
                              border: '1px solid #e8e8e8',
                              borderRadius: 6,
                              padding: 12,
                              marginTop: 8,
                              fontFamily: 'Monaco, Consolas, monospace',
                              fontSize: 13,
                              lineHeight: 1.5
                            }}>
                              {renderFormattedData(nodeResult.flowData, changedFields as Set<string>)}
                            </div>
                          </div>
                        </Card>

                        {/* 添加箭头分隔符 */}
                        {index < array.length - 1 && (
                          <div style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            margin: '16px 0',
                            position: 'relative'
                          }}>
                            <div style={{
                              width: '2px',
                              height: '20px',
                              backgroundColor: '#1890ff',
                              position: 'relative'
                            }}>
                              <div style={{
                                position: 'absolute',
                                bottom: '-3px',
                                left: '50%',
                                transform: 'translateX(-50%)',
                                width: 0,
                                height: 0,
                                borderLeft: '6px solid transparent',
                                borderRight: '6px solid transparent',
                                borderTop: '8px solid #1890ff'
                              }} />
                            </div>
                            <span style={{
                              position: 'absolute',
                              left: '50%',
                              transform: 'translateX(-50%)',
                              backgroundColor: '#fff',
                              padding: '2px 8px',
                              fontSize: 12,
                              color: '#1890ff',
                              fontWeight: 'bold',
                              top: '-10px'
                            }}>
                              数据流转
                            </span>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {exampleData.flowData && (
              <div style={{ marginTop: 24 }}>
                <Card
                  title={
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <span style={{ fontSize: 16, fontWeight: 'bold', color: '#52c41a' }}>
                        🎯 最终输出
                      </span>
                    </div>
                  }
                  style={{
                    border: '2px solid #b7eb8f',
                    borderRadius: 8,
                    backgroundColor: '#f6ffed'
                  }}
                  bodyStyle={{
                    backgroundColor: '#fff',
                    borderRadius: 6
                  }}
                >
                  <div style={{
                    backgroundColor: '#fafafa',
                    border: '1px solid #e8e8e8',
                    borderRadius: 6,
                    padding: 12,
                    fontFamily: 'Monaco, Consolas, monospace',
                    fontSize: 13,
                    lineHeight: 1.5
                  }}>
                    {renderFormattedData(exampleData.flowData, new Set())}
                  </div>
                </Card>
              </div>
            )}
          </div>
        )}
      </Modal>
    </Card>
  );
};

// 内联预置节点选择器组件
const PresetNodeSelector: React.FC<{
  category: string;
  onSelect: (node: PresetNode) => void;
  onCancel: () => void;
}> = ({ category, onSelect }) => {
  const [nodes, setNodes] = useState<PresetNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    loadNodes();
  }, [category]);

  const loadNodes = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${API_URL}/preset-nodes?category=${category}`);
      setNodes(response.data.data || []);
    } catch (error) {
      message.error('加载预置节点失败');
      console.error('Failed to load preset nodes:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredNodes = nodes.filter(node => {
    const matchesSearch = !searchText ||
      node.name.toLowerCase().includes(searchText.toLowerCase()) ||
      node.description.toLowerCase().includes(searchText.toLowerCase());
    return matchesSearch;
  });

  const categoryMap: Record<string, { label: string; color: string }> = {
    common: { label: '通用', color: 'blue' },
    business: { label: '业务', color: 'orange' },
  };

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Input
          placeholder="搜索节点名称或描述"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300 }}
        />
      </div>

      {loading ? (
        <div style={{ textAlign: 'center', padding: 40 }}>
          <div>加载中...</div>
        </div>
      ) : (
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 16, maxHeight: 400, overflowY: 'auto' }}>
          {filteredNodes.map((node) => (
            <Card
              key={node.id}
              hoverable
              onClick={() => onSelect(node)}
              style={{ cursor: 'pointer' }}
              bodyStyle={{ padding: 16 }}
            >
              <div style={{ display: 'flex', alignItems: 'flex-start', gap: 12 }}>
                <div
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 8,
                    backgroundColor: node.style.fill,
                    border: `2px solid ${node.style.stroke}`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: 18,
                    color: node.style.stroke,
                    flexShrink: 0,
                  }}
                >
                  {node.icon === 'user' && '👤'}
                  {node.icon === 'swap' && '🔄'}
                  {node.icon === 'api' && '🔗'}
                  {node.icon === 'question-circle' && '❓'}
                </div>
                <div style={{ flex: 1, minWidth: 0 }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 4 }}>
                    <h4 style={{ margin: 0, fontSize: 14, fontWeight: 'bold' }}>
                      {node.name}
                    </h4>
                    <span
                      style={{
                        padding: '2px 6px',
                        borderRadius: 4,
                        fontSize: 10,
                        backgroundColor: categoryMap[node.category]?.color === 'blue' ? '#e6f7ff' :
                                       categoryMap[node.category]?.color === 'orange' ? '#fff7e6' :
                                       categoryMap[node.category]?.color === 'green' ? '#f6ffed' : '#fff2f0',
                        color: categoryMap[node.category]?.color === 'blue' ? '#1890ff' :
                               categoryMap[node.category]?.color === 'orange' ? '#fa8c16' :
                               categoryMap[node.category]?.color === 'green' ? '#52c41a' : '#ff4d4f',
                      }}
                    >
                      {categoryMap[node.category]?.label || node.category}
                    </span>
                  </div>
                  <p style={{
                    margin: 0,
                    fontSize: 12,
                    color: '#666',
                    lineHeight: '1.4',
                  }}>
                    {node.description}
                  </p>
                  <div style={{ marginTop: 8, fontSize: 11, color: '#999' }}>
                    {node.formFields.length} 个配置项
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {!loading && filteredNodes.length === 0 && (
        <div style={{ textAlign: 'center', padding: 40, color: '#999' }}>
          {searchText ? '没有找到匹配的节点' : '暂无预置节点'}
        </div>
      )}
    </div>
  );
};

export default FlowEditor;