import React, { useState, useEffect } from 'react';
import {
  Card,
  message,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Popconfirm,
  Row,
  Col,
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { eventCategoryService, userService } from '../services/api';

const { TextArea } = Input;
const { Option } = Select;

interface EventCategory {
  id: string;
  name: string;
  description: string;
  ownerId: string;
  owner?: {
    id: string;
    username: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface User {
  id: string;
  username: string;
  email: string;
}

const EventCategoryManagement: React.FC = () => {
  const [categories, setCategories] = useState<EventCategory[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 模态框状态
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<EventCategory | null>(null);
  const [form] = Form.useForm();

  // 获取事件分类列表
  const fetchCategories = async () => {
    setLoading(true);
    try {
      const response = await eventCategoryService.getCategories({
        page: current,
        pageSize,
      });
      console.log('API Response:', response);
      setCategories(response.data.data.data || []);
      setTotal(response.data.data.total || 0);
    } catch (error) {
      console.error('获取事件分类失败:', error);
      message.error('获取事件分类列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      const response = await userService.getUsers({ page: 1, pageSize: 100 });
      console.log('Users API Response:', response);
      console.log('Users data:', response.data);
      setUsers(response.data.data.list || []);
    } catch (error: any) {
      console.error('获取用户列表失败:', error);
      console.error('Error response:', error.response);
      if (error.response?.status === 403) {
        message.error('没有权限获取用户列表');
      } else {
        message.error('获取用户列表失败');
      }
    }
  };

  useEffect(() => {
    fetchCategories();
  }, [current, pageSize]);

  useEffect(() => {
    fetchUsers();
  }, []);

  // 提交表单
  const handleSubmit = async (values: any) => {
    try {
      if (editingCategory) {
        await eventCategoryService.updateCategory(editingCategory.id, values);
        message.success('更新事件分类成功');
      } else {
        await eventCategoryService.createCategory(values);
        message.success('创建事件分类成功');
      }
      setModalVisible(false);
      fetchCategories();
    } catch (error: any) {
      message.error(error.response?.data?.message || '操作失败');
    }
  };

  // 新建分类
  const handleCreate = () => {
    setEditingCategory(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 编辑分类
  const handleEdit = (category: EventCategory) => {
    setEditingCategory(category);
    form.setFieldsValue({
      name: category.name,
      description: category.description,
      ownerId: category.ownerId,
    });
    setModalVisible(true);
  };

  // 删除单个分类
  const handleDelete = async (id: string) => {
    try {
      await eventCategoryService.deleteCategory(id);
      message.success('删除事件分类成功');
      fetchCategories();
    } catch (error: any) {
      message.error(error.response?.data?.message || '删除失败');
    }
  };

  const columns = [
    {
      title: '分类名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '分类描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '安全运营负责人',
      dataIndex: 'owner',
      key: 'owner',
      render: (owner: any) => owner?.username || '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: EventCategory) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个分类吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button danger size="small" icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card title="事件分类管理">
        <Row justify="end" style={{ marginBottom: 16 }}>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreate}
              >
                新建分类
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={categories}
          rowKey="id"
          loading={loading}
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, size) => {
              setCurrent(page);
              setPageSize(size || 10);
            },
          }}
        />
      </Card>

      {/* 创建/编辑分类模态框 */}
      <Modal
        title={editingCategory ? '编辑事件分类' : '新建事件分类'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="分类名称"
            rules={[
              { required: true, message: '请输入分类名称' },
              { max: 100, message: '分类名称不能超过100个字符' },
            ]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="分类描述"
            rules={[
              { max: 500, message: '分类描述不能超过500个字符' },
            ]}
          >
            <TextArea
              rows={4}
              placeholder="请输入分类描述"
            />
          </Form.Item>

          <Form.Item
            name="ownerId"
            label="安全运营负责人"
            rules={[{ required: true, message: '请选择安全运营负责人' }]}
          >
            <Select
              placeholder="请选择负责人"
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)
                  ?.toLowerCase()
                  ?.includes(input.toLowerCase())
              }
            >
              {users.map((user) => (
                <Option key={user.id} value={user.id}>
                  {user.username} ({user.email})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingCategory ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default EventCategoryManagement;
