import React, { createContext, useContext, useEffect, useState } from 'react';
import type { ReactNode } from 'react';
import { message } from 'antd';
import { userAuthApi } from '../services/authApi';
import type { User, UserContext as UserContextType, UserWithPermissions } from '../types/auth';

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [permissions, setPermissions] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  // 获取用户信息
  const fetchUser = async () => {
    try {
      setLoading(true);
      const response = await userAuthApi.getProfile();
      if (response.data.code === 200) {
        const userWithPermissions: UserWithPermissions = response.data.data;
        setUser(userWithPermissions.user);
        
        // 将权限转换为字符串数组
        const permissionKeys = userWithPermissions.permissions.map(
          (permission) => `${permission.resource}:${permission.action}`
        );
        setPermissions(permissionKeys);
      }
    } catch (error: any) {
      console.error('获取用户信息失败:', error);
      if (error.response?.status === 401) {
        // 用户未认证，清空状态
        setUser(null);
        setPermissions([]);
      } else {
        message.error('获取用户信息失败');
      }
    } finally {
      setLoading(false);
    }
  };

  // 检查用户是否有特定权限
  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    // 直接检查是否有该权限
    if (permissions.includes(permission)) {
      return true;
    }
    
    // 检查是否有对应资源的manage权限（manage权限包含所有操作）
    const [resource] = permission.split(':');
    const managePermission = `${resource}:manage`;
    if (permissions.includes(managePermission)) {
      return true;
    }
    
    return false;
  };

  // 检查用户是否有特定角色
  const hasRole = (roleName: string): boolean => {
    if (!user) return false;
    return user.roles.some(role => role.name === roleName);
  };

  // 刷新用户信息
  const refreshUser = async () => {
    await fetchUser();
  };

  useEffect(() => {
    fetchUser();
  }, []);

  const contextValue: UserContextType = {
    user,
    permissions,
    loading,
    hasPermission,
    hasRole,
    refreshUser,
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

// 自定义Hook
export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

// 权限检查Hook
export const usePermission = (permission: string): boolean => {
  const { hasPermission } = useUser();
  return hasPermission(permission);
};

// 角色检查Hook
export const useRole = (roleName: string): boolean => {
  const { hasRole } = useUser();
  return hasRole(roleName);
};

export default UserContext; 