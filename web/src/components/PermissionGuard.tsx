import React from 'react';
import type { ReactNode } from 'react';
import { usePermission, useRole } from '../contexts/UserContext';

interface PermissionGuardProps {
  children: ReactNode;
  permission?: string;
  role?: string;
  fallback?: ReactNode;
  requireAll?: boolean; // 当同时指定permission和role时，是否需要同时满足
}

/**
 * 权限守护组件
 * 根据用户权限和角色决定是否渲染子组件
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  role,
  fallback = null,
  requireAll = false,
}) => {
  const hasPermission = usePermission(permission || '');
  const hasRole = useRole(role || '');

  // 检查权限逻辑
  let hasAccess = true;

  if (permission && role) {
    // 同时指定了权限和角色
    if (requireAll) {
      // 需要同时满足权限和角色
      hasAccess = hasPermission && hasRole;
    } else {
      // 满足其中一个即可
      hasAccess = hasPermission || hasRole;
    }
  } else if (permission) {
    // 只指定了权限
    hasAccess = hasPermission;
  } else if (role) {
    // 只指定了角色
    hasAccess = hasRole;
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>;
};

export default PermissionGuard; 