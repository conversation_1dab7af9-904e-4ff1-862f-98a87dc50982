import { HtmlNode, HtmlNodeModel } from '@logicflow/core';

// 自定义 HTML 预置节点类
class CustomHtmlPresetNode extends HtmlNode {
  setHtml(rootEl: HTMLElement) {
    const { properties } = this.props.model;
    const { presetNodeName, formData = {}, presetNodeId } = properties;

    // 生成配置项的 HTML
    const configItems = Object.entries(formData)
      .slice(0, 3) // 最多显示3个配置项
      .map(([key, value]) => {
        const displayValue = String(value).length > 20 ? String(value).substring(0, 20) + '...' : value;
        return `
          <div class="config-item">
            <span class="config-label">${key}:</span>
            <span class="config-value">${displayValue}</span>
          </div>
        `;
      }).join('');

    const hasConfig = Object.keys(formData).length > 0;

    const el = document.createElement('div');
    el.className = 'preset-node-wrapper';

    // 根据节点类型设置容器样式类
    let containerClass = 'preset-node-container';
    if (presetNodeId === 'cron_start') {
      containerClass += ' cron-start';
    } else if (presetNodeId === 'http_start') {
      containerClass += ' http-start';
    }

    // 根据节点类型选择图标
    let nodeIcon = '📋';
    if (presetNodeId === 'cron_start') {
      nodeIcon = '⏰';
    } else if (presetNodeId === 'http_start') {
      nodeIcon = '🌐';
    }

    el.innerHTML = `
      <div class="${containerClass}">
        <div class="preset-node-header">
          <div class="node-icon">${nodeIcon}</div>
          <div class="node-title">${presetNodeName || '预置节点'}</div>
          <button class="edit-btn" onclick="editPresetNode('${this.props.model.id}')">
            <span class="edit-icon">✏️</span>
          </button>
        </div>
        <div class="preset-node-body">
          ${hasConfig ? configItems : '<div class="no-config">⚙️ 暂无配置</div>'}
        </div>
        ${hasConfig && Object.keys(formData).length > 3 ?
          '<div class="more-config">还有 ' + (Object.keys(formData).length - 3) + ' 项配置...</div>' :
          ''
        }
      </div>
    `;
    
    rootEl.innerHTML = '';
    rootEl.appendChild(el);

    // 全局编辑函数
    (window as any).editPresetNode = (nodeId: string) => {
      const { graphModel } = this.props;
      graphModel.eventCenter.emit('custom:edit-preset-node', { nodeId });
    };
  }
}

class CustomHtmlPresetNodeModel extends HtmlNodeModel {
  setAttributes() {
    const { width, height, presetNodeId } = this.properties as any;

    // 如果是开始节点（cron_start 或 http_start），设置为圆形尺寸
    if (presetNodeId === 'cron_start' || presetNodeId === 'http_start') {
      this.width = 160;
      this.height = 160;
    } else {
      this.width = width || 220;
      this.height = height || 140;
    }

    this.text.editable = false;
    // 隐藏默认文本显示
    this.text.value = '';

    // 如果是开始节点，设置连接规则
    if (presetNodeId === 'cron_start' || presetNodeId === 'http_start') {
      // 开始节点只能作为源节点，不能作为目标节点
      this.sourceRules.push({
        message: '开始节点只能连接到其他节点',
        validate: () => true,
      });
    }
  }

  // 重写连接规则方法
  getConnectedTargetRules() {
    const { presetNodeId } = this.properties as any;

    // Cron 开始节点只能作为源节点
    if (presetNodeId === 'cron_start') {
      return [
        {
          message: '开始节点只能连接到其他节点',
          validate: () => true,
        },
      ];
    }

    return super.getConnectedTargetRules();
  }

  getConnectedSourceRules() {
    const { presetNodeId } = this.properties as any;

    // Cron 开始节点不能作为目标节点
    if (presetNodeId === 'cron_start') {
      return [
        {
          message: '开始节点不能被其他节点连接',
          validate: () => false,
        },
      ];
    }

    return super.getConnectedSourceRules();
  }

  getDefaultAnchor() {
    const { width, height, x, y, id } = this;
    const { presetNodeId } = this.properties as any;

    // 开始节点只有输出锚点（圆形节点的四个方向）
    if (presetNodeId === 'cron_start' || presetNodeId === 'http_start') {
      const radius = width / 2;
      return [
        {
          x: x + radius,
          y,
          name: 'right',
          id: `${id}_right`,
          type: 'source', // 只能作为连线的起点
        },
        {
          x,
          y: y + radius,
          name: 'bottom',
          id: `${id}_bottom`,
          type: 'source', // 只能作为连线的起点
        },
        {
          x: x - radius,
          y,
          name: 'left',
          id: `${id}_left`,
          type: 'source', // 只能作为连线的起点
        },
        {
          x,
          y: y - radius,
          name: 'top',
          id: `${id}_top`,
          type: 'source', // 只能作为连线的起点
        },
      ];
    }

    // 其他节点保持默认的四个锚点
    return [
      {
        x: x - width / 2,
        y,
        name: 'left',
        id: `${id}_left`,
      },
      {
        x: x + width / 2,
        y,
        name: 'right',
        id: `${id}_right`,
      },
      {
        x,
        y: y - height / 2,
        name: 'top',
        id: `${id}_top`,
      },
      {
        x,
        y: y + height / 2,
        name: 'bottom',
        id: `${id}_bottom`,
      },
    ];
  }
}

export const CustomHtmlPresetNodeDefinition = {
  type: 'CustomHtmlPresetNode',
  view: CustomHtmlPresetNode,
  model: CustomHtmlPresetNodeModel,
};

// CSS 样式
export const presetNodeStyles = `
/* 隐藏 HTML 节点的默认文本层 */
.lf-node[data-node-type="CustomHtmlPresetNode"] .lf-node-text {
  display: none !important;
}

.lf-node[data-node-type="CustomHtmlPresetNode"] .lf-node-text-auto-wrap {
  display: none !important;
}
.preset-node-wrapper {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.preset-node-container {
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
  width: 100%;
  height: 100%;
  border-radius: 12px;
  border: 2px solid #52c41a;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Cron 开始节点特殊样式 - 圆形 */
.preset-node-container.cron-start {
  background: linear-gradient(135deg, #fff2f0 0%, #fff7e6 100%);
  border: 3px solid #ff4d4f;
  border-radius: 50% !important;
  box-shadow: 0 3px 12px rgba(255, 77, 79, 0.2);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 0;
}

.preset-node-container.cron-start:hover {
  box-shadow: 0 6px 20px rgba(255, 77, 79, 0.3);
  transform: scale(1.02);
}

.preset-node-container.cron-start .preset-node-header {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  border-radius: 50%;
  width: calc(100% - 20px);
  height: 50px;
  padding: 8px;
  margin: 10px auto 5px auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.preset-node-container.cron-start .preset-node-body {
  padding: 0 15px 15px 15px;
  font-size: 9px;
  line-height: 1.1;
  max-height: 70px;
  overflow: hidden;
  width: calc(100% - 30px);
  box-sizing: border-box;
  text-align: center;
}

.preset-node-container.cron-start .node-title {
  font-size: 12px;
  font-weight: 700;
}

.preset-node-container.cron-start .node-icon {
  font-size: 18px;
  margin-right: 4px;
}

.preset-node-container.cron-start .config-item {
  margin-bottom: 1px;
  font-size: 8px;
  display: block;
  text-align: center;
}

.preset-node-container.cron-start .config-label {
  font-size: 7px;
  margin-right: 2px;
  color: #888;
}

.preset-node-container.cron-start .config-value {
  font-size: 8px;
  padding: 1px 2px;
  background: rgba(255, 77, 79, 0.1);
  border-radius: 2px;
  font-weight: 600;
  color: #d4380d;
}

/* HTTP 开始节点特殊样式 - 圆形 */
.preset-node-container.http-start {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  border: 3px solid #1890ff;
  border-radius: 50% !important;
  box-shadow: 0 3px 12px rgba(24, 144, 255, 0.2);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 0;
}

.preset-node-container.http-start:hover {
  box-shadow: 0 6px 20px rgba(24, 144, 255, 0.3);
  transform: scale(1.02);
}

.preset-node-container.http-start .preset-node-header {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border-radius: 50%;
  width: calc(100% - 20px);
  height: 50px;
  padding: 8px;
  margin: 10px auto 5px auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.preset-node-container.http-start .preset-node-body {
  padding: 0 15px 15px 15px;
  font-size: 9px;
  line-height: 1.1;
  max-height: 70px;
  overflow: hidden;
  width: calc(100% - 30px);
  box-sizing: border-box;
  text-align: center;
}

.preset-node-container.http-start .node-title {
  font-size: 12px;
  font-weight: 700;
}

.preset-node-container.http-start .node-icon {
  font-size: 18px;
  margin-right: 4px;
}

.preset-node-container.http-start .config-item {
  margin-bottom: 1px;
  font-size: 8px;
  display: block;
  text-align: center;
}

.preset-node-container.http-start .config-label {
  font-size: 7px;
  margin-right: 2px;
  color: #888;
}

.preset-node-container.http-start .config-value {
  font-size: 8px;
  padding: 1px 2px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 2px;
  font-weight: 600;
  color: #0958d9;
}

.preset-node-container:hover {
  box-shadow: 0 4px 16px rgba(82, 196, 26, 0.25);
  transform: translateY(-1px);
}

.preset-node-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: linear-gradient(90deg, #52c41a, #73d13d);
  color: white;
  font-weight: 600;
  font-size: 13px;
  position: relative;
}

.node-icon {
  font-size: 16px;
  margin-right: 6px;
}

.node-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.edit-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  color: white;
  cursor: pointer;
  padding: 2px 6px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.edit-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.edit-icon {
  font-size: 12px;
}

.preset-node-body {
  padding: 10px 12px;
  font-size: 11px;
  line-height: 1.4;
  max-height: 80px;
  overflow-y: auto;
}

.config-item {
  display: flex;
  margin-bottom: 4px;
  align-items: flex-start;
}

.config-label {
  color: #666;
  font-weight: 500;
  margin-right: 6px;
  min-width: 0;
  flex-shrink: 0;
}

.config-value {
  color: #333;
  font-weight: 600;
  background: #e6f7ff;
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 10px;
  word-break: break-all;
  flex: 1;
}

.no-config {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 8px 0;
}

.more-config {
  text-align: center;
  font-size: 10px;
  color: #666;
  padding: 4px 0;
  border-top: 1px solid #e8f5e8;
  background: rgba(82, 196, 26, 0.05);
}

/* 滚动条样式 */
.preset-node-body::-webkit-scrollbar {
  width: 3px;
}

.preset-node-body::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 2px;
}

.preset-node-body::-webkit-scrollbar-thumb {
  background: #52c41a;
  border-radius: 2px;
}

.preset-node-body::-webkit-scrollbar-thumb:hover {
  background: #389e0d;
}
`;
