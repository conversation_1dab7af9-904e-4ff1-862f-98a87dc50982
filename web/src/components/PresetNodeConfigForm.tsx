import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Button, Space, Radio } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { eventCategoryService } from '../services/api';

// 本地类型定义
interface FormField {
  key: string;
  label: string;
  type: 'input' | 'select' | 'textarea' | 'radio' | 'condition_array' | 'aggregate_array' | 'lark_array';
  required: boolean;
  defaultValue: any;
  options?: { label: string; value: string }[];
  placeholder?: string;
  validation?: Record<string, any>;
}

interface PresetNode {
  id: string;
  name: string;
  description: string;
  category: string;
  nodeType: string;
  icon: string;
  formFields: FormField[];
  style: {
    fill: string;
    stroke: string;
    strokeWidth: number;
    fontSize: number;
    fontColor: string;
    borderRadius?: number;
  };
}

const { Option } = Select;
const { TextArea } = Input;

// 动态选择组件
const DynamicSelect: React.FC<{
  field: FormField;
  placeholder?: string;
  value?: any;
  onChange?: (value: any) => void;
}> = ({ field, placeholder = '', value, onChange }) => {
  const [options, setOptions] = useState<{ label: string; value: string }[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadOptions = async () => {
      if (field.validation?.dynamicApi) {
        setLoading(true);
        try {
          // 根据不同的API路径调用对应的服务
          let response;
          if (field.validation.dynamicApi === '/event-categories/options') {
            response = await eventCategoryService.getCategoryOptions();
          } else {
            // 如果有其他动态API，可以在这里添加
            console.warn('Unknown dynamic API:', field.validation.dynamicApi);
            setOptions([]);
            return;
          }

          const apiOptions = response.data.data?.options || [];
          setOptions(apiOptions.map((opt: any) => ({
            label: opt.name || opt.label,
            value: opt.id || opt.value
          })));
        } catch (error) {
          console.error('Failed to load dynamic options:', error);
          setOptions([]);
        } finally {
          setLoading(false);
        }
      } else {
        setOptions(field.options || []);
      }
    };

    loadOptions();
  }, [field.validation?.dynamicApi, field.options]);

  return (
    <Select
      placeholder={placeholder}
      loading={loading}
      value={value}
      onChange={onChange}
    >
      {options.map(option => (
        <Option key={option.value} value={option.value}>
          {option.label}
        </Option>
      ))}
    </Select>
  );
};

// 条件数组输入组件
interface ConditionArrayInputProps {
  value?: any[];
  onChange?: (value: any[]) => void;
  operators: { label: string; value: string }[];
  placeholder?: string;
}

const ConditionArrayInput: React.FC<ConditionArrayInputProps> = ({
  value = [],
  onChange,
  operators
}) => {
  // 解析value的辅助函数
  const parseValue = (val: any): any[] => {
    if (typeof val === 'string') {
      try {
        const parsed = JSON.parse(val);
        return Array.isArray(parsed) ? parsed : [{ field: '', operator: 'equals', value: '' }];
      } catch (error) {
        return [{ field: '', operator: 'equals', value: '' }];
      }
    }
    if (Array.isArray(val) && val.length > 0) {
      return val;
    }
    return [{ field: '', operator: 'equals', value: '' }];
  };

  const [conditions, setConditions] = useState<any[]>(() => parseValue(value));

  // 当value prop变化时，同步更新内部状态
  useEffect(() => {
    const newConditions = parseValue(value);
    setConditions(newConditions);
  }, [value]);

  const updateConditions = (newConditions: any[]) => {
    setConditions(newConditions);
    // 直接传递数组类型
    onChange?.(newConditions);
  };

  const addCondition = () => {
    const newConditions = [...conditions, { field: '', operator: 'equals', value: '' }];
    updateConditions(newConditions);
  };

  const removeCondition = (index: number) => {
    const newConditions = conditions.filter((_, i) => i !== index);
    updateConditions(newConditions);
  };

  const updateCondition = (index: number, key: string, val: string) => {
    const newConditions = [...conditions];
    newConditions[index] = { ...newConditions[index], [key]: val };
    updateConditions(newConditions);
  };

  return (
    <div style={{ border: '1px dashed #d9d9d9', borderRadius: 6, padding: 16 }}>
      {conditions.map((condition, index) => (
        <div key={index} style={{ marginBottom: index < conditions.length - 1 ? 12 : 0 }}>
          <Space.Compact style={{ display: 'flex', width: '100%' }}>
            <Input
              placeholder="字段名"
              value={condition.field}
              onChange={(e) => updateCondition(index, 'field', e.target.value)}
              style={{ flex: 1 }}
            />
            <Select
              placeholder="比较符"
              value={condition.operator}
              onChange={(val) => updateCondition(index, 'operator', val)}
              style={{ width: 120 }}
            >
              {operators.map(op => (
                <Option key={op.value} value={op.value}>{op.label}</Option>
              ))}
            </Select>
            <Input
              placeholder="比较值"
              value={condition.value}
              onChange={(e) => updateCondition(index, 'value', e.target.value)}
              style={{ flex: 1 }}
            />
            {conditions.length > 1 && (
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => removeCondition(index)}
                style={{ flexShrink: 0 }}
              />
            )}
          </Space.Compact>
        </div>
      ))}

      <Button
        type="dashed"
        onClick={addCondition}
        icon={<PlusOutlined />}
        style={{ width: '100%', marginTop: 12 }}
      >
        添加条件
      </Button>
    </div>
  );
};

// 聚合数组输入组件
interface AggregateArrayInputProps {
  value?: any[];
  onChange?: (value: any[]) => void;
  aggregateFunctions: { label: string; value: string }[];
  placeholder?: string;
}

const AggregateArrayInput: React.FC<AggregateArrayInputProps> = ({
  value = [],
  onChange,
  aggregateFunctions
}) => {
  // 解析value的辅助函数
  const parseAggregateValue = (val: any): any[] => {
    if (typeof val === 'string') {
      try {
        const parsed = JSON.parse(val);
        return Array.isArray(parsed) ? parsed : [{ fieldName: '', aggregateFunc: 'count', outputName: '' }];
      } catch (error) {
        return [{ fieldName: '', aggregateFunc: 'count', outputName: '' }];
      }
    }
    if (Array.isArray(val) && val.length > 0) {
      return val;
    }
    return [{ fieldName: '', aggregateFunc: 'count', outputName: '' }];
  };

  const [aggregateFields, setAggregateFields] = useState<any[]>(() => parseAggregateValue(value));

  // 当value prop变化时，同步更新内部状态
  useEffect(() => {
    const newFields = parseAggregateValue(value);
    setAggregateFields(newFields);
  }, [value]);

  const updateAggregateFields = (newFields: any[]) => {
    setAggregateFields(newFields);
    // 直接传递数组类型
    onChange?.(newFields);
  };

  const addAggregateField = () => {
    const newFields = [...aggregateFields, { fieldName: '', aggregateFunc: 'count', outputName: '' }];
    updateAggregateFields(newFields);
  };

  const removeAggregateField = (index: number) => {
    const newFields = aggregateFields.filter((_, i) => i !== index);
    updateAggregateFields(newFields);
  };

  const updateAggregateField = (index: number, key: string, val: string) => {
    const newFields = [...aggregateFields];
    newFields[index] = { ...newFields[index], [key]: val };
    updateAggregateFields(newFields);
  };

  return (
    <div style={{ border: '1px dashed #d9d9d9', borderRadius: 6, padding: 16 }}>
      {aggregateFields.map((field, index) => (
        <div key={index} style={{ marginBottom: index < aggregateFields.length - 1 ? 12 : 0 }}>
          <Space.Compact style={{ display: 'flex', width: '100%' }}>
            <Input
              placeholder="字段名 (count函数可为空)"
              value={field.fieldName}
              onChange={(e) => updateAggregateField(index, 'fieldName', e.target.value)}
              style={{ flex: 1 }}
            />
            <Select
              placeholder="聚合函数"
              value={field.aggregateFunc}
              onChange={(val) => updateAggregateField(index, 'aggregateFunc', val)}
              style={{ width: 140 }}
            >
              {aggregateFunctions.map(func => (
                <Option key={func.value} value={func.value}>{func.label}</Option>
              ))}
            </Select>
            <Input
              placeholder="输出字段名"
              value={field.outputName}
              onChange={(e) => updateAggregateField(index, 'outputName', e.target.value)}
              style={{ flex: 1 }}
            />
            {aggregateFields.length > 1 && (
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => removeAggregateField(index)}
                style={{ flexShrink: 0 }}
              />
            )}
          </Space.Compact>
        </div>
      ))}

      <Button
        type="dashed"
        onClick={addAggregateField}
        icon={<PlusOutlined />}
        style={{ width: '100%', marginTop: 12 }}
      >
        添加聚合字段
      </Button>
    </div>
  );
};

// Lark数组输入组件
interface LarkArrayInputProps {
  value?: any[];
  onChange?: (value: any[]) => void;
  contentTypes: { label: string; value: string; extra?: Record<string, any> }[];
  placeholder?: string;
}

const LarkArrayInput: React.FC<LarkArrayInputProps> = ({
  value = [],
  onChange,
  contentTypes
}) => {
  // 解析value的辅助函数
  const parseLarkValue = (val: any): any[] => {
    if (typeof val === 'string') {
      try {
        const parsed = JSON.parse(val);
        return Array.isArray(parsed) ? parsed : [{ contentType: '', contentText: '' }];
      } catch (error) {
        return [{ contentType: '', contentText: '' }];
      }
    }
    if (Array.isArray(val) && val.length > 0) {
      return val;
    }
    return [{ contentType: '', contentText: '' }];
  };

  const [larkFields, setLarkFields] = useState<any[]>(() => parseLarkValue(value));

  // 当value prop变化时，同步更新内部状态
  useEffect(() => {
    const newFields = parseLarkValue(value);
    setLarkFields(newFields);
  }, [value]);

  const updateLarkFields = (newFields: any[]) => {
    setLarkFields(newFields);
    // 直接传递数组类型
    onChange?.(newFields);
  };

  const addLarkField = () => {
    const newFields = [...larkFields, { contentType: '', contentText: '' }];
    updateLarkFields(newFields);
  };

  const removeLarkField = (index: number) => {
    const newFields = larkFields.filter((_, i) => i !== index);
    updateLarkFields(newFields);
  };

  const updateLarkField = (index: number, key: string, val: string) => {
    const newFields = [...larkFields];
    newFields[index] = { ...newFields[index], [key]: val };
    updateLarkFields(newFields);
  };

  return (
    <div style={{ border: '1px dashed #d9d9d9', borderRadius: 6, padding: 16 }}>
      {larkFields.map((field, index) => (
        <div key={index} style={{ marginBottom: index < larkFields.length - 1 ? 12 : 0 }}>
          <Space.Compact style={{ display: 'flex', width: '100%' }}>
            <Select
              placeholder="内容类型"
              value={field.contentType}
              onChange={(val) => updateLarkField(index, 'contentType', val)}
              style={{ width: 220 }} // 选择框加长
            >
              {contentTypes.map(type => (
                <Option key={type.value} value={type.value}>{type.label}</Option>
              ))}
            </Select>
            {/* 根据后端配置的inputType决定是否显示textarea，兼容旧数据 */}
            {(() => {
              const selectedType = contentTypes.find(type => type.value === field.contentType);

              // 兼容性处理：如果没有 extra 配置，使用旧的硬编码逻辑
              if (!selectedType?.extra?.inputType) {
                // 兼容旧数据：text, custom, table 显示 textarea
                return ['text', 'custom', 'table'].includes(field.contentType);
              }

              // 新逻辑：根据后端配置的 inputType 决定
              const inputType = selectedType.extra.inputType;
              return inputType === 'textarea';
            })() && (
              <TextArea
                placeholder="请输入内容"
                value={field.contentText}
                onChange={e => updateLarkField(index, 'contentText', e.target.value)}
                rows={4}
                autoSize={{ minRows: 4, maxRows: 8 }}
                style={{ flex: 1, marginLeft: 8 }} // 输入框缩短且加高
              />
            )}
            {larkFields.length > 1 && (
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => removeLarkField(index)}
                style={{ flexShrink: 0, marginLeft: 8 }}
              />
            )}
          </Space.Compact>
        </div>
      ))}

      <Button
        type="dashed"
        onClick={addLarkField}
        icon={<PlusOutlined />}
        style={{ width: '100%', marginTop: 12 }}
      >
        添加内容项
      </Button>
    </div>
  );
};

interface PresetNodeConfigFormProps {
  node: PresetNode | null;
  onCancel: () => void;
  onSubmit: (formData: Record<string, any>) => void;
  initialValues?: Record<string, any>;
}

const PresetNodeConfigForm: React.FC<PresetNodeConfigFormProps> = ({
  node,
  onCancel,
  onSubmit,
  initialValues,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (node) {
      // 设置表单值，优先使用传入的初始值，否则使用默认值
      const formValues: Record<string, any> = {};
      node.formFields.forEach(field => {
        formValues[field.key] = initialValues?.[field.key] ?? field.defaultValue;
      });
      form.setFieldsValue(formValues);
    }
  }, [node, form, initialValues]);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      onSubmit(values);
      form.resetFields();
    } catch (error) {
      console.error('Form validation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderFormField = (field: FormField) => {
    // 构建验证规则
    const rules: any[] = [
      {
        required: field.required,
        message: `请${field.type === 'select' ? '选择' : '输入'}${field.label}`,
      },
    ];

    // 处理数字类型验证
    if (field.validation?.type === 'number') {
      rules.push({
        validator: (_: any, value: any) => {
          if (value === undefined || value === null || value === '') {
            return Promise.resolve(); // 空值由 required 规则处理
          }

          const num = Number(value);

          // 检查是否为有效数字
          if (isNaN(num)) {
            return Promise.reject(new Error(`${field.label}必须是有效数字`));
          }

          // 检查最小值
          if (field.validation?.min !== undefined && num < field.validation.min) {
            return Promise.reject(new Error(`${field.label}不能小于${field.validation.min}`));
          }

          // 检查最大值
          if (field.validation?.max !== undefined && num > field.validation.max) {
            return Promise.reject(new Error(`${field.label}不能大于${field.validation.max}`));
          }

          return Promise.resolve();
        },
      });
    }

    const commonProps = {
      key: field.key,
      name: field.key,
      label: field.label,
      rules,
    };

    switch (field.type) {
      case 'input':
        return (
          <Form.Item {...commonProps}>
            <Input
              placeholder={field.placeholder}
              type={field.validation?.type === 'number' ? 'number' : 'text'}
            />
          </Form.Item>
        );

      case 'select':
        return (
          <Form.Item {...commonProps}>
            <DynamicSelect
              field={field}
              placeholder={field.placeholder || `请选择${field.label}`}
            />
          </Form.Item>
        );

      case 'textarea':
        return (
          <Form.Item {...commonProps}>
            <TextArea
              placeholder={field.placeholder}
              rows={3}
              autoSize={{ minRows: 2, maxRows: 6 }}
            />
          </Form.Item>
        );

      case 'radio':
        return (
          <Form.Item {...commonProps}>
            <Radio.Group>
              {field.options?.map(opt => (
                <Radio key={opt.value} value={opt.value}>{opt.label}</Radio>
              ))}
            </Radio.Group>
          </Form.Item>
        );

      case 'condition_array':
        return (
          <Form.Item {...commonProps}>
            <ConditionArrayInput
              operators={field.validation?.operators || []}
              placeholder={field.placeholder}
            />
          </Form.Item>
        );

      case 'aggregate_array':
        return (
          <Form.Item {...commonProps}>
            <AggregateArrayInput
              aggregateFunctions={field.validation?.aggregateFunctions || []}
              placeholder={field.placeholder}
            />
          </Form.Item>
        );

      case 'lark_array':
        return (
          <Form.Item {...commonProps}>
            <LarkArrayInput
              contentTypes={field.validation?.contentTypes || []}
              placeholder={field.placeholder}
            />
          </Form.Item>
        )

      default:
        return null;
    }
  };

  if (!node) return null;

  return (
    <>
      <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
        <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{node.name}</div>
        <div style={{ fontSize: 12, color: '#666' }}>{node.description}</div>
      </div>

      <Form
        form={form}
        layout="vertical"
        autoComplete="off"
      >
        {node.formFields.map(field => renderFormField(field))}
      </Form>

      <div style={{ marginTop: 24, textAlign: 'right' }}>
        <Button onClick={onCancel} style={{ marginRight: 8 }}>
          取消
        </Button>
        <Button type="primary" loading={loading} onClick={handleSubmit}>
          确定
        </Button>
      </div>
    </>
  );
};

export default PresetNodeConfigForm;
