import { RectNode, RectNodeModel, h } from '@logicflow/core';

// 自定义预置节点类
class CustomPresetNode extends RectNode {
  static extendKey = 'CustomPresetNode';

  getShape() {
    const { model } = this.props;
    const { x, y, width, height, radius } = model;
    const style = model.getNodeStyle();

    return h('g', {}, [
      // 主节点矩形
      h('rect', {
        ...style,
        x: x - width / 2,
        y: y - height / 2,
        rx: radius,
        ry: radius,
        width,
        height,
      }),
      // 节点标题
      h('text', {
        fill: style.color,
        fontSize: 13,
        fontWeight: 'bold',
        x,
        y: y - height / 2 + 18,
        textAnchor: 'middle',
        className: 'lf-node-title',
      }, model.properties?.presetNodeName || model.text?.value || model.text || ''),
      // 分隔线
      h('line', {
        x1: x - width / 2 + 10,
        y1: y - height / 2 + 25,
        x2: x + width / 2 - 10,
        y2: y - height / 2 + 25,
        stroke: '#d9d9d9',
        strokeWidth: 1,
      }),
      // 配置信息显示
      this.getConfigText(model, x, y, width, height, style),
      // 编辑按钮
      this.getEditButton(model, x, y, width, height),
    ]);
  }

  getConfigText(model: any, x: number, y: number, _width: number, _height: number, _style: any) {
    const properties = model.properties || {};
    const config = properties.formData || {};

    if (!config || Object.keys(config).length === 0) {
      return h('text', {
        fill: '#999',
        fontSize: 10,
        x,
        y: y - 5,
        textAnchor: 'middle',
        className: 'lf-node-config-text',
      }, '暂无配置');
    }

    const configLines = Object.entries(config)
      .slice(0, 2) // 只显示前2个配置项
      .map(([key, value], index) => {
        // 获取字段的显示标签
        const displayValue = String(value).length > 12 ? String(value).substring(0, 12) + '...' : value;
        return h('text', {
          fill: '#666',
          fontSize: 10,
          x,
          y: y - 10 + (index * 14),
          textAnchor: 'middle',
          className: 'lf-node-config-text',
        }, `${key}: ${displayValue}`);
      });

    return h('g', {}, configLines);
  }

  getEditButton(model: any, x: number, y: number, width: number, height: number) {
    const buttonWidth = 50;
    const buttonHeight = 20;
    const buttonX = x + width / 2 - buttonWidth - 5;
    const buttonY = y + height / 2 - buttonHeight - 5;

    return h('g', {
      className: 'lf-node-edit-button',
      style: 'cursor: pointer;',
      onClick: (e: Event) => {
        e.stopPropagation();
        // 触发自定义编辑事件
        const event = new CustomEvent('node-edit', {
          detail: { nodeId: model.id, nodeData: model }
        });
        window.dispatchEvent(event);
      }
    }, [
      // 按钮背景
      h('rect', {
        x: buttonX,
        y: buttonY,
        width: buttonWidth,
        height: buttonHeight,
        rx: 3,
        ry: 3,
        fill: '#1890ff',
        stroke: '#1890ff',
        strokeWidth: 1,
        opacity: 0.9,
      }),
      // 按钮文字
      h('text', {
        x: buttonX + buttonWidth / 2,
        y: buttonY + buttonHeight / 2 + 3,
        textAnchor: 'middle',
        fill: 'white',
        fontSize: 10,
        fontWeight: 'normal',
      }, '编辑'),
    ]);
  }
}

class CustomPresetNodeModel extends RectNodeModel {
  static extendKey = 'CustomPresetNode';

  initNodeData(data: any) {
    super.initNodeData(data);
    this.width = 180;
    this.height = 100;
    this.radius = 8;
    // 禁用文本编辑
    this.text.editable = false;
  }

  getNodeStyle() {
    const style = super.getNodeStyle();
    const properties = this.properties || {};

    // 使用预置节点的样式
    if (properties.isPresetNode) {
      return {
        ...style,
        fill: '#f6ffed',
        stroke: '#52c41a',
        strokeWidth: 2,
        color: '#333',
        fontSize: 11,
      };
    }

    return style;
  }

  getDefaultAnchor() {
    const { width, height, x, y, id } = this;
    return [
      {
        x: x - width / 2,
        y,
        name: 'left',
        id: `${id}_left`,
      },
      {
        x: x + width / 2,
        y,
        name: 'right',
        id: `${id}_right`,
      },
      {
        x,
        y: y - height / 2,
        name: 'top',
        id: `${id}_top`,
      },
      {
        x,
        y: y + height / 2,
        name: 'bottom',
        id: `${id}_bottom`,
      },
    ];
  }
}

export { CustomPresetNode, CustomPresetNodeModel };
