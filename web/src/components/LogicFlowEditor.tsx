import React, { useEffect, useRef, useState } from 'react';
import LogicFlow from '@logicflow/core';
import { Button, Space, message, Modal, Input } from 'antd';
import {
  PlayCircleOutlined,
  SaveOutlined,
  FolderOpenOutlined,
  ClearOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  ReloadOutlined,
  SwapOutlined
} from '@ant-design/icons';
import '@logicflow/core/lib/index.css';

// 演示数据
const getDemoData = (demoIndex: number) => {
  const demos = [
    // 演示1: 用户注册流程
    {
      name: '用户注册流程',
      data: {
        nodes: [
          {
            id: 'start',
            type: 'circle',
            x: 150,
            y: 80,
            text: '开始'
          },
          {
            id: 'input',
            type: 'rect',
            x: 150,
            y: 180,
            text: '填写信息'
          },
          {
            id: 'validate',
            type: 'diamond',
            x: 150,
            y: 300,
            text: '验证'
          },
          {
            id: 'success',
            type: 'circle',
            x: 150,
            y: 420,
            text: '成功'
          }
        ],
        edges: [
          {
            id: 'e1',
            sourceNodeId: 'start',
            targetNodeId: 'input',
            type: 'polyline'
          },
          {
            id: 'e2',
            sourceNodeId: 'input',
            targetNodeId: 'validate',
            type: 'polyline'
          },
          {
            id: 'e3',
            sourceNodeId: 'validate',
            targetNodeId: 'success',
            type: 'polyline'
          }
        ]
      }
    },
    // 演示2: 订单处理流程
    {
      name: '订单处理流程',
      data: {
        nodes: [
          {
            id: 'order_start',
            type: 'circle',
            x: 100,
            y: 100,
            text: '接收订单'
          },
          {
            id: 'check_inventory',
            type: 'diamond',
            x: 250,
            y: 100,
            text: '检查库存'
          },
          {
            id: 'process_payment',
            type: 'rect',
            x: 250,
            y: 220,
            text: '处理支付'
          },
          {
            id: 'order_complete',
            type: 'circle',
            x: 250,
            y: 320,
            text: '订单完成'
          }
        ],
        edges: [
          {
            id: 'oe1',
            sourceNodeId: 'order_start',
            targetNodeId: 'check_inventory',
            type: 'polyline'
          },
          {
            id: 'oe2',
            sourceNodeId: 'check_inventory',
            targetNodeId: 'process_payment',
            type: 'polyline'
          },
          {
            id: 'oe3',
            sourceNodeId: 'process_payment',
            targetNodeId: 'order_complete',
            type: 'polyline'
          }
        ]
      }
    },
    // 演示3: 审批流程
    {
      name: '审批流程',
      data: {
        nodes: [
          {
            id: 'apply',
            type: 'circle',
            x: 150,
            y: 80,
            text: '提交申请'
          },
          {
            id: 'review',
            type: 'diamond',
            x: 150,
            y: 180,
            text: '审批'
          },
          {
            id: 'approved',
            type: 'circle',
            x: 150,
            y: 280,
            text: '通过'
          }
        ],
        edges: [
          {
            id: 'ae1',
            sourceNodeId: 'apply',
            targetNodeId: 'review',
            type: 'polyline'
          },
          {
            id: 'ae2',
            sourceNodeId: 'review',
            targetNodeId: 'approved',
            type: 'polyline'
          }
        ]
      }
    }
  ];

  return demos[demoIndex] || demos[0];
};

const LogicFlowEditor: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const lfRef = useRef<LogicFlow | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [jsonData, setJsonData] = useState('');
  const [currentDemo, setCurrentDemo] = useState(0);

  useEffect(() => {
    if (containerRef.current && !lfRef.current) {
      // 确保容器有足够的尺寸
      const containerWidth = containerRef.current.offsetWidth || 800;
      const containerHeight = containerRef.current.offsetHeight || 600;

      // 初始化LogicFlow - 使用简化配置
      const lf = new LogicFlow({
        container: containerRef.current,
        width: containerWidth,
        height: containerHeight,
        grid: {
          size: 20,
          visible: true,
        },
        keyboard: {
          enabled: true,
        }
      });

      lfRef.current = lf;

      // 延迟渲染，确保容器完全初始化
      setTimeout(() => {
        try {
          // 加载演示数据
          const demoData = getDemoData(currentDemo);
          console.log('渲染演示数据:', demoData.name);
          lf.render(demoData.data);

          // 自动适应画布大小
          setTimeout(() => {
            lf.fitView();
          }, 100);

          console.log('LogicFlow 初始化完成');
        } catch (error) {
          console.error('LogicFlow 渲染错误:', error);
        }
      }, 300);

      // 监听节点点击事件
      lf.on('node:click', ({ data }) => {
        console.log('节点被点击:', data);
        message.info(`点击了节点: ${data.text || data.id}`);
      });

      // 监听边点击事件
      lf.on('edge:click', ({ data }) => {
        console.log('边被点击:', data);
        message.info(`点击了连线: ${data.text || data.id}`);
      });
    }

    // 处理窗口大小变化
    const handleResize = () => {
      if (lfRef.current && containerRef.current) {
        lfRef.current.resize(
          containerRef.current.offsetWidth,
          containerRef.current.offsetHeight
        );
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 保存流程图数据
  const handleSave = () => {
    if (lfRef.current) {
      const data = lfRef.current.getGraphData();
      const jsonString = JSON.stringify(data, null, 2);
      setJsonData(jsonString);
      setIsModalVisible(true);
      message.success('流程图数据已生成！');
    }
  };

  // 加载流程图数据
  const handleLoad = () => {
    setJsonData('');
    setIsModalVisible(true);
  };

  // 确认加载数据
  const handleLoadConfirm = () => {
    try {
      const data = JSON.parse(jsonData);
      if (lfRef.current) {
        lfRef.current.render(data);
        message.success('流程图加载成功！');
      }
      setIsModalVisible(false);
    } catch (error) {
      message.error('JSON格式错误，请检查数据格式！');
    }
  };

  // 清空画布
  const handleClear = () => {
    if (lfRef.current) {
      lfRef.current.clearData();
      message.success('画布已清空！');
    }
  };

  // 放大
  const handleZoomIn = () => {
    if (lfRef.current) {
      lfRef.current.zoom(true);
    }
  };

  // 缩小
  const handleZoomOut = () => {
    if (lfRef.current) {
      lfRef.current.zoom(false);
    }
  };

  // 重置视图
  const handleResetZoom = () => {
    if (lfRef.current) {
      lfRef.current.resetZoom();
    }
  };

  // 切换演示
  const handleSwitchDemo = () => {
    const nextDemo = (currentDemo + 1) % 3; // 3个演示场景
    setCurrentDemo(nextDemo);
    if (lfRef.current) {
      const demoData = getDemoData(nextDemo);
      lfRef.current.render(demoData.data);
      // 自动适应画布大小
      setTimeout(() => {
        lfRef.current?.fitView();
      }, 100);
      message.success(`已切换到: ${demoData.name}`);
    }
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 工具栏 */}
      <div style={{ padding: '16px 0', borderBottom: '1px solid #f0f0f0' }}>
        <Space>
          <Button
            type="primary"
            icon={<SwapOutlined />}
            onClick={handleSwitchDemo}
          >
            切换演示 ({getDemoData(currentDemo).name})
          </Button>
          <Button
            icon={<PlayCircleOutlined />}
            onClick={() => message.info('执行流程图功能待实现')}
          >
            执行
          </Button>
          <Button
            icon={<SaveOutlined />}
            onClick={handleSave}
          >
            保存
          </Button>
          <Button
            icon={<FolderOpenOutlined />}
            onClick={handleLoad}
          >
            加载
          </Button>
          <Button
            icon={<ClearOutlined />}
            onClick={handleClear}
          >
            清空
          </Button>
          <Button
            icon={<ZoomInOutlined />}
            onClick={handleZoomIn}
          >
            放大
          </Button>
          <Button
            icon={<ZoomOutOutlined />}
            onClick={handleZoomOut}
          >
            缩小
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleResetZoom}
          >
            重置
          </Button>
        </Space>
      </div>

      {/* LogicFlow 容器 */}
      <div
        ref={containerRef}
        style={{
          flex: 1,
          minHeight: '500px',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          overflow: 'hidden',
          position: 'relative'
        }}
      />

      {/* 数据模态框 */}
      <Modal
        title="流程图数据"
        open={isModalVisible}
        onOk={handleLoadConfirm}
        onCancel={() => setIsModalVisible(false)}
        width={800}
        okText="加载数据"
        cancelText="取消"
      >
        <Input.TextArea
          value={jsonData}
          onChange={(e) => setJsonData(e.target.value)}
          placeholder="请输入或查看流程图的JSON数据..."
          rows={20}
          style={{ fontFamily: 'monospace' }}
        />
      </Modal>
    </div>
  );
};

export default LogicFlowEditor;
