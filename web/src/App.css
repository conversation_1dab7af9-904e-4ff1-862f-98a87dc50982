/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#root {
  width: 100%;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* LogicFlow 相关样式 */
.lf-canvas-overlay {
  background-color: #fafafa;
}

/* 确保LogicFlow容器正确显示 */
.lf-canvas {
  width: 100% !important;
  height: 100% !important;
}

.lf-graph {
  width: 100% !important;
  height: 100% !important;
}

/* 自定义节点样式 */
.lf-node-rect {
  fill: #e6f7ff;
  stroke: #1890ff;
  stroke-width: 2;
}

.lf-node-circle {
  fill: #f6ffed;
  stroke: #52c41a;
  stroke-width: 2;
}

.lf-node-diamond {
  fill: #fff7e6;
  stroke: #fa8c16;
  stroke-width: 2;
}

/* 连线样式 */
.lf-edge {
  stroke: #666;
  stroke-width: 2;
}

.lf-edge:hover {
  stroke: #1890ff;
  stroke-width: 3;
}

/* 文本样式 */
.lf-node-text {
  font-size: 12px;
  fill: #333;
  text-anchor: middle;
  dominant-baseline: middle;
}

/* 工具栏样式 */
.toolbar {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    padding: 8px;
  }

  .toolbar .ant-space {
    flex-wrap: wrap;
  }
}
