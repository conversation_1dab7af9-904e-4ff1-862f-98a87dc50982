import axios from 'axios';
import { config } from '../config';

const API_URL = config.apiBaseUrl;

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: config.request.timeout,
  withCredentials: config.request.withCredentials,
});

// 流程相关API
export const flowApi = {
  // 获取流程列表
  getFlows: (params?: { page?: number; pageSize?: number; status?: string; keyword?: string }) => 
    api.get('/flows', { params }),
  
  // 获取单个流程
  getFlow: (id: string) => 
    api.get(`/flows/${id}`),
  
  // 创建流程
  createFlow: (data: any) => 
    api.post('/flows', data),
  
  // 更新流程
  updateFlow: (id: string, data: any) =>
    api.put(`/flows/${id}`, data),

  // 更新流程状态
  updateFlowStatus: (id: string, status: string) =>
    api.put(`/flows/${id}`, { status }),
  
  // 删除流程
  deleteFlow: (id: string) => 
    api.delete(`/flows/${id}`),
  
  // 执行流程
  executeFlow: (id: string) => 
    api.post(`/flows/${id}/execute`),
};

// 模板相关API
export const templateApi = {
  // 获取模板列表
  getTemplates: (params?: { page?: number; pageSize?: number; keyword?: string }) => 
    api.get('/templates', { params }),
  
  // 获取单个模板
  getTemplate: (id: string) => 
    api.get(`/templates/${id}`),
};

// 执行记录相关API
export const executionApi = {
  // 获取执行记录列表
  getExecutions: (params?: { page?: number; pageSize?: number; flowId?: string }) =>
    api.get('/executions', { params }),

  // 获取执行记录详情
  getExecution: (id: string) =>
    api.get(`/executions/${id}`),

  // 获取指定流程的执行记录
  getFlowExecutions: (flowId: string, params?: { page?: number; pageSize?: number }) =>
    api.get(`/executions/flow/${flowId}`, { params }),

  // 删除指定流程的执行记录
  deleteFlowExecutions: (flowId: string) =>
    api.delete(`/executions/flow/${flowId}`),
};

// 用户相关API
export const userService = {
  // 获取用户列表
  getUsers: (params?: { page?: number; pageSize?: number; keyword?: string }) =>
    api.get('/users', { params }),

  // 获取单个用户
  getUser: (id: string) =>
    api.get(`/users/${id}`),

  // 创建用户
  createUser: (data: any) =>
    api.post('/users', data),

  // 更新用户
  updateUser: (id: string, data: any) =>
    api.put(`/users/${id}`, data),

  // 删除用户
  deleteUser: (id: string) =>
    api.delete(`/users/${id}`),

  // 获取用户权限
  getUserPermissions: (id?: string) =>
    id ? api.get(`/users/${id}/permissions`) : api.get('/users/permissions'),
};

// 事件分类相关API
export const eventCategoryService = {
  // 获取事件分类列表
  getCategories: (params?: { page?: number; pageSize?: number }) =>
    api.get('/event-categories', { params }),

  // 获取分类选项
  getCategoryOptions: () =>
    api.get('/event-categories/options'),

  // 获取单个事件分类
  getCategory: (id: string) =>
    api.get(`/event-categories/${id}`),

  // 创建事件分类
  createCategory: (data: any) =>
    api.post('/event-categories', data),

  // 更新事件分类
  updateCategory: (id: string, data: any) =>
    api.put(`/event-categories/${id}`, data),

  // 删除事件分类
  deleteCategory: (id: string) =>
    api.delete(`/event-categories/${id}`),

  // 批量删除事件分类
  batchDeleteCategories: (ids: string[]) =>
    api.post('/event-categories/batch-delete', { ids }),
};

// 事件运营相关API
export const eventOperationService = {
  // 获取事件运营列表
  getOperations: (params?: {
    page?: number;
    pageSize?: number;
    categoryId?: string;
    ownerId?: string;
    processType?: string;
    occurredStartTime?: string;
    occurredEndTime?: string;
    keyword?: string;
  }) =>
    api.get('/event-operations', { params }),

  // 获取处理分类选项
  getProcessTypeOptions: () =>
    api.get('/event-operations/process-types'),

  // 搜索员工
  searchEmployees: (keyword?: string, limit?: number) =>
    api.get('/event-operations/search-employees', { params: { keyword, limit } }),

  // 获取单个事件运营
  getOperation: (id: string) =>
    api.get(`/event-operations/${id}`),

  // 创建事件运营
  createOperation: (data: any) =>
    api.post('/event-operations', data),

  // 更新事件运营
  updateOperation: (id: string, data: any) =>
    api.put(`/event-operations/${id}`, data),

  // 删除事件运营
  deleteOperation: (id: string) =>
    api.delete(`/event-operations/${id}`),

  // 批量删除事件运营
  batchDeleteOperations: (ids: string[]) =>
    api.post('/event-operations/batch-delete', { ids }),

  // 批量更新事件运营
  batchUpdateOperations: (data: {
    ids: string[];
    processType?: string;
    processDescription?: string;
    processCompletedAt?: string;
  }) =>
    api.post('/event-operations/batch-update', data),
};

export default api;