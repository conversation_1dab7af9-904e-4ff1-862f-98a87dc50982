import axios from 'axios';
import type { PresetNode, PresetNodeInstance } from '../types/presetNode';
import { config } from '../config';

const API_URL = config.apiBaseUrl;

// API 方法
export const presetNodeApi = {
  // 获取预置节点列表
  getPresetNodes: async (category?: string): Promise<PresetNode[]> => {
    try {
      const params = category ? { category } : {};
      const response = await axios.get(`${API_URL}/preset-nodes`, { params });
      return response.data.data || [];
    } catch (error) {
      console.error('Failed to get preset nodes:', error);
      throw error;
    }
  },

  // 获取单个预置节点详情
  getPresetNode: async (id: string): Promise<PresetNode> => {
    try {
      const response = await axios.get(`${API_URL}/preset-nodes/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('Failed to get preset node:', error);
      throw error;
    }
  },

  // 创建预置节点实例
  createPresetNodeInstance: async (data: {
    presetNodeId: string;
    x: number;
    y: number;
    formData: Record<string, any>;
  }): Promise<PresetNodeInstance> => {
    try {
      const response = await axios.post(`${API_URL}/preset-nodes/instances`, data);
      return response.data.data;
    } catch (error) {
      console.error('Failed to create preset node instance:', error);
      throw error;
    }
  },
};
