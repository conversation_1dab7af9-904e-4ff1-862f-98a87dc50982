import axios from 'axios';
import { config } from '../config';

const API_URL = config.apiBaseUrl;

const authApi = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // 重要：发送Cookie
});

// 用户认证相关API
export const userAuthApi = {
  // 用户登录（仅需要用户名）
  login: (username: string) => authApi.post('/auth/login', { username }),
  
  // 用户登出
  logout: () => authApi.post('/auth/logout'),
  
  // 获取当前用户信息
  getProfile: () => authApi.get('/users/profile'),
  
  // 更新当前用户信息
  updateProfile: (data: any) => authApi.put('/users/profile', data),
  
  // 获取当前用户权限
  getPermissions: () => authApi.get('/users/permissions'),
};

// 用户管理相关API（需要管理员权限）
export const userMgmtApi = {
  // 获取用户列表
  getUsers: (params?: any) => authApi.get('/users', { params }),
  
  // 获取指定用户
  getUser: (id: string) => authApi.get(`/users/${id}`),
  
  // 创建用户
  createUser: (data: any) => authApi.post('/users', data),
  
  // 更新用户
  updateUser: (id: string, data: any) => authApi.put(`/users/${id}`, data),
  
  // 删除用户
  deleteUser: (id: string) => authApi.delete(`/users/${id}`),
  
  // 获取用户权限
  getUserPermissions: (id: string) => authApi.get(`/users/${id}/permissions`),
};

// 角色管理相关API
export const roleApi = {
  // 获取角色列表
  getRoles: (params?: any) => authApi.get('/roles', { params }),
  
  // 获取所有角色（下拉选择）
  getAllRoles: () => authApi.get('/roles/all'),
  
  // 获取单个角色
  getRole: (id: string) => authApi.get(`/roles/${id}`),
  
  // 创建角色
  createRole: (data: any) => authApi.post('/roles', data),
  
  // 更新角色
  updateRole: (id: string, data: any) => authApi.put(`/roles/${id}`, data),
  
  // 删除角色
  deleteRole: (id: string) => authApi.delete(`/roles/${id}`),
};

// 权限管理相关API
export const permissionApi = {
  // 获取权限列表
  getPermissions: (params?: any) => authApi.get('/permissions', { params }),
  
  // 获取所有权限（下拉选择）
  getAllPermissions: () => authApi.get('/permissions/all'),
  
  // 根据资源获取权限
  getPermissionsByResource: (resource: string) => 
    authApi.get('/permissions/by-resource', { params: { resource } }),
  
  // 获取单个权限
  getPermission: (id: string) => authApi.get(`/permissions/${id}`),
  
  // 创建权限
  createPermission: (data: any) => authApi.post('/permissions', data),
  
  // 更新权限
  updatePermission: (id: string, data: any) => authApi.put(`/permissions/${id}`, data),
  
  // 删除权限
  deletePermission: (id: string) => authApi.delete(`/permissions/${id}`),
};

export default authApi; 