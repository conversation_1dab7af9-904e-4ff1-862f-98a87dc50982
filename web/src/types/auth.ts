// 用户信息
export interface User {
  id: string;
  username: string;
  email?: string;
  avatar?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  roles: Role[];
}

// 角色信息
export interface Role {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  permissions: Permission[];
  users?: User[];
}

// 权限信息
export interface Permission {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  resource: string;
  action: string;
  createdAt: string;
  updatedAt: string;
  roles?: Role[];
}

// 用户及其权限信息
export interface UserWithPermissions {
  user: User;
  roles: Role[];
  permissions: Permission[];
}

// 创建用户请求
export interface CreateUserRequest {
  username: string;
  email?: string;
  avatar?: string;
  roleIds: string[];
}

// 更新用户请求
export interface UpdateUserRequest {
  username?: string;
  email?: string;
  avatar?: string;
  status?: string;
  roleIds?: string[];
}

// 更新用户资料请求
export interface UpdateProfileRequest {
  username?: string;
  email?: string;
  avatar?: string;
}

// 创建角色请求
export interface CreateRoleRequest {
  name: string;
  displayName: string;
  description?: string;
  isDefault?: boolean;
  permissionIds: string[];
}

// 更新角色请求
export interface UpdateRoleRequest {
  name?: string;
  displayName?: string;
  description?: string;
  isDefault?: boolean;
  permissionIds?: string[];
}

// 创建权限请求
export interface CreatePermissionRequest {
  name: string;
  displayName: string;
  description?: string;
  resource: string;
  action: string;
}

// 更新权限请求
export interface UpdatePermissionRequest {
  name?: string;
  displayName?: string;
  description?: string;
  resource?: string;
  action?: string;
}

// 用户上下文
export interface UserContext {
  user: User | null;
  permissions: string[];
  loading: boolean;
  hasPermission: (permission: string) => boolean;
  hasRole: (roleName: string) => boolean;
  refreshUser: () => Promise<void>;
}

// 列表请求参数
export interface ListRequest {
  page?: number;
  pageSize?: number;
  keyword?: string;
}

// 用户列表请求参数
export interface UserListRequest extends ListRequest {
  status?: string;
  roleId?: string;
}

// 角色列表请求参数
export interface RoleListRequest extends ListRequest {}

// 权限列表请求参数
export interface PermissionListRequest extends ListRequest {
  resource?: string;
  action?: string;
}

// 分页响应
export interface PaginatedResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
} 