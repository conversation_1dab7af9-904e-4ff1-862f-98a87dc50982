// 预置节点相关类型定义
export interface FormField {
  key: string;
  label: string;
  type: 'input' | 'select' | 'textarea' | 'condition_array' | 'aggregate_array' | 'lark_array';
  required: boolean;
  defaultValue: any;
  options?: { label: string; value: string; extra?: Record<string, any> }[];
  placeholder?: string;
  validation?: Record<string, any>;
}

export interface PresetNode {
  id: string;
  name: string;
  description: string;
  category: string;
  nodeType: string;
  icon: string;
  config: {
    width: number;
    height: number;
    resizable: boolean;
    deletable: boolean;
    editable: boolean;
    properties: Record<string, any>;
  };
  formFields: FormField[];
  style: {
    fill: string;
    stroke: string;
    strokeWidth: number;
    fontSize: number;
    fontColor: string;
    borderRadius?: number;
  };
}

export interface PresetNodeInstance {
  id: string;
  presetNodeId: string;
  x: number;
  y: number;
  formData: Record<string, any>;
  config: any;
  style: any;
}
