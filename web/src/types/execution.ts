// 执行记录相关类型定义

export type TriggerType = 'manual' | 'cron' | 'http';
export type ExecutionStatus = 'running' | 'completed' | 'failed' | 'cancelled';

export interface FlowExecutionListItem {
  id: string;
  flowId: string;
  flowName: string;
  triggerType: TriggerType;
  triggerSource: string;
  status: ExecutionStatus;
  startTime: string;
  endTime?: string;
  duration: number;
  errorMessage: string;
  createdAt: string;
}

export interface FlowExecutionStep {
  stepId: string;
  nodeId: string;
  nodeType: string;
  nodeName: string;
  status: string;
  startTime: number;
  endTime: number;
  duration: number;
  inputData: Record<string, any>;
  outputData: Record<string, any>;
  flowData: Record<string, any>;
  errorMessage?: string;
}

export interface FlowExecutionSummary {
  totalSteps: number;
  successSteps: number;
  failedSteps: number;
  skippedSteps: number;
  executionPath: string;
}

export interface FlowExecutionLog {
  executionId: string;
  steps: FlowExecutionStep[];
  nodeResults: Record<string, any>;
  summary: FlowExecutionSummary;
}

export interface FlowData {
  nodes: Array<{
    id: string;
    type: string;
    x: number;
    y: number;
    text: string;
    properties?: Record<string, any>;
  }>;
  edges: Array<{
    id: string;
    type: string;
    sourceNodeId: string;
    targetNodeId: string;
    text?: string;
    startPoint?: { x: number; y: number };
    endPoint?: { x: number; y: number };
    pointsList?: Array<{ x: number; y: number }>;
    properties?: Record<string, any>;
  }>;
}

export interface FlowExecutionDetail {
  id: string;
  flowId: string;
  flowName: string;
  triggerType: TriggerType;
  triggerSource: string;
  status: ExecutionStatus;
  startTime: string;
  endTime?: string;
  duration: number;
  errorMessage: string;
  createdAt: string;
  flowSnapshot: FlowData;
  inputData: Record<string, any>;
  outputData: Record<string, any>;
  executionLog: FlowExecutionLog;
}

export interface FlowWithLastExecution {
  id: string;
  name: string;
  executionTime: string;
  description: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  lastExecution?: FlowExecutionListItem;
}

// 触发类型显示名称映射
export const TriggerTypeDisplayNames: Record<TriggerType, string> = {
  manual: '手动触发',
  cron: '定时触发',
  http: 'HTTP触发',
};

// 执行状态显示名称映射
export const ExecutionStatusDisplayNames: Record<ExecutionStatus, string> = {
  running: '执行中',
  completed: '执行完成',
  failed: '执行失败',
  cancelled: '执行取消',
};

// 执行状态颜色映射
export const ExecutionStatusColors: Record<ExecutionStatus, string> = {
  running: 'blue',
  completed: 'green',
  failed: 'red',
  cancelled: 'gray',
};
