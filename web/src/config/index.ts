// 应用配置
export const config = {
  // API基础URL - 根据环境自动选择（优先使用环境变量，其次使用当前域名拼接）
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || `${window.location.origin}/sec-flow/api/v1`,
  
  // 应用基础配置
  app: {
    name: 'Sec Flow',
    version: '1.0.0',
  },
  
  // 开发环境配置
  dev: {
    enableDebug: import.meta.env.DEV,
    enableMockData: import.meta.env.VITE_ENABLE_MOCK === 'true',
  },
  
  // 请求配置
  request: {
    timeout: 30000, // 30秒超时
    withCredentials: true, // 支持Cookie
  },
};

// 导出API基础URL（向后兼容）
export const API_BASE_URL = config.apiBaseUrl;

// 环境检查
export const isDev = import.meta.env.DEV;
export const isProd = import.meta.env.PROD;

// 调试日志函数
export const debugLog = (...args: any[]) => {
  if (config.dev.enableDebug) {
    console.log('[DEBUG]', ...args);
  }
};
